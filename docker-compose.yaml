services:
  app: &defaults
    build:
      context: .
      dockerfile: docker/Dockerfile.dev
    volumes:
      - ./src:/app/src
      - /app/node_modules # Prevent overwriting node_modules
    ports:
      - '3000:3000'
    env_file:
      - .env
    depends_on:
      - db

  db:
    image: postgres:16
    container_name: db
    volumes:
      - db:/var/lib/postgresql/data # Mount the volume to persist the data
    ports:
      - '5432:5432'
    env_file: # Load environment variables from the .env file
      - .env

  # Test Layer
  ci:
    <<: *defaults
    build:
      context: .
      dockerfile: docker/funding-platform-api/Dockerfile
      target: shipment
    command: echo "Success"
    environment:
      - ENVIRONMENT=ci
    ports: []
    volumes:
      - .:/app
    working_dir: /app

volumes:
  db: # Define the volume to persist the PostgreSQL data
    driver: local
