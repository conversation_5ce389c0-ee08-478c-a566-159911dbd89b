<p align="center">
  <a href="https://hashgraph-association.com/" target="blank"><img src="https://github.com/AxLabs/tha-fp-backend/assets/37138571/2316f411-e1b4-4586-96cf-fad01d4b6644" alt="THA Logo" /></a>
</p>

# The HashGraph Association's Funding Platform Backend

🚀 Created using the [Nest](https://github.com/nestjs/nest) CLI, equipped with TypeORM, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and EsLint.

[![Feature ✨](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/actions/workflows/feature.yaml/badge.svg)](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/actions/workflows/feature.yaml)
[![Release 📦](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/actions/workflows/release.yaml/badge.svg)](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/actions/workflows/release.yaml)
[![Main 📦](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/actions/workflows/main.yaml/badge.svg)](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/actions/workflows/main.yaml)
[![Quality Gate Status](https://sonarqube.pub.mng.hashgraph-group.com/api/project_badges/measure?project=funding-platform-api&metric=alert_status&token=sqb_e3c8737b0ebb2fed8763d21fa8538ef981ae22f7)](https://sonarqube.pub.mng.hashgraph-group.com/dashboard?id=funding-platform-api)

## View documentation

To view the documentation, please add the asciidoc plugin to your IDE. Eaxmples:
### Intellij:
https://plugins.jetbrains.com/plugin/7391-asciidoc
### VS Code: 
https://marketplace.visualstudio.com/items?itemName=asciidoctor.asciidoctor-vscode
### Troubleshot if PlantUML diagrams are not displayed
https://github.com/asciidoctor/asciidoctor-intellij-plugin/issues/409

## 💻 Environment Setup

⚠ This project uses **yarn** as the package manager.

If you don't have it yet, install the Nest CLI with `npm i -g @nestjs/cli`. This will make the `nest` command available
globally.

All environment variables are documented in `.env.example`. For local development, copy and set them in a `.env` file in
the project directory.

## 🐳 Local Database

You can run a local PostgreSQL database for development using Docker Compose with the following command:

```bash
docker compose -f docker-compose.yml up -d
```

It will startup and configure a DB with the values set in the env vars `POSTGRES_DB`, `POSTGRES_USER` and
`POSTGRES_PASSWORD`. Meaning, the DB name, the user and password should already match your settings in the env vars.
The DB will be available at port 5432 on localhost.

## 🔗 Dependency Installation

To install the dependencies, run:

```bash
$ yarn install
```

## 🏃‍♂️ Running the Application

- **Development Mode:** Start the application in development mode using:

```bash
$ yarn start
```

- **Development Mode with Watch:** Start the application in development mode with file watching enabled:

```bash
$ yarn start:dev
```

This command will monitor your files for changes, automatically recompile them, and reload the server to reflect the
updates.

- **Production Mode:** Start the application in production mode using:

```bash
$ yarn build
$ yarn start
```

## 🧪 Testing

- **Unit Tests:** Execute unit tests using:

```bash
$ yarn test
```

- **End-to-End Tests (e2e):** Run end-to-end tests with:

```bash
$ yarn test:e2e
```

## 🧹 Linting and Formatting

- **Lint and format:** Run eslint and prettier to find code smells and automatically fix formatting issues:

```bash
$ yarn lint
```

- **Format:** To only run prettier and automatically apply formatting rules run:

```bash
$ yarn format
```

## Setting up User Roles

By default, all users are given the `USER` role upon signing up. In order for a user to be able to create or modify Grant Programs (and in turn assign Grant Call Coordinator, etc), they'd need to have the `GRANT_PROGRAM_COORDINATOR` role. This can be done by the following steps:

- Open the `tha-fp-db` database.
- Open the `user` table.
- Find the user you want to give permissions to and under the column named `role`, change the role from the default value - `USER` to `GRANT_PROGRAM_COORDINATOR`.
- Logout of the dApp.
- Disconnect the dApp from your wallet as well if you have to.
- Initiate a login to the dApp again.

This process will invalidate the existing session token with the `USER` role and will now have a session token with the `GRANT_PROGRAM_COORDINATOR` role instead.

## Database Migrations

This application use TypeORM for ORM. TypeORM includes functionality for DB migrations. Migrations can be executed
manually by connecting to a DB and running the appropriate scripts, or they can be ran automatically as part of the
application startup.

The `POSTGRES_MIGRATIONS_RUN` env variable controls if the application should run DB migrations at startup. It makes
sense to set it to `true` on deployed environments, such that when a new application version is pushed, the necessary DB
migrations are automatically run. The automatic migration takes the database configuration (e.g., connection string)
from the same place as the rest of the application, i.e., `database.module.ts`. There we specify where the migration
files are located and if the migration should be performed on startup.

For manual execution of DB migrations, the DB configuration has to be specified separately. This happens in
`./src/database/typeorm.config.ts`. That config file takes its values from the same environment variables as used in
`database.module.ts`. To execute pending migrations run `yarn typeorm:migrate`. To revert the last migration run `yarn
typeorm:revert`. Note that this only applies the revert script of one migration file at a time.

TypeORM stores information about which migrations have been applied to a DB in a dedicated table on that DB. The name of
that table is also configured on the data source in the `migrationsTableName` property.

Creating new migrations can be done by hand. Run the `yarn typeorm:create` command to create new empty migration file in
the `./src/database/migrations` directory. Then proceed to write the necessary queries for the migration. But,
migrations can also be generated automatically with TypeORM. Run the `yarn typeorm:generate` command, and TypeORM will
attempt to generate a migration file based on the differences between the entities in your code and the schema found on
the connected DB (configured in `./src/database/typeorm.config.ts`).

Finally, note that there is a `POSTGRES_SYNC` environment variable that controls if the database schema should
automatically be kept in sync with the entities in your code. This feature isn't about DB migration but just for
convenience during development. This must not be set to `true` in production, because it might lead to data
inconsistency and loss.

## 📃 License

Nest is [MIT licensed](LICENSE).
