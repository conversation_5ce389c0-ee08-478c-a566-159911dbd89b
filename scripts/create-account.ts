/**
 * This script creates a new Hedera account using a public key from AWS KMS.
 * The public key is retrieved from AWS KMS and converted to a Hedera compatible format.
 *
 * The script requires the following environment variables:
 * - AWS_KMS_REGION
 * - AWS_KMS_KEY_ID
 * - AWS_ACCESS_KEY_ID
 * - AWS_SECRET_ACCESS_KEY
 * - PRIVATE_KEY  # Private key of an account that will pay for the account creation
 * - ACCOUNT_ID   # Account ID of the account that will pay for the account creation
 */

import { AccountCreateTransaction, AccountId, Client, Hbar, PrivateKey, PublicKey } from '@hashgraph/sdk';
import { GetPublicKeyCommand, KMSClient } from '@aws-sdk/client-kms';

import { ec as EllipticCurve } from 'elliptic';
import { config } from 'dotenv';

config();

const getPublicKey = async () => {
  const kmsClient = new KMSClient({
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
    region: process.env.AWS_KMS_REGION,
  });

  const publicCommand = new GetPublicKeyCommand({
    KeyId: process.env.AWS_KMS_KEY_ID,
  });

  const response = await kmsClient.send(publicCommand);

  // Remove the DER ASN.1 object identifier prefix from the public key
  const hexPublicKey = Buffer.from(response.PublicKey)
    .toString('hex')
    .replace('3056301006072a8648ce3d020106052b8104000a034200', '');

  const ec = new EllipticCurve('secp256k1');

  // Instantiate public key objects
  const publicKeyCompressed = ec.keyFromPublic(hexPublicKey, 'hex').getPublic(true, 'hex');
  const newAccountPublicKey = PublicKey.fromBytesECDSA(Buffer.from(publicKeyCompressed, 'hex'));

  console.log('Hedera prefix publicKey:', newAccountPublicKey.toStringDer());

  return newAccountPublicKey;
};

async function main() {
  let client: Client;
  try {
    // Env variables not required in main app, should be added on demand for execution scripts
    const myAccountId = AccountId.fromString(process.env.ACCOUNT_ID);
    const myPrivateKey = PrivateKey.fromStringDer(process.env.PRIVATE_KEY);

    const accountPublicKey = await getPublicKey();

    client = Client.forTestnet().setOperator(myAccountId, myPrivateKey);

    const txCreateAccount = new AccountCreateTransaction()
      .setKeyWithoutAlias(accountPublicKey)
      .setInitialBalance(new Hbar(25));

    const txCreateAccountResponse = await txCreateAccount.execute(client);
    const receiptCreateAccountTx = await txCreateAccountResponse.getReceipt(client);
    const statusCreateAccountTx = receiptCreateAccountTx.status;
    const accountId = receiptCreateAccountTx.accountId;
    const txIdAccountCreated = txCreateAccountResponse.transactionId.toString();

    console.log('------------------------------ Create Account ------------------------------ ');
    console.log('Receipt status       :', statusCreateAccountTx.toString());
    console.log('Transaction ID       :', txIdAccountCreated);
    console.log('Hashscan URL         :', `https://hashscan.io/testnet/tx/${txIdAccountCreated}`);
    console.log('Account ID           :', accountId.toString());
  } catch (error) {
    console.error(error);
  } finally {
    if (client) {
      client.close();
    }
  }
}

main();
