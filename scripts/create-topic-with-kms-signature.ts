import * as dotenv from 'dotenv';

import { AppModule } from '../src/app.module';
import { HederaService } from '../src/hedera/hedera.service';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';

// Load environment variables from .env file
dotenv.config();

// Check required environment variables
function checkRequiredEnvVars() {
  const requiredVars = ['HEDERA_ACCOUNT_ID', 'HEDERA_OPERATOR_PUBLIC_KEY_DER', 'MIRROR_NODE_URL'];

  const missing = requiredVars.filter((varName) => !process.env[varName]);

  if (missing.length > 0) {
    console.error('Missing required environment variables:');
    missing.forEach((varName) => console.error(`- ${varName}`));
    console.error('\nPlease add these variables to your .env file or environment before running this script.');
    process.exit(1);
  }
}

async function bootstrap() {
  // Enable debug logging
  process.env.LOG_LEVEL = 'debug';

  const logger = new Logger('CreateTopicScript');
  logger.log('Starting NestJS application to test KMS signature with Hedera topic creation');

  // Check environment variables first
  checkRequiredEnvVars();

  try {
    logger.log('Initializing NestJS application...');
    const app = await NestFactory.createApplicationContext(AppModule);

    // Get the HederaService from the application context
    const hederaService = app.get(HederaService);

    logger.log('Attempting to create a Hedera topic with KMS signature...');
    const topicId = await hederaService.createTopic();

    logger.log(`Successfully created Hedera topic with ID: ${topicId}`);
    logger.log('KMS signature is working correctly!');

    await app.close();
  } catch (error) {
    logger.error('Error occurred while testing KMS signature:');
    logger.error(`Error type: ${error.constructor.name}`);
    logger.error(`Error message: ${error.message}`);

    if (error.stack) {
      logger.error('Stack trace:');
      logger.error(error.stack);
    }

    process.exit(1);
  }
}

bootstrap()
  .then(() => {
    process.exit(0);
  })
  .catch((error) => {
    console.error('Unhandled error in bootstrap:', error);
    process.exit(1);
  });
