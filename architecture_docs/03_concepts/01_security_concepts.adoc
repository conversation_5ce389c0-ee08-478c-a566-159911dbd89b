= Security Concept

[NOTE]
====
Add any security related decisions. Here are a few examples

- All external communication is encrypted using TLS 1.3
- API authentication uses OAuth 2.0 with JWT tokens
- Sensitive data in the database is encrypted at rest
- Regular security audits and penetration testing are conducted
====

=== AWS <<../01_introduction_and_goals/06_glossary.adoc#KMS, KMS>> and Transaction signing

- All transactions requiring platform signature are signed using AWS KMS.
- Private key for signing is persisted in AWS KMS, while public key is assigned to operator account.
- Private key is of ECDSA type, using secp256k1 curve.

[plantuml]
----
@startuml

participant gmp as "Grant Management Platform"
participant hn as "Hedera Network"
participant kms as "AWS KMS"

gmp -> gmp: Create new transaction
loop for each node chosen by client
    gmp -> kms: Request signature for created transaction for chosen node
    kms --> gmp: Return signature
    gmp -> gmp: Add signature to transaction
end
gmp -> hn: Submit transaction
hn --> gmp: Return transaction receipt

@enduml
----

