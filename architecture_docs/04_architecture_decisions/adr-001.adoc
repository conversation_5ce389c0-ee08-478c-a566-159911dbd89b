== ADR: User profiles will be handled via HCS

[cols="1, 1,4,1", id=changelog, options="header"]
|===
|Version |Status |Next steps |Last updated
|0.1 |Decided |We need to specify the events we want to log for each user |16.02.2025
|===

=== Context
During the workshop in Pfäffikon on the 11.02.2025 we were trying to tackle the issue of non-transparent user actions. This is why we came to the conclusion that we should log the users actions on a topic for the user.

=== Option for storing the users actions in a decentralized way

The following diagram visualizes the components and interactions for creating the users profile.

[plantuml, target=images/fe_component, format=svg]
----
@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

LAYOUT_TOP_DOWN()


Person_Ext(user, "Application user", "The user of the platform")
Component_Ext(sms_auth, "SMS Verification service", "Verifies the users mobile number.")
Component_Ext(wallet, "User wallet", "The users self-custody wallet.")
Component(frontend, "HGMP Frontend", "The frontend of the Hedera Managed Grant Platform.")

System_Boundary(hcs, "Hedera Consensus Service"){
    Component_Ext(topic, "User topic", "The user specific topic.")
}

System_Boundary(backend, "HGMP Backend", "The backend of the Hedera Managed Grant Platform."){
    Component(database_connector, "Database Connector", "Allows for the creation and maintenance of grant programs.")
    Component(hedera_service, "Hedera Service", "Allows for the interaction with the different Hedera services.")
}

System_Boundary(aws, "Amazon Web Services") {
    Component_Ext(rds_postgres, "RDS_PostgreSQL", "Stores the user profile.")
}

Rel(user, wallet, "Owns")
Rel(user, frontend, "Interacts with application")
Rel(backend, sms_auth, "Verifies his phone number via")
Rel(frontend, backend, "Access for services")
Rel(database_connector, rds_postgres, "Stores users topic id")
Rel(hedera_service, topic, "Creates a topic per user")
Rel(hedera_service, topic, "Writes new events to topic")
Rel(hedera_service, topic, "Queries information from topic")

@enduml
----

=== Events overview

[cols="1, 1,4", id=stakeholders, options="header"]
|===
|Event Id |Name |Description
|E1 |Phone number |The message with Id 0 will always be the hashed phone number to ensure that we are not allowing somebody to sign up twice.
|E2 |tbd |tbd
|===