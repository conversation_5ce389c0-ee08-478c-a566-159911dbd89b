== ADR: Platform private key / wallet

[cols="1, 1,4,1", id=changelog, options="header"]
|===
|Version |Status |Next steps |Last updated
|0.1 |Open |To be decided |03.03.2025
|===

=== Context
The grant management platform is required to interact with the Hedera ecosystem itself to create the topics as stated within ADR-002. This section describes how the private key is stored and how it is used to sign the different transactions of the platform.

=== Wallet Balance
Whenever we sign a transaction with the private key of the platforms wallet, we will also check the balance of it. If the balance is below:
- 100 Hbar a warning email gets send once to the admins team mailbox that the wallet needs to be topped up
- 50 Hbar an alert email gets send once to the admins team mailbox that the wallet needs to be topped up
- 25 Hbar a critical alert email gets send once to the admins team mailbox that the wallet needs to be topped up
The admins then ask the Finance Department to top up the wallet again to 250 Hbar.

=== Storing the key and signing
We will keep the single platforms wallet private key within the AWS KMS and sign it by using its https://docs.aws.amazon.com/kms/latest/APIReference/API_Sign.html[API service].