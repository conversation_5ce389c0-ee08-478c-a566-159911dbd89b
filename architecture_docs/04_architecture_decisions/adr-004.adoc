== ADR: User profile stored as DID

[cols="1, 1,4,1", id=changelog, options="header"]
|===
|Version |Status |Next steps |Last updated
|0.1 |Decided |- |20.02.2025
|===

=== Context
We currently are using the database of the application to:
[loweralpha,start=1]
. validate the credentials of an user
. authenticate an user
. authorize an user based on the assigned roles

As the platform is designed to work as a decentralized app (users are logging on via wallet connection) we should also adjust the identity and access management (IAM) approach accordingly. The team at the Hashgraph Identity Platform is working in parallel on a solution for https://hashgraph.atlassian.net/wiki/spaces/HIP/pages/248545315/Decentralized+Single+Sign-On+dSSO+-+Technical+Documentation[decentralized Single Sign On], which we would aim to leverage.

=== Overview
The following displays the way the IAM can be handled via DIDs.

// tag::architect[]
[plantuml,target=images/deployment,format=svg]
----

@startuml
!define C4_CONTEXT
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

LAYOUT_WITH_LEGEND()

title Decentralized SSO

Person_Ext(usr, "Platform user", "User of the platform")

System_Boundary(user, "Platform User", "Assets owned by platform user"){
    System_Ext(wallet, "User wallet", "Identity wallet of the user.")
    System_Ext(mobile, "Mobile number", "Mobile number of the individual.")
}

System_Ext(sms, "SMS Authenticator", "Authenticator service for mobile numbers.")

System_Boundary(gmp, "Grant management platform", "The private key and did document of the grant management platform"){
System(platform_key, "Platform Private Key", "Private key of the grant management platform it keeps under self custody.")
System(platform_did, "Platform DID", "The DID document of the grant management platform.")
}

System_Boundary(did_docs, "Platform User DID documents", "The did documents and associated verifiable credentials of a platform user"){
    System_Ext(did, "User DID Document", "The DID document of the user which he needs to create himself.")
    System_Ext(kyc_vc, "KYC VC", "VC holding proof of KYC of the user.")
    System_Ext(vote_vc, "Vote VC", "VC holding proof of a specific vote the user made.")
}

Rel(usr, wallet, "Holds keypair")
Rel(usr, mobile, "Owns")

Rel(sms, mobile, "Verified")

Rel(wallet, did_docs, "Associated")
Rel(wallet, did, "Created")
Rel(did, kyc_vc, "Associated")
Rel(did, vote_vc, "Associated")

Rel(platform_key, platform_did, "Created")
Rel(platform_did, kyc_vc, "Signed and created for user")
Rel(platform_did, vote_vc, "Signed and created for user")
@enduml
----

=== Work to be done
[cols="1, 2, 4", id=changelog, options="header"]
|===
|# |Work item |Description
|1 |Identity wallet |The Grant Managament Platform will need to implement the Identity Wallet interface which is being developped by Blade Labs.
|2 |DID creation workflow |There are frontend and backend sides to this. We would need to ask users on the FE if they already have a DID. If yes, they could link the DID to their user profile - if not, we will need to start the process to create a DID form them.
|3 |Integrate the Identity Platform SSO |In order to authenticate users we need to implement the dSSO solution.
|4 |Review onbaording process |The onboarding process will need to grant Verifiable Credentials to the users. This needs to be implemented into the flow.
|5 |Define DID schemas |The DID fields to be used and the schema for the different VCs need to be defined.
|===