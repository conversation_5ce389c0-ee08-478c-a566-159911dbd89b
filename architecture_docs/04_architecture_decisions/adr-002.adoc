== ADR: Share actions on grant applications publicly

[cols="1, 1,4,1", id=changelog, options="header"]
|===
|Version |Status |Next steps |Last updated
|0.1 |Decided |- |18.02.2025
|===

=== Context
During a meeting on 18.02.2025 we decided that we want to display all actions of an internal user on a grant application within a topic that is linked to the grant application. On the other side, we need to transparently hold all votes that happened on the application on another topic, to not mix the actions of internal and external users (voters).

=== Solution

Each grant application will be linked to two topcis:

. a topic for all the actions an internal user did on the grant application (only the platform private key can submit messages to it)
. a topic for all the votes that users gave a grant application (open for any wallet to submit messages)
+

The events of the topics will then be displayed within the application overview page for everybody to see.
To read out the topics we will use the https://docs.hedera.com/hedera/sdks-and-apis/sdks/consensus-service/get-topic-message[HCS SDK Get topic messages function]. Please see an example down below to query messages from a topic with over 1000 messages.

=== Overview

[plantuml, target=images/fe_component, format=svg]
----
@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

LAYOUT_TOP_DOWN()

Component(grant_application, "Grant Application", "A grant application for a given grant call.")

System_Boundary(hcs, "Hedera Consensus Service", "The Hedera Consensus Service which acts as our audit log."){
    Component_Ext(application_actions_topic, "Application Actions Topic #1", "A topic for each handed in grant application that logs all the votes the users made for the application publicly.")
    Component_Ext(application_actions_topic, "Application Actions Topic #2", "A topic for each grant application that achieved enough votes during the first community voting to pass the threshold. The topic logs all the votes of the users on the application publicly.")
    Component_Ext(application_votes_topic, "Application Votes Topic", "A topic for each grant application that holds all the votes for the application.")
}

Rel(grant_application, hcs, "Writes events to")

@enduml
----

=== Query example

[source,javascript]
----
const {
    TopicMessageQuery,
    Client,
    PrivateKey
} = require("@hashgraph/sdk");
require('dotenv').config({ path: '../.env' })

const myAccountId = "[INSERT ACCOUNT ID HERE]";
const myPrivateKey = PrivateKey.fromString("[INSERT PRIVATE KEY HERE]");
const topicId = "0.0.5237992";

// If we weren't able to grab it, we should throw a new error
if (myAccountId == null ||
    myPrivateKey == null ) {
    throw new Error("Environment variables myAccountId and myPrivateKey must be present");
}

// Create our connection to the Hedera network
// The Hedera JS SDK makes this really easy!
const client = Client.forTestnet();

client.setOperator(myAccountId, myPrivateKey);

async function main() {
    //Create the query to subscribe to a topic
    new TopicMessageQuery()
        .setTopicId(topicId)
        .setStartTime(0)
        .subscribe(
            client,
            (message) => console.log(Buffer.from(message.contents, "utf8").toString())
        );
}

main();
----

=== Changes to existing components

This requires us to:

. create a new grant call application and vote topic for each
. store the topic ids within the database for each grant call application
+
