== ADR: Voting topic for the #2 Community voting

[cols="1, 1,4,1", id=changelog, options="header"]
|===
|Version |Status |Next steps |Last updated
|0.1 |Open |To be discussed |13.03.2025
|===

== Context
The process flow of the given grant calls has been refined on 12.03.2025. A summary of the calls outcome and the newly decided process can be found https://hashgraph.atlassian.net/wiki/spaces/GMP/pages/272957441/2025-03-11+GMP+Grants+lifecycle+review[here]. +
The impact this has on the architecture is that we will have two community voting events instead of only one. +
While the first community voting mechanism is untouched and we still can use the concepts defined in adr-003, there are additional restrictions that apply to the second community voting:
[loweralpha,start=1]
. the voting will only be possible for the projects that landed the tops pots of the https://hashgraph.atlassian.net/wiki/spaces/GMP/pages/272957441/2025-03-11+GMP+Grants+lifecycle+review#Qualification-(Go%2FNo-Go)---Community-voting-%231[community voting phase] and moved into the next stage
. regardless of the number of projects that remain in this phase, each registered user on the platform will only be able to cast
.. one upvote (in favor)
.. one downvote (against)

== Impact
[loweralpha,start=1]
. The sequence diagram of the adr-003 now also includes the the 2nd voting phase