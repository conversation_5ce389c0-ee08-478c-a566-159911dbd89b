= Introduction and Goals

////
[NOTE]
====
This section provides a high-level overview of the project goals and key requirements. Keep it concise and business-focused.
====
////

// tag::manager[]
== Executive Summary

=== Objective
The Grant Management Platform by THA aims to enhance the transparency and interactivity of the grant allocation process. While the current process is perceived by the community as happening "behind closed doors," the new version will focus on two primary pillars: transparency to the community and community inclusion.

=== Current State
Currently, the PMO (Project Management Office) at THA is responsible for receiving grant applications, processing them, conducting due diligence, and selecting the projects that, in its best judgment, provide the greatest value to the Hedera ecosystem. However, in a decentralized ecosystem, no single central entity should make decisions on behalf of the community. The new approach empowers the community to influence the ecosystem they interact with by participating in the grant allocation process and making their voices heard.

Everything is powered by the Hedera Blockchain.

=== The New Grant Allocation Process
The new grant allocation process will be structured as follows:

1. Grant Program Creation: THA administrators will establish a grant program, which involves allocating a budget to a specific category, such as Gaming.

2. Issuing Grant Calls: Within each program, THA administrators will initiate grant calls. These calls will receive a portion of the allocated grant program budget and will address a specific challenge within the category. For example, within the Gaming category, a grant call could focus on promoting the inclusion of on-chain assets.

3. Application Submission Phase: Once a grant call is open, projects can submit their applications through dedicated forms on the Grant Management Platform. Applications will be publicly visible, and submissions will be accepted within a defined timeframe, referred to as the "application submission phase."

4. Community Voting: After the submission phase ends, the community will have the opportunity to vote on the applications. Members can express support for proposals they believe would benefit the ecosystem and vote against those they find less relevant.

5. Selection and Transparent PMO Process: At the conclusion of the voting phase, the top-ranked applications based on the total votes will advance to the PMO process. This process will be fully transparent to the community, with all interactions logged and publicly accessible.

=== Key Benefits: Transparency and Inclusion

==== Transparency
Transparency will be achieved through:

- Immutable Voting Records: Each vote will be recorded on the Hedera Consensus Service, signed by the voter's private key. Only registered users will be allowed to vote, ensuring authenticity and accountability.

- Public PMO Process: The entire PMO process will be open and accessible to the community.

- Documented Decisions: All decisions made during the PMO process will be publicly documented on the Hedera Consensus Service, ensuring clarity and traceability of who made when which decision based on what reason.

==== Inclusion
Inclusion will be promoted through:

- Active Community Participation: Community members will have a direct role in voting and influencing which projects receive grants.

- Live Broadcasts of PMO Interviews: Interviews conducted during the PMO process will be live-streamed, providing real-time insights into decision-making.

This approach aims to align grant allocation with the interests of the community while fostering trust and engagement through transparency and inclusion.



== Outlook
The onboarding to the grant management platform will only be the first step in the process from a letting a project become a tokenized company that investors can participate in. The workflow for currently is thought of as dispalyed below.

==== The main aspects of it are:

. Once a project received a grant and finished the implementation, an entity in Lichtenstein should be created for it
. The shares of the company will be tokenized
. The users of the grant management platform can participate in an initial token sale of the companies tokenized shares
. The tokenized shares can be traded via two ways:
.. The company will be onboarded to a partner bank that operates its crypto custody via the Taurus platform - the purpose if this is so THA does not need to get a MICAR license. The token will then be tradeable via the Taurus marketplace. Any user who wants to leverage this regulated marketplace, needs to become a customer of the bank and deposit his/her token to the banks' custody.
.. THA will host a Delivery-vs-Payment contract that serves as a trusted third party for a bulletin board. The bulletin board can be accessed by all users of the grant management platform to share request to buy or sell a given amount of token for a given price.
+


[plantuml]
----
@startuml

|Onboarding Phase|
start
:Admin creates grant program;
:Assigns budget;
:Admin creates grant call;
:Assigns fraction of budget to call;
:Projects apply for the grant call;
:Community reviews applications;
:Community votes on top projects;
:Top X projects enter next round;
:Admins organize live interviews;
:Community re-votes on final applicants;
:Winner with most votes receives grant;

|Project Implementation Phase|
:Winning project implements solution;
:Project submits invoices to THA;
:Implementation is complete;

|Company Phase|
:Create entity in Lichtenstein;
:Tokenize company shares via THA;
:Conduct initial token sale;

if (Hard KYC required for investors?) then (yes)
    |Company Phase|
    :Onboard company to partner bank;
    :Deposit tokens into custody;
    :Create marketplace for token trading;
else (no)
    |Company Phase|
    :Host blackboard for buyers/sellers;
    :Smart contract handles DVP logic;
endif

stop
@enduml
----



// end::manager[]

