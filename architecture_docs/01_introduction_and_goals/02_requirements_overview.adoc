// tag::architect[]
= Requirements Overview

////
[NOTE]
====
This section outlines high-level technical requirements. Each entry represents a key capability of the system. Update this section as new high-level requirements emerge or existing ones change significantly.
Most of these requirements will probably be from an ADR so there is an example on how to include one with proper formatting
====
////

[cols="1,2,3,3,1", id=requirements, options="header"]
|=====================================================================================
| ID| As a... | I want... | So that...| Focus
| R1| User | to be able to see all actions that happened ona grant call | I can convice myself of the correct process. | Transparency
|=====================================================================================

// end::architect[]
