= Features Traceability Matrix (RTM)

////
[NOTE]
====
This matrix serves as the central point for tracing features. A feature is not a User Story. In the example below, the feature is "Secure Login" but a User Story is something a user would access the system for.

E.g.: As an Administrator of the system I want to assign roles to users to manage the features they have access to

Update this matrix when adding new features or when making significant changes to architectural components, quality attributes, architecture decisions, or when identifying new risks or technical debt items.
====
////

[TIP]
====
To be completed once job stories have been accepted.
====

[cols="1,3,2,2,2,2", id=rtm, options="header"]
|===
|ID |Job Story |Components |Quality Attributes |Arch. Decisions |Risks/Tech Debt
|R1
|TBD
|TBD
|TBD
|TBD
|TBD
|===
