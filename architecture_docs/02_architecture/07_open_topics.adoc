
[cols="1,2,3,3,1", id=requirements, options="header"]
|=====================================================================================
| ID| Title | Description | Next steps | Decision log
| OT1 | Usage of on-prem datacenter | As the defied startegy is to host every component we can within THAs partner data center, we need to move the AWS services to it. | Define a migration plan - ideally before go-live so we don't need to migrate data. This affects the entire AWS stack within the components view. | 16.02 Created open topic
| OT2 | File storage | This also relates to OT1, as we should store the files in an archive solution provided by our datacenter | Request the possible solution from our data center | 16.02 Created open topic
| OT3 | User management | Users, roles and permissions are currently handled via a self-developed "solution" that lives within the database of the application. This is not a state we should live with for long but rather have a proper RBAC solution in place. | Added ADR-004 for this. To be discussed with <PERSON> for the roadmap. | 19.02 Created open topic; 25.02. added ADR-004
| OT4 | Platform Private Key Management | The platform will need to interact with the Hedera ecosystem itself. It therefore needs to have its own wallet with a respective balance of Hbar on it. The private key must be stored savely and must not be exposed to the application or users. | Added ADR-005 for this. For the first demo it would also b | 19.02 Created open topic; 25.02. added ADR-004
|=====================================================================================