=== Container View

This diagram shows the containerized applications the Hedera Grand Management Platform is composed of.

[plantuml, target=images/level2, format=svg]
----
@startuml
!define C4_CONTAINER
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

title Hedera Grand Management Platform - Container Diagram

LAYOUT_WITH_LEGEND()

Person_Ext(community_member, "Community Member", "Users that want to inform themselves about grants, the projects that apply for them and vote for the projects they like.")
Person_Ext(project_owner, "Project owner", "Users that run a project or have an idea for one that matches a grant call and hand in an application for it.")

System_Boundary(tha, "Hedera Grand Management Platform") {
    Person(program_coordinator, "Grant Program Coordinator", "Creates the ‘Grant Programmes’ as an initial container in which he creates the subsequent ‘Grant Calls’ and allocates ‘Grant Call Coordinators’ to them.")
    Person(call_coordinator, "Grant Call Coordinator", "Is in charge of one or more ‘Grant Call’ that the ‘Project owners’ can hand in their applications for.")

    Container(frontend, "Frontend", "TypeScript | NextJS", "Allows users (depending on their role) to create, maintain and apply for grant programs and calls.")
    Container(backend, "Backend Service", "TypeScript | NextJS", "Exposes the services the frontend requires.")
}

System_Ext(hedera, "Hedera Hashgraph", "Layer one blockchain solution of Hedera Hashgraph.")

Rel(community_member, frontend, "Accesses UI to interact with the platform")
Rel(project_owner, frontend, "Accesses UI to interact with the platform")
Rel(program_coordinator, frontend, "Accesses UI to interact with the platform")
Rel(call_coordinator, frontend, "Accesses UI to interact with the platform")
Rel(frontend, backend, "Sends API calls")
Rel(backend, hedera, "Calls services")

@enduml

----
