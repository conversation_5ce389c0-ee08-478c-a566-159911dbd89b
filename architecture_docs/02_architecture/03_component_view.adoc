
=== [[components]] Component Diagram - Frontend

This diagram shows the fine granular components which the Hedera Grand Management Platform is composed of.

[plantuml, target=images/fe_component, format=svg]
----
@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

LAYOUT_LANDSCAPE()

System_Boundary(frontend, "Hedera Grand Management Platform Frontend") {
    Component(grant_program, "Grant program", "Allows for the creation and maintenance of grant programs.")
    Component(grant_call, "Grant call", "Allows for the creation and maintenance of one or more grant calls that belong ot a specific grant program.")
    Component(grant_application, "Grant application", "Allows for the creation and maintenance of grant applications that projects hand in for a grant call.")
    Component(landing_page, "Landing page", "Landing page of the grant management platform.")
    Component(account_management, "Account management", "Allows the users to register, log on and recover accounts.")
    Component(login, "Login page", "Allows the users to log in or kick off the account recovery.")
}

System_Boundary(backend, "Hedera Grand Management Platform Backend") {
    Component(database_connector, "Database Connector", "Allows for the creation and maintenance of grant programs.")
    Component(voting_service, "Grant Application Voting Service", "Allows for the creation of votes for given grant calls.")
    Component(balance_service, "Wallet Balance Service", "Checks the balance of the platform wallet and ensures operability by sending out notifications to the platform admins if the wallet needs additional Hbar.")
    Component(kms_service, "KMS Service", "Allows for the interactions with the Key Management Service (KMS).")
    Component(grant_application_service, "Grant Application Service", "Allows for the creation and maintenance of grant programs.")
    Component(grant_call_service, "Grant Call Service", "Allows for the creation and maintenance of grant programs.")
    Component(grant_program_service, "Grant Program Service", "Allows for the creation and maintenance of grant programs.")
    Component(mail_service, "Mail Service", "Allows for the creation and maintenance of grant programs.")
    Component(notifications_service, "Notifications Service", "Allows for the creation and maintenance of grant programs.")
    Component(sms_service, "SMS Service", "Allows for the creation and maintenance of grant programs.")
    Component(auth_service, "Authentication Service", "Handles authentication and authorization of the solution.")
    Component(hedera_service, "Hedera Service", "Allows for the interaction with the different Hedera services.")
}

System_Boundary(aws, "Amazon Web Services") {
    Component_Ext(s3_bucket, "AWS S3", "The simple storage of AWS is being used for smaller files like profile images and the likes.")
    Component_Ext(rds_postgres, "RDS_PostgreSQL", "The postgreSQL database provided by the managed database service of AWS is used for all the internal data of the application (users, roles, gran programs and calls, ...). You can refer to the data schema for more information.")
    Component_Ext(aws_ses, "AWS SES", "The Simple Email Service (SES) from AWS is being used for sending out the onbaording and account recovery emails.")
    Component_Ext(aws_kms, "AWS KMS", "The Key Management System provided by AWS which is being used to store the platforms private key.")
    Component_Ext(sms_auth, "AWS SNS", "The Simple Notification Service of AWS allows to send one-time-passwords to mobile phones.")
}

System_Boundary(hedera, "Hedera Hashgraph") {
    Component_Ext(hcs, "Hedera Consensus Service", ".")
}

Component_Ext(wallet_connect, "Wallet Connect", "Allows to the user to connect to the applications via different wallet providers.")
Component_Ext(hashpack, "Hashpack SDK", "Allows for the easy connection of a wallet that an user keeps in self-custody within his clients hashpack application.")
Component_Ext(social_signup, "Social Signup", "Allows for the easy login of users via their social logins.")

Rel(frontend, backend, "Accesses for services")
Rel(hedera_service, hcs, "Accesses for user profiles")
Rel(database_connector, rds_postgres, "CRUD accesses for data schemas")
Rel(mail_service, aws_ses, "Accesses for sending out notification emails")
Rel(notifications_service, mail_service, "Accesses for sending out emails")
Rel(sms_service, sms_auth, "Accesses for sending out verification sms")
Rel(auth_service, social_signup, "Accesses for authentication of users")
Rel(auth_service, database_connector, "Accesses for authentication of users")
Rel(backend, wallet_connect, "Accesses for authentication of users")
Rel(backend, hashpack, "Accesses for authentication of users via hashpack browser plugins")
Rel(voting_service, hedera_service, "Uses to store votes on HCS")
Rel(kms_service, aws_kms, "Uses to access the platforms private key")
@enduml
----
