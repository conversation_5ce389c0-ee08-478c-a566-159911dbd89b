=== Runtime View


[NOTE]
====
This documentation is intended for developers and architects to help them understand how the application has been implemented and the functions implemented in each component and how the components interact.

This document should contain Sequence diagrams to show the sequence of events to implement a particular feature. It should also contain state diagrams, if pertinent, to show the different states of information in the processes.

It can contain any other details (class diagrams, ERM and more) which would help a developer understand the system. The deployment is described in the deployment view
====

=== Connect a wallet
[TIP]
====
This diagram shows how users can connect a wallet to the application.
====

Please note that:

. The Hedera account address must be in format <shard>.<realm>.<account>
. The challenge is also included in the challenge token to help the backend determine that it was the originator of the challenge and how old it is.
+


// tag::architect[]
[plantuml, target=images/deployment, format=svg]
----
@startuml
title Deploy a policy

participant usr as "User"
participant fe as "Frontend"
participant oauth as "OAuth Providers"
participant be as "Backend"

usr -> fe: Goto /connect-wallet and click "Connect Wallet"
fe --> usr: Prompt user to connect wallet
usr -> fe: Confirm wallet connection

fe -> be: GET /auth/request-challenge?address=<hedera-account-address>
note right
The Hedera account address must be in format
<shard>.<realm>.<account>
end note
be --> fe: response
note right
{
  "success": true,
  "challenge": "example.com wants you to...",
  "challengeToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "accountExists": false
}
end note

----

=== Social Provider Sign Up Flow
[TIP]
====
This diagram shows how users can log in using different social logins that provide oauth.
The currently supported logins are: +
1. Google +
2. Github
====

Please note that:

. The challenge is also included in the challenge token to help the backend determine that it was the originator of the challenge and how old it is.
. The POST /auth/signing request uses same `challengeToken` as was used in the initial request to `signup`.
+

Example of a session token JWT payload:
```
{
  "id": 4,
  "role": "USER",
  "scope": "SESSION_TOKEN",
  "emailVerified": false
}
```


// tag::architect[]
[plantuml, target=images/deployment, format=svg]
----
@startuml
title Deploy a policy

participant usr as "User"
participant fe as "Frontend"
participant oauth as "OAuth Providers"
participant be as "Backend"

usr -> fe: Clicks on any of the social provider buttons
fe -> oauth: User signs into the social platform
oauth --> fe: Platform returns access token with embedded user data
fe -> be: GET /auth/request-challenge?address=<hedera-account-address>
note right
The Hedera account address must be in format
<shard>.<realm>.<account>
end note
be --> fe: response
note right
{
  "success": true,
  "challenge": "example.com wants you to...",
  "challengeToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "accountExists": false
}
end note
fe -> be: POST /auth/social-login
be --> fe: response
note right
{
  "success": true,
  "sessionToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "isNewUser": false,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "role": "USER",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
end note
fe -> be: POST /auth/signin
note right
{
  "address": "0.0.4422816",
  "challengeToken": "eyJhbGciOiJIUzI1...A_678oMVg",
  "signature": "0x2b4c2fdce0...ce81061c"
}
end note
be --> fe: response
note right
{
  "success": true,
  "sessionToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ...",
  "user": {
    // user information
  }
}
end note
fe -> fe: Decode sessionToken and check emailVerified == false

@enduml
----

=== Manual Up Flow
[TIP]
====
This diagram shows how users sign up and verify an email address.
====

Please note that:

. The POST /auth/signing request uses same `challengeToken` as was used in the initial request to `signup`.
. Note that after the final step subsequent authenticated requests to the backend would fail if the session token does not contain `emailVerified: true`
+

Example of a session token JWT payload:
```
{
  "id": 4,
  "role": "USER",
  "scope": "SESSION_TOKEN",
  "emailVerified": false
}
```


// tag::architect[]
[plantuml, target=images/deployment, format=svg]
----
@startuml
title Deploy a policy

participant usr as "User"
participant fe as "Frontend"
participant oauth as "OAuth Providers"
participant be as "Backend"

usr -> fe: User enters account information for new account
fe --> usr: Call signMessage on connect wallet with backend challenge
usr -> fe: Approve signMessage
fe -> be: POST /auth/signup
note right
{
  "address": "0.0.212434",
  "email": "<EMAIL>",
  "displayName": "John Doe",
  "challengeToken": "eyJhbGciOiJIUzI1...A_678oMVg",
  "signature": "0x2b4c2fdce01d9530ca...92ce81061c"
}
end note
be -> be: Verify the signature
be -> be: check if an account with the address or email already exists
alt does not exist
be -> be: create account
end
be --> fe: response
note right
{
  "success": true,
  "message": 'User created successfully.'
}
end note
fe -> be: POST /auth/signin
note right
{
  "address": "0.0.4422816",
  "challengeToken": "eyJhbGciOiJIUzI1...A_678oMVg",
  "signature": "0x2b4c2fdce0...ce81061c"
}
end note
be --> fe: response
note right
{
  "success": true,
  "sessionToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ...",
  "user": {
    // user information
  }
}
end note
fe -> fe: Decode sessionToken and check emailVerified == false

== Email Verification Flow ==
fe -> be: POST /auth/send-otp
note right
Authorization: Bearer <sessionToken>
end note
be -> be: Get user's email address using the session token and send OTP email for email verification
be --> fe: response
note right
{
  "success": true,
  "message": "OTP sent to the user."
}
end note
fe --> usr: Display OTP request
usr -> fe: Enters OTP from email
fe -> be: POST /auth/verify-otp
be -> be: Verify OTP and generate new session token this time with `emailVerified: true`
be --> fe: response
note right
{
  "success": true,
  "sessionToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ...",
}
end note
fe -> fe: Replace session token with new one and successfully login the user
@enduml
----

=== Account Recovery
[TIP]
====
This diagram shows how users can recover their account.
====

Please note that:

. If the response to the ``GET /auth/request-challenge?address=<hedera-wallet-address>`` call contains `accountExists: true`, don't continue the flow. It means that the address is already in use by an account and adding the address will fail.

+


// tag::architect[]
[plantuml, target=images/deployment, format=svg]
----
@startuml
title Deploy a policy

participant usr as "User"
participant fe as "Frontend"
participant be as "Backend"

usr -> fe: Visit recovery page, provide email of account to recover
fe -> be: POST /auth/recover-account
note right
{
  "email":"<EMAIL>"
}
end note
be -> be: Fetch account associated with email address and generate a recovery token
be --> fe: 200 OK
be --> usr: Send email containing recovery link including a recovery token
usr -> fe: User opens email and clicks on recovery link
fe --> usr: Shows recovery page with a button to connect wallet
usr -> fe: Connect wallet
fe -> be: GET /auth/request-challenge?address=<hedera-wallet-address>
be --> fe: response
note right
{
  "success": true,
  "challenge": "example.com wants you to...",
  "challengeToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "accountExists": false
}
end note
fe --> usr: Call signMessage on connected wallet with challenge from backend
usr -> fe: Approve signMessage
fe -> be: POST /auth/update-wallet
note right
{
  "address": "0.0.212434",
  "challengeToken": "eyJhbGciOiJIUzI1...A_678oMVg",
  "signature": "0x2b4c2fdce01d9530caa7ff3beeb...1061c",
  "recoveryToken": "eyJhbGciOiJIUzI1...A_678oMVg"
}
end note
be -> be: Verify signature
note right
Verify signature, and use the email address
in the recovery token to retrieve the user entity.
Check if the new address is already in use by another user account.
Remove all previous addresses and add the new one.
end note
be --> fe: response
note right
{
  "success": true,
  "message": "Wallet address updated successfully."
}
end note
fe --> usr: Show recovery success page.
note left
After that, the user has to log in with the new wallet.
end note
@enduml
----

=== Adding a wallet address to an account
[TIP]
====
This diagram shows how the wallet address of an user gets added to the account.
====

Please note that:

. The Hedera account address must be in format <shard>.<realm>.<account>
. If the response to the ``GET /auth/request-challenge?address=<hedera-wallet-address>`` call contains `accountExists: true`, don't continue the flow. It means that the address is already in use by an account and adding the address will fail.

+


// tag::architect[]
[plantuml, target=images/deployment, format=svg]
----
@startuml
title Deploy a policy

participant usr as "User"
participant fe as "Frontend"
participant be as "Backend"

note over usr
Precondition: user is logged
in and has a valid JWT.
end note
usr -> fe: Goto user account settings and click "connect new wallet"
fe --> usr: response
note right
Show WalletConnect QR code/deep link
or open HashPack browser extension.
Prompt user to scan or enter PW.
end note
usr -> fe: Confirm wallet connection
fe -> be: GET /auth/request-challenge?address=<hedera-wallet-address>
be --> fe: response
note right
{
  "success": true,
  "challenge": "example.com wants you to...",
  "challengeToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "accountExists": false
}
end note
fe --> usr: Call signMessage on connectd wallet with challenge from backend
usr -> fe: Approve signMessage
fe -> be: POST /auth/add-wallet
note right
Authorization: Bearer <session-jwt>
{
  "address": "0.0.212434",
  "challengeToken": "eyJhbGciOiJIUzI1...A_678oMVg",
  "signature": "0x2b4c2fdce01d9530ca...92ce81061c"
}
end note
be -> be: Verification
note right
Check if the new address is already in use by another user account.
Verify the signature with the challenge in the challenge token,
the signature and the public key associated with the new address
end note
be --> fe: response
note right
{
  "success": true,
  "message": 'Wallet successfully added to the account.'
}
end note
fe --> usr: Inform the user and make them signin
@enduml
----
