== Data schema

The following diagram displays the current data schema used for the application.

[plantuml]
----
@startuml

enum user_role_enum {
  GRANT_PROGRAM_COORDINATOR
  USER
}

enum grant_category_enum {
  ON_CHAIN_FINANCE
  CONSUMER_LOYALTY
  SUSTAINABILITY
  DIGITAL_IDENTITY
  TRACEABILITY
  TELCO
  OTHER
}

enum business_category_enum {
  STARTUP
  ENTERPRISE
  GOVERNMENT
}

enum distribution_type_enum {
  PERCENTAGE
  FIXED_AMOUNT
}

enum workflow_status_enum {
  IN_PROGRESS
  READY_FOR_NEXT_STEP
  ACTION_REQUIRED
  APPROVED
  REJECTED
  WITHDRAWN
  CLOSED
}

enum stage_code_enum {
  GP_OPEN
  GP_FINALIZED
  GC_CLOSED
  GC_OPEN_FOR_APPLICATIONS
  GC_SCREENING
  GC_COMMUNITY_VOTING
  GC_ONBOARDING
  GC_FINAL_COMMUNITY_VOTING
  GC_FINALIZED
  GA_SCREENING
  GA_QUALIFICATION
  GA_INTERVIEW
  GA_DUE_DILIGENCE
  GA_TOWN_HALL
  GA_FINAL_QUALIFICATION
}

enum stage_transition_type_enum {
  MANUAL
  AUTOMATIC
}

enum workflow_entity_type_enum {
  PROGRAM
  CALL
  APPLICATION
}

class User {
  +id : integer
  +email : varchar (unique)
  +addresses : text[] (not nullable)
  +displayName : varchar
  +emailVerified : boolean (default: false)
  +phoneNumber : varchar (unique, nullable)
  +isPhoneVerified : boolean (default: false)
  +phoneOtp : bigint (nullable)
  +phoneOtpExpiresAt : timestamptz (nullable)
  +otp : bigint (nullable)
  +otpExpiresAt : timestamptz (nullable)
  +createdAt : timestamptz (auto-generated)
  +role : user_role_enum (default: USER)
  +deletedAt : timestamptz (nullable, soft delete)
  +reactivatedAt : timestamptz (nullable)
  +linkedAccounts : jsonb (nullable, default: {})
}

class UserNotificationPreferences {
    +id: integer (Primary Key)
    +userId: integer (Foreign Key)
    +notificationType: varchar
    +enabled: boolean (default: true)
    +createdAt: timestamptz (auto-gen)
    +updatedAt: timestamptz (auto-gen)
}

class GrantApplication {
  +id : integer
  +title : varchar
  +description : varchar
  +companyName : varchar
  +companyCountry : varchar
  +categories : grant_category_enum[]
  +contactFullName : varchar
  +contactEmail : varchar
  +contactPhoneNumber : varchar
  +actionTopicId : varchar(255)
  +votingTopicId : varchar(255) (nullable)
  +workflowStateId : integer
  +createdById : integer
  +createdAt : timestamptz
  +updatedAt : timestamptz
  --
  +grantCallId : integer
}

class GrantCall {
  +id : integer
  +grantCallSlug : varchar (unique)
  +name : varchar
  +description : varchar
  +businessCategory : business_category_enum
  +categories : text[] (nullable)
  +totalGrantAmount : numeric(18,2) (nullable)
  +createdById : integer
  +workflowStateId : integer (nullable)
  +createdAt : timestamptz
  +updatedAt : timestamptz
  --
  +grantProgramId : integer
}

class GrantProgram {
  +id : integer
  +grantProgramSlug : varchar (unique)
  +name : varchar
  +description : varchar
  +scope : varchar
  +budget : numeric(12,0)
  +grantorPublicProfileName : varchar
  +grantorLogoURL : varchar
  +grantorDescription : varchar
  +grantorWebsite : varchar
  +workflowStateId : integer (nullable)
  +createdAt : timestamptz
  +updatedAt : timestamptz
  --
  +grantProgramCoordinatorId : integer
}

class GrantCallStageSetting {
  +id : integer
  +grantCallId : integer
  +workflowStepDefinitionId : integer
  +startDate : timestamptz (nullable)
  +endDate : timestamptz (nullable)
  +durationSeconds : integer (nullable)
  +stageUrl : text (nullable)
}

class GrantDistributionRule {
  +id : integer
  +grantCallId : integer
  +rank : integer
  +type : distribution_type_enum
  +value : numeric(18,2)
}

class Vote {
  +id : integer
  +grantApplicationId: integer (Foreign Key)
  +inFavorVotes: integer (default: 0)
  +againstVotes: integer (default: 0)
  +walletsInFavor : varchar[]
  +walletsAgainst : varchar[]
  +createdAt : timestamptz
  +updatedAt : timestamptz
}

class WorkflowState {
  +id : integer
  +workflowTemplateId : integer (nullable)
  +currentStepDefinitionId : integer (nullable)
  +status : workflow_status_enum (default: IN_PROGRESS)
  +currentStepTransitionedAt : timestamptz (nullable)
  +currentStepEndsAt : timestamptz (nullable)
}

class WorkflowStepDefinition {
  +id : integer
  +workflowTemplateId : integer
  +name : varchar(100)
  +code : stage_code_enum
  +sequenceNumber : integer
  +transitionType : stage_transition_type_enum (default: MANUAL)
  +description : text (nullable)
  +isTerminal : boolean (default: false)
}

class WorkflowTemplate {
  +id : integer
  +name : varchar(150) (unique)
  +entityType : workflow_entity_type_enum
}


' Relationships

User "1" -- "*" UserNotificationPreferences : has
User "1" -- "*" GrantApplication : created (createdBy)
User "1" -- "*" GrantCall : created (createdBy)
User "1" -- "*" GrantProgram : coordinatedBy (grantProgramCoordinator)

GrantProgram "1" -- "*" GrantCall : contains
GrantProgram "1" -- "1" WorkflowState : manages_status_via (nullable)

GrantCall "1" -- "*" GrantApplication : has_applications
GrantCall "1" -- "1" WorkflowState : manages_status_via (nullable)
GrantCall "1" -- "*" GrantCallStageSetting : has_settings_for
GrantCall "1" -- "*" GrantDistributionRule : has_rules_for

GrantApplication "1" -- "1" Vote : has_vote_details
GrantApplication "1" -- "1" WorkflowState : manages_status_via

GrantCallStageSetting "*" -- "1" WorkflowStepDefinition : uses_definition

WorkflowTemplate "1" -- "*" WorkflowStepDefinition : defines_steps
WorkflowState "0..1" -- "1" WorkflowTemplate : based_on_template (nullable)
WorkflowState "0..1" -- "1" WorkflowStepDefinition : at_current_step (nullable)


@enduml
----
