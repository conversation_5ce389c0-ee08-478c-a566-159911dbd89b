= System Context


This diagram shows the systems that are communicating and interacting within the scope of the Hedera Grant Management Platform. The diagram should be updated when new systems are added or removed from the platform.

// tag::architect[]
[plantuml,target=images/deployment,format=svg]
----

@startuml
!define C4_CONTEXT
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

LAYOUT_WITH_LEGEND()

title Hedera Grand Management Platform System Context Diagram

Person_Ext(users, "Users", "Users can vote on projects that applied for a grant call or create an application for one.")

System_Boundary(tha, "Grant Management PLatform") {
    Person(admins, "Admins", "There are different administrative roles within the platform that allow for the management of grant programs and grant calls")

    System(platform, "Grant Management PLatform", "The THG Funding Platform simplifies grant management and expands into decentralized funding with Hedera Hashgraph while providing transparency of the process to the community.")
}
System_Ext(hedera, "Hedera Hashgraph", "Layer one blockchain solution of Hedera Hashgraph.")

Rel(users, platform, "Accesses the platform via its frontend.")
Rel(admins, platform, "Accesses the platform via its frontend.")
Rel(platform, hedera, "Interacts with different services.")

@enduml
----
