# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
indent_style = space

[*.html]
indent_style = space
indent_size = 2

[*.{ts,json,js,tsx,jsx}]
indent_style = space
indent_size = 2

[*.md]
indent_size = 2
indent_style = space

[Dockerfile]
indent_style = space
indent_size = 2

# Tab indentation (no size specified)
[Makefile]
indent_style = tab

[*.{yml,yaml}]
indent_size = 2
indent_style = space
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true
line_wrap_mode = soft wrap
