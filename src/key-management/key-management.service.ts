import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { KMSClient, SignCommand, SignCommandInput } from '@aws-sdk/client-kms';

import { ConfigService } from '@nestjs/config';
import keccak256 from 'keccak256';

// Important for EC / ECDSA signature conversion
export type ECSignatureComponents = {
  r: Buffer;
  s: Buffer;
};

@Injectable()
export class KeyManagementService {
  private readonly logger = new Logger(KeyManagementService.name);
  private readonly kmsClient: KMSClient;
  private readonly kmsKeyId: string;

  constructor(private readonly configService: ConfigService) {
    const region = this.configService.getOrThrow<string>('AWS_KMS_REGION');
    this.kmsKeyId = this.configService.getOrThrow<string>('AWS_KMS_KEY_ID');

    this.kmsClient = new KMSClient({
      region,
      credentials: {
        accessKeyId: this.configService.getOrThrow<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.getOrThrow<string>('AWS_SECRET_ACCESS_KEY'),
      },
    });
  }

  async signMessage(messageBytes: Uint8Array): Promise<Buffer> {
    try {
      const hash = keccak256(`0x${Buffer.from(messageBytes).toString('hex')}`);

      const signParams: SignCommandInput = {
        KeyId: this.kmsKeyId,
        Message: hash,
        MessageType: 'DIGEST',
        SigningAlgorithm: 'ECDSA_SHA_256',
      };

      const signCommand = new SignCommand(signParams);
      const response = await this.kmsClient.send(signCommand);

      if (!response.Signature) {
        throw new HttpException('Failed to sign message: no signature returned from KMS', HttpStatus.FAILED_DEPENDENCY);
      }

      // Convert DER signature to the format Hedera expects
      const signature = Buffer.from(response.Signature);

      // Parse the DER ECDSA signature and get components
      const { r, s } = this.parseDERSignature(signature);
      const hederaSignature = this.createHederaCompatibleSignature(r, s);

      return hederaSignature;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error('Error signing message with AWS KMS:', error);

      throw new HttpException(`Failed to sign message with AWS KMS: ${error.message}`, HttpStatus.FAILED_DEPENDENCY);
    }
  }

  /**
   * Parse a DER encoded ECDSA signature into its r and s components
   */
  private parseDERSignature(derSignature: Buffer): ECSignatureComponents {
    let offset = 0;

    // Check for SEQUENCE tag
    if (derSignature[offset++] !== 0x30) {
      throw new Error('Invalid DER signature: Expected SEQUENCE tag');
    }

    // Skip sequence length
    offset++;

    // First INTEGER tag for R value
    if (derSignature[offset++] !== 0x02) {
      throw new Error('Invalid DER signature: Expected INTEGER tag for R');
    }

    // R length and value
    const rLength = derSignature[offset++];
    const rValue = derSignature.subarray(offset, offset + rLength);
    offset += rLength;

    // Second INTEGER tag for S value
    if (derSignature[offset++] !== 0x02) {
      throw new Error('Invalid DER signature: Expected INTEGER tag for S');
    }

    // S length and value
    const sLength = derSignature[offset++];
    const sValue = derSignature.subarray(offset, offset + sLength);

    return {
      r: rValue,
      s: sValue,
    };
  }

  /**
   * Create a Hedera-compatible signature from r and s components
   * For secp256k1 curve, both r and s should be 32 bytes each
   */
  private createHederaCompatibleSignature(r: Buffer, s: Buffer): Buffer {
    const normalizeBuffers = (buffer: Buffer, length: number) => {
      // Ensure buffer is correct length, removing padding byte if present
      if (buffer.length > length && buffer[0] === 0x00) {
        return buffer.subarray(1);
      } else if (buffer.length < length) {
        const paddedBuffer = Buffer.alloc(length, 0);
        buffer.copy(paddedBuffer, length - buffer.length, 0, buffer.length);
        return paddedBuffer;
      }
      return buffer;
    };

    // Normalize buffers to make sure they are correct length
    const normalizedR = normalizeBuffers(r, 32);
    const normalizedS = normalizeBuffers(s, 32);

    // Hedera expects a plain concatenation of r|s (64 bytes total)
    return Buffer.concat([normalizedR, normalizedS]);
  }
}
