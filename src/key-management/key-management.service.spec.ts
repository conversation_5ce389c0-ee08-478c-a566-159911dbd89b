import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@nestjs/config';
import { KeyManagementService } from './key-management.service';

describe('KeyManagementService', () => {
  let service: KeyManagementService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeyManagementService,
        {
          provide: ConfigService,
          useValue: {
            getOrThrow: jest.fn().mockImplementation((key: string) => {
              const config = {
                AWS_KMS_REGION: 'us-east-1',
                AWS_KMS_KEY_ID: 'test-key-id',
                AWS_ACCESS_KEY_ID: 'test-access-key',
                AWS_SECRET_ACCESS_KEY: 'test-secret-key',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    service = module.get<KeyManagementService>(KeyManagementService);
  });

  describe('parseDERSignature', () => {
    it('should correctly parse a valid DER signature', () => {
      // Example DER signature (simplified for testing)
      // This is a mock DER signature with:
      // - SEQUENCE tag (0x30)
      // - R value (0x02 followed by length and value)
      // - S value (0x02 followed by length and value)
      const derSignature = Buffer.from([
        0x30,
        0x44, // SEQUENCE tag and length
        0x02,
        0x20, // R INTEGER tag and length
        ...Array(32).fill(0x01), // R value (32 bytes)
        0x02,
        0x20, // S INTEGER tag and length
        ...Array(32).fill(0x02), // S value (32 bytes)
      ]);

      const result = (service as any).parseDERSignature(derSignature);

      expect(result.r).toBeDefined();
      expect(result.s).toBeDefined();
      expect(result.r.length).toBe(32);
      expect(result.s.length).toBe(32);
      expect(result.r[0]).toBe(0x01);
      expect(result.s[0]).toBe(0x02);
    });

    it('should throw error for invalid SEQUENCE tag', () => {
      const invalidSignature = Buffer.from([0x31, 0x44]); // Wrong tag
      expect(() => (service as any).parseDERSignature(invalidSignature)).toThrow(
        'Invalid DER signature: Expected SEQUENCE tag',
      );
    });

    it('should throw error for invalid R INTEGER tag', () => {
      const invalidSignature = Buffer.from([
        0x30,
        0x44, // Valid SEQUENCE
        0x03,
        0x20, // Wrong tag for R
      ]);
      expect(() => (service as any).parseDERSignature(invalidSignature)).toThrow(
        'Invalid DER signature: Expected INTEGER tag for R',
      );
    });
  });

  describe('createHederaCompatibleSignature', () => {
    it('should create a valid Hedera signature from normalized r and s values', () => {
      const r = Buffer.alloc(32, 0x01);
      const s = Buffer.alloc(32, 0x02);

      const result = (service as any).createHederaCompatibleSignature(r, s);

      expect(result.length).toBe(64); // 32 bytes for r + 32 bytes for s
      expect(result.subarray(0, 32)).toEqual(r);
      expect(result.subarray(32)).toEqual(s);
    });

    it('should normalize buffers that are too long', () => {
      const r = Buffer.from([0x00, ...Array(32).fill(0x01)]); // 33 bytes with leading 0
      const s = Buffer.from([0x00, ...Array(32).fill(0x02)]); // 33 bytes with leading 0

      const result = (service as any).createHederaCompatibleSignature(r, s);

      expect(result.length).toBe(64);
      expect(result[0]).toBe(0x01); // Leading 0 should be removed
      expect(result[32]).toBe(0x02); // Leading 0 should be removed
    });

    it('should normalize buffers that are too short', () => {
      const r = Buffer.alloc(30, 0x01); // 30 bytes
      const s = Buffer.alloc(30, 0x02); // 30 bytes

      const result = (service as any).createHederaCompatibleSignature(r, s);

      expect(result.length).toBe(64);
      // First two bytes should be padded with zeros
      expect(result[0]).toBe(0x00);
      expect(result[1]).toBe(0x00);
      expect(result[32]).toBe(0x00);
      expect(result[33]).toBe(0x00);
    });
  });
});
