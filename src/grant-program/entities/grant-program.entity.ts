import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { GrantCall } from '../../grant-call/entities/grant-call.entity';
import { NumericColumnTransformer } from '../../database/column-transformers';
import { User } from '../../auth/entities/user.entity';
import { WorkflowState } from '../../workflow/entities/workflow-state.entity';

@Entity()
export class GrantProgram {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true })
  grantProgramSlug: string;

  @Column()
  name: string;

  @Column()
  description: string;

  @Column()
  scope: string;

  // The budget is restricted to a size that can be contained in a JS `number`. Additionally, we use a transformer to
  // always convert the retrieved value to a `number` type. It would be returned as `string` if we don't do this.
  @Column({ type: 'numeric', precision: 12, scale: 0, transformer: new NumericColumnTransformer() })
  budget: number;

  @Column()
  grantorPublicProfileName: string;

  @Column()
  grantorLogoURL: string;

  @Column()
  grantorDescription: string;

  @Column()
  grantorWebsite: string;

  @ManyToOne(() => User)
  @JoinColumn()
  grantProgramCoordinator: User;

  @OneToMany(() => GrantCall, (grantCall) => grantCall.grantProgram)
  grantCalls: GrantCall[];

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;

  @Column({ type: 'int', nullable: true })
  workflowStateId: number | null;

  @OneToOne(() => WorkflowState, {
    nullable: true,
    onDelete: 'RESTRICT',
    eager: false,
    cascade: true,
  })
  @JoinColumn({ name: 'workflowStateId' })
  workflowState: WorkflowState | null;
}
