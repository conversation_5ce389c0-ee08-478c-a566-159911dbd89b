import { ApiProperty } from '@nestjs/swagger';
import { AuthenticatedGrantCallDto } from '../../grant-call/dto';
import { IsArray, IsNotEmpty } from 'class-validator';

export class GrantCallsOfProgramResponse {
  @ApiProperty({
    example: true,
    description: 'Status to indicate the success of the request.',
  })
  @IsNotEmpty()
  success: boolean;

  @ApiProperty({
    type: [AuthenticatedGrantCallDto],
    description: 'The grant calls of the grant program.',
  })
  @IsNotEmpty()
  @IsArray({ each: true })
  data: AuthenticatedGrantCallDto[];
}
