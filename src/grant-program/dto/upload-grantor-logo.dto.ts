import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class FileUploadResponse {
  @ApiProperty({
    example: true,
    description: 'Status to indicate the success of the request.',
  })
  @IsNotEmpty()
  @IsBoolean()
  success: boolean;

  @ApiProperty({
    example: 'temp/hederahash-1234.jpg',
    description: 'The temporary file path of the uploaded file. Should be used while creating the grant program.',
  })
  @IsNotEmpty()
  @IsString()
  filePath: string;
}
