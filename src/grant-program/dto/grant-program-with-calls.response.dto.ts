import { ApiProperty } from '@nestjs/swagger';
import { FindOneGrantCallBaseDto } from '../../grant-call/dto';
import { GrantProgramDto } from './grant-program.dto';

export class GrantProgramWithCallsResponseDto {
  @ApiProperty({ type: GrantProgramDto, description: 'Details of the Grant Program.' })
  grantProgram: GrantProgramDto;

  @ApiProperty({ type: [FindOneGrantCallBaseDto], description: 'List of Grant Calls associated with the program.' })
  grantCalls: FindOneGrantCallBaseDto[];
}
