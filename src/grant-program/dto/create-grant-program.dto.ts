import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, MaxLength } from 'class-validator';

export class CreateGrantProgram {
  @ApiProperty({
    required: true,
    example: 'Grant Program 1',
    description: 'The name of the grant program.',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(120)
  name: string;

  @ApiProperty({
    required: true,
    example: 'This is a grant program.',
    description: 'A description of the grant program.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    required: true,
    example: 'Tech Enablement on Hedera Network',
    description: 'The main scope of the grant program.',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(120)
  scope: string;

  @ApiProperty({
    required: true,
    example: 10000,
    description: 'The total budget allocated for the grant program.',
  })
  @IsNumber()
  @IsNotEmpty()
  budget: number;

  @ApiProperty({
    required: true,
    example: 'Hedera Hashgraph',
    description: 'The name of the grantor.',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(120)
  grantorPublicProfileName: string;

  @ApiProperty({
    required: true,
    example: 'https://hedera.com/logo.png',
    description: 'The logo of the grantor.',
  })
  @IsString()
  @IsNotEmpty()
  grantorLogoURL: string;

  @ApiProperty({
    required: true,
    example: 'Hedera Hashgraph is a public distributed ledger for building decentralized applications.',
    description: 'The description of the grantor.',
  })
  @IsString()
  @IsNotEmpty()
  grantorDescription: string;

  @ApiProperty({
    required: true,
    example: 'https://hedera.com',
    description: 'The website of the grantor.',
  })
  @IsString()
  @IsNotEmpty()
  grantorWebsite: string;
}

export class CreateGrantProgramResponse {
  @ApiProperty({
    example: true,
    description: 'Status to indicate the success of the request.',
  })
  success: boolean;

  @ApiProperty({
    example: 'test-grant-program-zxcvas',
    description: 'The slug of the grant program.',
  })
  grantProgramSlug: string;

  @ApiProperty({
    example: 'Grant Program created successfully.',
    description: 'The message to indicate the success of the request.',
  })
  message: string;
}
