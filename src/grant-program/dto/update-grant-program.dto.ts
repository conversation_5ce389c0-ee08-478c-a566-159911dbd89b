import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, <PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class UpdateGrantProgram {
  @ApiProperty({
    required: false,
    example: 'Grant Program 1',
    description: 'The name of the grant program.',
  })
  @IsString()
  @IsOptional()
  @MaxLength(120)
  name: string;

  @ApiProperty({
    required: false,
    example: 'This is a grant program.',
    description: 'A description of the grant program.',
  })
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty({
    required: false,
    example: 'Tech Enablement on Hedera Network',
    description: 'The main scope of the grant program.',
  })
  @IsOptional()
  @IsString()
  @MaxLength(120)
  scope: string;

  @ApiProperty({
    required: false,
    example: 10000,
    description: 'The total budget allocated for the grant program.',
  })
  @IsOptional()
  @IsNumber()
  budget: number;

  @ApiProperty({
    required: false,
    example: 'Hedera Hashgraph',
    description: 'The name of the grantor.',
  })
  @IsOptional()
  @IsString()
  @MaxLength(120)
  grantorPublicProfileName: string;

  @ApiProperty({
    required: false,
    example: 'https://hedera.com/logo.png',
    description: 'The logo of the grantor.',
  })
  @IsOptional()
  @IsString()
  grantorLogoURL: string;

  @ApiProperty({
    required: false,
    example: 'Hedera Hashgraph is a public distributed ledger for building decentralized applications.',
    description: 'The description of the grantor.',
  })
  @IsOptional()
  @IsString()
  grantorDescription: string;

  @ApiProperty({
    required: false,
    example: 'https://hedera.com',
    description: 'The website of the grantor.',
  })
  @IsOptional()
  @IsString()
  grantorWebsite: string;
}

export class UpdateGrantProgramResponse {
  @ApiProperty({
    example: true,
    description: 'Status to indicate the success of the request.',
  })
  success: boolean;

  @ApiProperty({
    example: 'updated-grant-program-slug-jieoj',
    description: "The grant programs slug. Updated if the program's name was changed.",
  })
  grantProgramSlug: string;
}
