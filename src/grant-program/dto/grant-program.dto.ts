import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { GrantProgramStageCode, StageCode } from '../../workflow/enums/stage-code.enum';
import { UserDto } from '../../auth/dto';

export class GrantProgramDto {
  @ApiProperty({
    example: 'grant-program-1-898788',
    description: 'The slug of the grant program.',
  })
  @IsNotEmpty()
  grantProgramSlug: string;

  @ApiProperty({
    example: 'Grant Program 1',
    description: 'The name of the grant program.',
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'The description of the grant program.',
    description: 'The description of the grant program.',
  })
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    example: 'Tech Enablement on Hedera Network',
    description: 'The scope of the grant program.',
  })
  @IsNotEmpty()
  scope: string;

  @ApiProperty({
    example: 100000,
    description: 'The budget of the grant program.',
  })
  @IsNotEmpty()
  budget: number;

  @ApiProperty({
    example: 'Hedera Foundation',
    description: 'The name of the grantor public profile.',
  })
  @IsNotEmpty()
  grantorPublicProfileName: string;

  @ApiProperty({
    example: 'grant-logos/grant-program-1-898788.jpg',
    description: 'The URL of the grantor logo.',
  })
  @IsNotEmpty()
  grantorLogoURL: string;

  @ApiProperty({
    example: 'Hedera Hashgraph is a public distributed ledger for building decentralized applications.',
    description: 'The description of the grantor.',
  })
  @IsNotEmpty()
  grantorDescription: string;

  @ApiProperty({
    example: 'https://hedera.com',
    description: 'The website of the grantor.',
  })
  @IsNotEmpty()
  grantorWebsite: string;

  @ApiProperty({
    description: 'The status of the grant program.',
    enum: GrantProgramStageCode,
    enumName: 'GrantProgramStageCode',
    example: GrantProgramStageCode.OPEN,
  })
  @IsNotEmpty()
  status: StageCode;

  @ApiProperty({
    description: 'The grant program coordinator.',
    type: UserDto,
  })
  @IsNotEmpty()
  grantProgramCoordinator: UserDto;

  @ApiProperty({
    example: true,
    description: 'Whether the grant program is able to change stage manually.',
  })
  isAbleToChangeStageManually: boolean;
}

export class GrantProgramWithGrantCallCountDto extends GrantProgramDto {
  @ApiProperty({
    example: 5,
    description: 'The number of grant calls under the grant program.',
  })
  grantCallsCount: number;
}

export class FetchGrantProgramResponse {
  @ApiProperty({
    example: true,
    description: 'Status to indicate the success of the request.',
  })
  success: boolean;

  @ApiProperty({
    type: GrantProgramWithGrantCallCountDto,
    description: 'The grant program.',
  })
  data: GrantProgramWithGrantCallCountDto;
}

export class FetchGrantProgramsResponse {
  @ApiProperty({
    example: true,
    description: 'Status to indicate the success of the request.',
  })
  success: boolean;

  @ApiProperty({
    type: [GrantProgramWithGrantCallCountDto],
    description: 'The array of grant programs.',
  })
  data: GrantProgramWithGrantCallCountDto[];
}
