import sanitizeHtml from 'sanitize-html';

/**
 * Sanitizes all string properties of a DTO by removing any HTML tags and attributes. This includes string arrays. Only
 * considers the "first level" of the DTO, i.e., it does not sanitize nested objects.
 *
 * @param dto The DTO to sanitize
 * @returns The sanitized DTO
 */
export function sanitizeDto<T extends object>(dto: T): T {
  const sanitizedDto = { ...dto };
  for (const key in sanitizedDto) {
    if (typeof sanitizedDto[key] === 'string') {
      sanitizedDto[key as keyof T] = sanitizeInput(sanitizedDto[key] as string) as T[keyof T];
    }
    if (Array.isArray(sanitizedDto[key]) && sanitizedDto[key].every((item) => typeof item === 'string')) {
      sanitizedDto[key as keyof T] = sanitizeStringArray(sanitizedDto[key] as string[]) as T[keyof T];
    }
  }
  return sanitizedDto;
}

export function sanitizeStringArray(array: string[]): string[] {
  return array.map((item) => {
    return sanitizeInput(item);
  });
}

export function sanitizeInput(input: string): string {
  return sanitizeHtml(input, {
    allowedTags: [], // No HTML tags allowed
    allowedAttributes: {}, // No attributes allowed
  });
}
