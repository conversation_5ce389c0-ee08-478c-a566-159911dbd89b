import { slugifyString } from './slugify.util';

describe('Slugify Util', () => {
  // test the slugifyText function
  describe('slugifyText', () => {
    it('should return a slugified string for a small input string', () => {
      const text = 'This is a test string';
      const result = slugifyString(text);
      expect(result).toContain('this-is-a-test-string');
      expect(result.length).toBe(28);
    });

    it('should return a slugified string for a input string larger than the max length', () => {
      const text =
        'This is a test string which is larger than the max length of the slug such that it will be truncated';
      const result = slugifyString(text);
      expect(result).toContain('this-is-a-test-string-which-is-larger-than-the-max-length-of-the-slug-suc');
      expect(result.length).toBe(80);
    });
  });
});
