import { sanitizeDto, sanitizeInput, sanitizeStringArray } from './sanitize.util';

describe('Sanitize Util', () => {
  describe('sanitizeInput', () => {
    it('should completely remove <script> tags and their content', () => {
      const input = 'This is a test string <script> alert("hello") and some other text </script>';
      const result = sanitizeInput(input);
      expect(result).toEqual('This is a test string ');
    });

    it('should remove other tags but not their content', () => {
      const input = 'This is a test string <p>alert("hello")<br/> and </p><a href="http://example.com">other text</a>';
      const result = sanitizeInput(input);
      expect(result).toEqual('This is a test string alert("hello") and other text');
    });
  });

  describe('sanitizeStringArray', () => {
    it('should sanitize all strings in an array', () => {
      const input1 = 'This is a test string <script> alert("hello") and some other text </script>';
      const input2 = 'This is a test string <p>alert("hello")<br/> and </p><a href="http://example.com">other text</a>';
      const input3 = '<h1>This is a <i>test</i> string <p>and</p> <a href="http://example.com">other text</a></h1>';
      const result = sanitizeStringArray([input1, input2, input3]);
      expect(result[0]).toEqual('This is a test string ');
      expect(result[1]).toEqual('This is a test string alert("hello") and other text');
      expect(result[2]).toEqual('This is a test string and other text');
    });
  });

  describe('sanitizeDto', () => {
    it('should sanitize all top level string and string[] properties', () => {
      const input1 = 'This is a test string <script> alert("hello") and some other text </script>';
      const input2 = 'This is a test string <p>alert("hello")<br/> and </p><a href="http://example.com">other text</a>';
      const input3 = '<h1>This is a <i>test</i> string <p>and</p> <a href="http://example.com">other text</a></h1>';

      const input = {
        prop1: input1,
        prop2: input2,
        prop3: [input1, input2, input3],
        prop4: 3000,
        prop5: { prop1: input1, prop2: input2, prop3: input3 },
      };
      const result = sanitizeDto(input);

      expect(result.prop1).toEqual('This is a test string ');
      expect(result.prop2).toEqual('This is a test string alert("hello") and other text');
      expect(result.prop3[0]).toEqual('This is a test string ');
      expect(result.prop3[1]).toEqual('This is a test string alert("hello") and other text');
      expect(result.prop3[2]).toEqual('This is a test string and other text');
      expect(result.prop4).toEqual(3000);
      expect(result.prop5.prop1).toEqual(input1); // Nested objects are not sanitized
    });
  });
});
