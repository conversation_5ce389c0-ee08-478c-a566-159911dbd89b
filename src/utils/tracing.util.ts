import { defaultResource, resourceFromAttributes } from '@opentelemetry/resources';

import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import { ExpressInstrumentation } from '@opentelemetry/instrumentation-express';
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http';
import { NestInstrumentation } from '@opentelemetry/instrumentation-nestjs-core';
import { NodeTracerProvider } from '@opentelemetry/sdk-trace-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { SimpleSpanProcessor } from '@opentelemetry/sdk-trace-base';
import { registerInstrumentations } from '@opentelemetry/instrumentation';

export async function initializeTracing() {
  const resource = defaultResource().merge(
    resourceFromAttributes({
      [ATTR_SERVICE_NAME]: process.env.OTEL_SERVICE_NAME || 'funding-platform-api',
    }),
  );

  // 2) Instantiate the provider with span-processors
  const provider = new NodeTracerProvider({
    resource,
    spanProcessors: [
      new SimpleSpanProcessor(
        new OTLPTraceExporter({
          url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4317',
        }),
      ),
    ],
  });

  // 3) Register it globally
  provider.register();

  // 4) Wire up HTTP, Express and Nest auto-instrumentation
  registerInstrumentations({
    instrumentations: [new HttpInstrumentation(), new ExpressInstrumentation(), new NestInstrumentation()],
  });
}
