import { getNameFromEmail } from './string.util';

describe('getNameFromEmail', () => {
  it('should extract name from email with dot separator', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('<PERSON>');
  });

  it('should extract name from email with underscore separator', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('<PERSON> Doe');
  });

  it('should extract name from email with hyphen separator', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('<PERSON> Doe');
  });

  it('should extract name from email with single word username', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('<PERSON>');
  });

  it('should return null for an invalid email', () => {
    const email = 'invalid-email';
    const result = getNameFromEmail(email);
    expect(result).toBeNull();
  });

  it('should handle emails with mixed cases', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('John Doe');
  });

  it('should handle emails with extra dots', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('John Doe');
  });

  it('should handle emails with extra underscores', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('John Doe');
  });

  it('should handle emails with extra hyphens', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('John Doe');
  });

  it('should return null for a null email', () => {
    const email = null;
    const result = getNameFromEmail(email);
    expect(result).toBeNull();
  });

  it('should return null for an undefined email', () => {
    const email = undefined;
    const result = getNameFromEmail(email);
    expect(result).toBeNull();
  });

  it('should handle emails with other separators', () => {
    const email = '<EMAIL>';
    const result = getNameFromEmail(email);
    expect(result).toBe('John Doe Smith');
  });
});
