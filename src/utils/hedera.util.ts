/**
 * Adds the necessary prefix to the given the message for signing or verifying it with a Hedera account.
 * When signing messages or verifying signatures, the message must be prefixed with '\x19Hedera Signed Message:\n' and
 * the message length.
 *
 * @param message The message to prefix.
 * @returns The prefixed message.
 */
export function prefixMessageToSign(message: string): string {
  return '\x19Hedera Signed Message:\n' + message.length + message;
}
