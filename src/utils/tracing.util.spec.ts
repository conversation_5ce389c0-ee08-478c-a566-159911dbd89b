import { NodeTracerProvider } from '@opentelemetry/sdk-trace-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { SimpleSpanProcessor } from '@opentelemetry/sdk-trace-base';
import { initializeTracing } from './tracing.util';

// Mock OpenTelemetry classes and functions
jest.mock('@opentelemetry/sdk-trace-node', () => ({
  NodeTracerProvider: jest.fn(),
}));

jest.mock('@opentelemetry/sdk-trace-base', () => ({
  SimpleSpanProcessor: jest.fn(),
}));

jest.mock('@opentelemetry/exporter-trace-otlp-http', () => ({
  OTLPTraceExporter: jest.fn(),
}));

describe('initializeTracing', () => {
  it('should initialize OpenTelemetry and register the provider', async () => {
    // Setup environment variables for the test
    process.env.OTEL_SERVICE_NAME = 'test-service';
    process.env.OTEL_EXPORTER_OTLP_ENDPOINT = 'http://localhost:4317';

    // Prepare mocks
    const registerMock = jest.fn();
    const addSpanProcessorMock = jest.fn();

    (NodeTracerProvider as jest.Mock).mockImplementation(() => ({
      register: registerMock,
      addSpanProcessor: addSpanProcessorMock,
    }));

    // Call the function that initializes tracing
    await initializeTracing();

    // Assertions
    expect(NodeTracerProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        resource: expect.objectContaining({
          attributes: expect.objectContaining({
            'service.name': 'test-service',
          }),
        }),
      }),
    );
    expect(OTLPTraceExporter).toHaveBeenCalledWith({
      url: 'http://localhost:4317',
    });
    expect(SimpleSpanProcessor).toHaveBeenCalledWith(expect.any(OTLPTraceExporter));
    expect(registerMock).toHaveBeenCalled();
  });
});
