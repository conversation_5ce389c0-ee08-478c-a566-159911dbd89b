import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

const isUserFilterValidator = (value: string) => {
  const valueAsNumber = parseInt(value, 10);

  if (value !== 'me' && isNaN(valueAsNumber)) {
    return {
      isValid: false,
      error: `User filter should be "me" or User ID`,
    };
  }

  return {
    isValid: true,
    error: ``,
  };
};

export const IsUserFilter = (validationOptions?: ValidationOptions) => {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'isUserFilter',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: string) {
          return isUserFilterValidator(value).isValid;
        },
        defaultMessage(args: ValidationArguments) {
          return isUserFilterValidator(args.value).error;
        },
      },
    });
  };
};
