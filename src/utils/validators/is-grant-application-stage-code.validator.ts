import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  isEnum,
  registerDecorator,
} from 'class-validator';

import { StageCode } from '../../workflow/enums/stage-code.enum';

@ValidatorConstraint({ name: 'isGrantApplicationStageCode', async: false })
export class IsGrantApplicationStageCodeConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    if (typeof value !== 'string') {
      args.constraints[0] = args.constraints[0] ?? {};
      args.constraints[0].message = `$property value '${value}' must be a string.`;
      return false;
    }

    if (!value.startsWith('GA_')) {
      args.constraints[0] = args.constraints[0] ?? {};
      args.constraints[0].message = `$property value '${value}' must start with "GA_".`;
      return false;
    }

    if (!isEnum(value, StageCode)) {
      args.constraints[0] = args.constraints[0] ?? {};
      args.constraints[0].message = `$property value '${value}' is not a valid StageCode.`;
      return false;
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return args.constraints[0]?.message ?? `Each value in $property must be a valid StageCode starting with "GA_".`;
  }
}

export function IsGrantApplicationStageCode(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [{}],
      validator: IsGrantApplicationStageCodeConstraint,
    });
  };
}
