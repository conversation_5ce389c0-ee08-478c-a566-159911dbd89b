import slugify from 'slugify';

const generateRandomString = (length: number) => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

/**
 * Creates a slug from the given string. Replacing spaces with hyphens and adds 6 random characters at the end.
 * E.g., if the input string is "Hello World", the output will be "hello-world-acuHel".
 *
 * @param text the string to slugify
 * @param maxLen the maximum length of the slug
 * @returns the slug
 */
export const slugifyString = (text, maxLen = 80) => {
  let slug = slugify(text, {
    replacement: '-',
    lower: true,
    strict: true,
    trim: true,
  });
  // Leave space for 6 random characters, plus 1 for the hyphen.
  slug = slug.slice(0, maxLen - 7);
  const randomString = generateRandomString(6);
  slug = `${slug}-${randomString}`;
  return slug;
};

export async function generateUniqueSlug(
  name: string,
  doesSlugExist: (slugToCheck: string) => Promise<boolean>,
  defaultBase: string,
): Promise<string> {
  const slugOptions = {
    lower: true,
    strict: true,
    replacement: '-',
    remove: /[*+~.()'"!:@]/g,
  };

  let baseSlug = slugify(name, slugOptions);
  baseSlug = baseSlug.replace(/(?:^-+)|(?:-+$)/g, '');

  if (!baseSlug) {
    baseSlug = defaultBase;
  }

  let slug = baseSlug;
  let counter = 1;

  while (await doesSlugExist(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}
