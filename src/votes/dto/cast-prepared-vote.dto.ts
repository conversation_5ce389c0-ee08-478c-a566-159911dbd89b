import { IsBase64, IsBoolean, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CastPreparedVoteRequestDTO {
  @ApiProperty({ description: 'User signed transaction in base64 format', example: 'ey...' })
  @IsString()
  @IsBase64()
  transaction: string;
}

export class CastVoteResultDTO {
  @ApiProperty({ description: 'Indicates if the vote casting was successful', example: true })
  @IsBoolean()
  @IsNotEmpty()
  success: boolean;
}
