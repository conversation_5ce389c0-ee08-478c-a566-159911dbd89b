import { IsIn, IsString, Matches } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export enum VoteType {
  IN_FAVOR = 'in_favor',
  AGAINST = 'against',
  REMOVE = 'remove',
}

const validVoteTypeValues = Object.values(VoteType);

export class PrepareVoteRequestDTO {
  @ApiProperty({
    description: 'Type of vote (optional for updates)',
    enum: VoteType,
    enumName: 'VoteType',
    required: true,
    example: VoteType.AGAINST,
  })
  @IsIn(validVoteTypeValues, {
    message: `voteType must be one of the following values: ${validVoteTypeValues.map((v) => "'" + v + "'").join(', ')}`,
  })
  voteType: VoteType;

  @ApiProperty({ description: 'Wallet ID', example: '0.0.12345' })
  @Matches(/^\d+\.\d+\.\d+$/, {
    message: 'Invalid wallet ID structure',
  })
  walletId: string;
}

export class PrepareVoteResponseDTO {
  @ApiProperty({ description: 'Signed transaction containing vote information stringified to base64' })
  @IsString()
  transaction: string;
}
