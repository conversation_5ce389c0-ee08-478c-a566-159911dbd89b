import { Controller, Post, Body, Param, ParseIntPipe, UseGuards, UsePipes, ValidationPipe, Get } from '@nestjs/common';
import { VotesService } from './votes.service';
import { CastPreparedVoteRequestDTO, CastVoteResultDTO } from './dto/cast-prepared-vote.dto';
import { AuthGuard } from '../auth/auth.guard';
import { RequestUser, RequestUserPayload } from '../auth/request-user.decorator';
import { PrepareVoteRequestDTO, PrepareVoteResponseDTO } from './dto/prepare-vote.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { GetGrantApplicationVotesResponseDTO } from './dto/get-grant-application-votes-response.dto';

@ApiTags('Votes')
@Controller('applications/:applicationId/votes')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class ApplicationVotesController {
  constructor(private readonly votesService: VotesService) {}

  @Post('prepare')
  @ApiOperation({ summary: 'Prepare a vote for an application' })
  @ApiCreatedResponse({ description: 'Vote prepared successfully', type: PrepareVoteResponseDTO })
  @ApiBadRequestResponse({ description: 'Bad request, invalid input or voting not open' })
  @ApiNotFoundResponse({ description: 'Application not found' })
  @ApiForbiddenResponse({ description: 'Not eligible to vote' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async prepareVote(
    @Param('applicationId', ParseIntPipe) applicationId: number,
    @Body() prepareVoteDto: PrepareVoteRequestDTO,
    @RequestUser() user: RequestUserPayload,
  ): Promise<PrepareVoteResponseDTO> {
    return this.votesService.prepareVote(user, applicationId, prepareVoteDto);
  }

  @Post()
  @ApiOperation({ summary: 'Cast a prepared vote for an application using transaction ID' })
  @ApiCreatedResponse({ description: 'Vote cast successfully', type: CastVoteResultDTO })
  @ApiBadRequestResponse({ description: 'Bad request, invalid input, transaction failure, or voting not open' })
  @ApiNotFoundResponse({ description: 'Application or Vote record not found' })
  @ApiForbiddenResponse({ description: 'Not eligible to vote or unauthorized payer account' })
  @ApiBody({ type: CastPreparedVoteRequestDTO, description: 'Request body containing transaction ID' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async castVote(
    @Param('applicationId', ParseIntPipe) applicationId: number,
    @Body() castPreparedVoteDto: CastPreparedVoteRequestDTO,
  ): Promise<CastVoteResultDTO> {
    await this.votesService.castPreparedVote(applicationId, castPreparedVoteDto.transaction);

    return {
      success: true,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all votes for a specific grant application' })
  @ApiOkResponse({
    description: 'Successfully retrieved votes for application',
    type: GetGrantApplicationVotesResponseDTO,
  })
  @ApiBadRequestResponse({ description: 'Invalid application ID provided' })
  async getVotesForGrantApplication(
    @Param('applicationId', ParseIntPipe) applicationId: number,
  ): Promise<GetGrantApplicationVotesResponseDTO> {
    return this.votesService.getVotesForGrantApplication(applicationId);
  }
}
