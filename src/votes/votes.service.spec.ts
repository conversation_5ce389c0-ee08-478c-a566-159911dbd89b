import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TopicMessageSubmitTransaction, Transaction } from '@hashgraph/sdk';

import { DataSource } from 'typeorm';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { HederaService } from '../hedera/hedera.service';
import { RequestUserPayload } from '../auth/request-user.decorator';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { User } from '../auth/entities/user.entity';
import { VOTE_MESSAGES } from './votes.constants';
import { Vote } from './entities/vote.entity';
import { VoteType } from './dto/prepare-vote.dto';
import { VotesService } from './votes.service';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('VotesService', () => {
  let service: VotesService;

  const mockVoteRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockGrantApplicationService = {
    findGrantApplication: jest.fn(),
    ensureApplicationVotingTopicId: jest.fn(),
  };

  const mockHederaService = {
    prepareSubmitMessageTransaction: jest.fn(),
    executeSignedTransaction: jest.fn(),
    ensureApplicationVotingTopicId: jest.fn(),
  };

  const mockDataSource = {
    createQueryRunner: jest.fn().mockReturnValue({
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        findOne: jest.fn(),
        save: jest.fn(),
      },
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VotesService,
        {
          provide: getRepositoryToken(Vote),
          useValue: mockVoteRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: GrantApplicationService,
          useValue: mockGrantApplicationService,
        },
        {
          provide: HederaService,
          useValue: mockHederaService,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<VotesService>(VotesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('prepareVote', () => {
    const mockUser = { id: 1 };
    const mockApplicationId = 1;
    const mockWalletId = '0.0.12345';

    const mockApplication = {
      id: mockApplicationId,
      voteTopicId: '0.0.123456',
      status: WorkflowStatus.IN_PROGRESS,
      currentStepDefinition: {
        id: 20,
        code: StageCode.GA_QUALIFICATION,
      } as WorkflowStepDefinition,
    };

    const mockUserEntity = {
      id: mockUser.id,
      phoneNumber: '+1234567890',
      isPhoneVerified: true,
      addresses: [mockWalletId],
    };

    const mockVote = {
      grantApplication: { id: mockApplicationId },
      walletsInFavor: [],
      walletsAgainst: [],
      inFavorVotes: 0,
      againstVotes: 0,
    };

    beforeEach(() => {
      mockGrantApplicationService.findGrantApplication.mockResolvedValue(mockApplication);
      mockUserRepository.findOne.mockResolvedValue(mockUserEntity);
      mockVoteRepository.findOne.mockResolvedValue(mockVote);
      mockHederaService.prepareSubmitMessageTransaction.mockResolvedValue('mock-transaction');
      mockGrantApplicationService.ensureApplicationVotingTopicId.mockResolvedValue(mockApplication.voteTopicId);
    });

    it('should prepare a new in-favor vote successfully', async () => {
      const result = await service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
        voteType: VoteType.IN_FAVOR,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction' });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        { voteType: VOTE_MESSAGES.IN_FAVOR },
        mockApplication.voteTopicId,
        mockWalletId,
      );
    });

    it('should prepare a new against vote successfully', async () => {
      const result = await service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
        voteType: VoteType.AGAINST,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction' });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        { voteType: VOTE_MESSAGES.AGAINST },
        mockApplication.voteTopicId,
        mockWalletId,
      );
    });

    it('should prepare a vote change from against to in-favor', async () => {
      mockVoteRepository.findOne.mockResolvedValue({
        ...mockVote,
        walletsAgainst: [mockWalletId],
      });

      const result = await service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
        voteType: VoteType.IN_FAVOR,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction' });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        { voteType: VOTE_MESSAGES.PREVIOUS_AGAINST },
        mockApplication.voteTopicId,
        mockWalletId,
      );
    });

    it('should prepare a vote change from in-favor to against', async () => {
      mockVoteRepository.findOne.mockResolvedValue({
        ...mockVote,
        walletsInFavor: [mockWalletId],
      });

      const result = await service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
        voteType: VoteType.AGAINST,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction' });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        { voteType: VOTE_MESSAGES.PREVIOUS_IN_FAVOR },
        mockApplication.voteTopicId,
        mockWalletId,
      );
    });

    it('should throw BadRequestException when trying to clear a non-existent vote', async () => {
      await expect(
        service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
          voteType: VoteType.REMOVE,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when trying to vote the same way again', async () => {
      mockVoteRepository.findOne.mockResolvedValue({
        ...mockVote,
        walletsInFavor: [mockWalletId],
      });

      await expect(
        service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when voting is not open', async () => {
      mockGrantApplicationService.findGrantApplication.mockResolvedValue({
        data: { ...mockApplication, status: WorkflowStatus.WITHDRAWN },
      });

      await expect(
        service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw ForbiddenException when user has no phone number', async () => {
      mockUserRepository.findOne.mockResolvedValue({
        ...mockUserEntity,
        phoneNumber: null,
      });

      await expect(
        service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException when user phone is not verified', async () => {
      mockUserRepository.findOne.mockResolvedValue({
        ...mockUserEntity,
        isPhoneVerified: false,
      });

      await expect(
        service.prepareVote(mockUser as unknown as RequestUserPayload, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('castPreparedVote', () => {
    const mockApplicationId = 1;
    const mockWalletId = '0.0.12345';
    const mockTransactionBase64 = 'mock-transaction-base64';

    // Create a proper mock that passes instanceof checks
    class MockTopicMessageSubmitTransaction extends TopicMessageSubmitTransaction {
      getMessage = jest.fn().mockReturnValue(Buffer.from(JSON.stringify({ voteType: VoteType.IN_FAVOR })));
    }

    const mockTransaction = new MockTopicMessageSubmitTransaction();

    const mockRecord = {
      transactionId: {
        accountId: {
          toString: jest.fn().mockReturnValue(mockWalletId),
        },
      },
    };

    const mockQueryRunner = {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        findOne: jest.fn(),
        save: jest.fn(),
      },
    };

    beforeEach(() => {
      jest.spyOn(Transaction, 'fromBytes').mockReturnValue(mockTransaction);
      mockHederaService.executeSignedTransaction.mockResolvedValue({ record: mockRecord });
      mockDataSource.createQueryRunner.mockReturnValue(mockQueryRunner);
    });

    it('should add a new in-favor vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(Buffer.from(JSON.stringify({ voteType: VOTE_MESSAGES.IN_FAVOR })));

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        ...initialVote,
        walletsInFavor: [mockWalletId],
        inFavorVotes: 1,
      });
    });

    it('should add a new against vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(Buffer.from(JSON.stringify({ voteType: VOTE_MESSAGES.AGAINST })));

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        ...initialVote,
        walletsAgainst: [mockWalletId],
        againstVotes: 1,
      });
    });

    it('should change vote from in-favor to against and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(Buffer.from(JSON.stringify({ voteType: VOTE_MESSAGES.AGAINST })));

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [],
        walletsAgainst: [mockWalletId],
        inFavorVotes: 0,
        againstVotes: 1,
      });
    });

    it('should change vote from against to in-favor and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [mockWalletId],
        inFavorVotes: 0,
        againstVotes: 1,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(Buffer.from(JSON.stringify({ voteType: VOTE_MESSAGES.IN_FAVOR })));

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      });
    });

    it('should clear an existing in-favor vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(Buffer.from(JSON.stringify({ voteType: VOTE_MESSAGES.REMOVE })));

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      });
    });

    it('should clear an existing against vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [mockWalletId],
        inFavorVotes: 0,
        againstVotes: 1,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(Buffer.from(JSON.stringify({ voteType: VOTE_MESSAGES.REMOVE })));

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      });
    });

    it('should handle multiple wallets voting correctly', async () => {
      const otherWalletId = '0.0.67890';
      const initialVote = {
        walletsInFavor: [otherWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(Buffer.from(JSON.stringify({ voteType: VOTE_MESSAGES.IN_FAVOR })));

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [otherWalletId, mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 2,
        againstVotes: 0,
      });
    });

    it('should throw BadRequestException for invalid transaction signature', async () => {
      mockHederaService.executeSignedTransaction.mockRejectedValue(
        new BadRequestException('Invalid transaction signature'),
      );

      await expect(service.castPreparedVote(mockApplicationId, mockTransactionBase64)).rejects.toThrow(
        BadRequestException,
      );
    });
  });
});
