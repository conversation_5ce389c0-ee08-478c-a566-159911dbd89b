import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PrepareVoteRequestDTO, VoteType } from './dto/prepare-vote.dto';
import { TopicMessageSubmitTransaction, Transaction } from '@hashgraph/sdk';

import { GrantApplicationService } from '../grant-application/grant-application.service';
import { HederaService } from '../hedera/hedera.service';
import { RequestUserPayload } from '../auth/request-user.decorator';
import { User } from '../auth/entities/user.entity';
import { Vote } from './entities/vote.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { VOTE_MESSAGES, VoteMessage } from './votes.constants';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';

@Injectable()
export class VotesService {
  private readonly logger = new Logger(VotesService.name);

  constructor(
    private readonly grantApplicationService: GrantApplicationService,
    @InjectRepository(Vote)
    private readonly voteRepository: Repository<Vote>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly hederaService: HederaService,
    private readonly dataSource: DataSource,
  ) {}

  async prepareVote(
    user: RequestUserPayload,
    applicationId: number,
    { voteType, walletId: accountId }: PrepareVoteRequestDTO,
  ) {
    const application = await this.getApplicationData(applicationId);

    await this.validateGrantApplicationVotingStatus(application);
    await this.validateUserVoteEligibility(user, accountId);

    const voteTopicId = await this.grantApplicationService.ensureApplicationVotingTopicId(application);

    const grantApplicationVotes = await this.voteRepository.findOne({
      where: {
        grantApplication: {
          id: applicationId,
        },
      },
    });

    const existingInFavorVote = grantApplicationVotes.walletsInFavor.includes(accountId);
    const existingAgainstVote = grantApplicationVotes.walletsAgainst.includes(accountId);

    this.validateVoteType(existingInFavorVote, existingAgainstVote, voteType);

    const message = this.generateVoteMessage(existingInFavorVote, existingAgainstVote, voteType);

    const votePayload = {
      voteType: message,
    };

    const transaction = await this.hederaService.prepareSubmitMessageTransaction(votePayload, voteTopicId, accountId);

    return {
      transaction,
    };
  }

  async castPreparedVote(applicationId: number, transactionBase64: string) {
    const transaction = Transaction.fromBytes(Buffer.from(transactionBase64, 'base64'));

    if (!(transaction instanceof TopicMessageSubmitTransaction)) {
      throw new BadRequestException('Invalid transaction type provided.');
    }

    const message = Buffer.from(transaction.getMessage()).toString();
    const { voteType } = JSON.parse(message) as { voteType: VoteMessage };

    const { record } = await this.hederaService.executeSignedTransaction(transaction);
    const walletId = record.transactionId.accountId.toString();

    return this.adjustApplicationVoteCount(applicationId, walletId, voteType);
  }

  async adjustApplicationVoteCount(applicationId: number, walletId: string, voteType: VoteMessage) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const grantApplicationVotes = await queryRunner.manager.findOne(Vote, {
      where: {
        grantApplication: {
          id: applicationId,
        },
      },
      lock: {
        mode: 'pessimistic_write',
      },
    });

    const existingInFavorVoteIndex = grantApplicationVotes.walletsInFavor.indexOf(walletId);
    const existingAgainstVoteIndex = grantApplicationVotes.walletsAgainst.indexOf(walletId);

    if (existingInFavorVoteIndex > -1) {
      grantApplicationVotes.walletsInFavor.splice(existingInFavorVoteIndex, 1);
      grantApplicationVotes.inFavorVotes--;
    } else if (existingAgainstVoteIndex > -1) {
      grantApplicationVotes.walletsAgainst.splice(existingAgainstVoteIndex, 1);
      grantApplicationVotes.againstVotes--;
    }

    if (voteType === VOTE_MESSAGES.IN_FAVOR) {
      grantApplicationVotes.walletsInFavor.push(walletId);
      grantApplicationVotes.inFavorVotes++;
    } else if (voteType === VOTE_MESSAGES.AGAINST) {
      grantApplicationVotes.walletsAgainst.push(walletId);
      grantApplicationVotes.againstVotes++;
    }

    await queryRunner.manager.save(grantApplicationVotes);
    await queryRunner.commitTransaction();
    await queryRunner.release();

    return grantApplicationVotes;
  }

  async getVotesForGrantApplication(applicationId: number): Promise<Pick<Vote, 'inFavorVotes' | 'againstVotes'>> {
    const votesCount = await this.voteRepository.findOne({
      where: {
        grantApplication: { id: applicationId },
      },
      select: ['inFavorVotes', 'againstVotes'],
    });

    if (!votesCount) {
      throw new NotFoundException('Votes not found');
    }

    return votesCount;
  }

  private generateVoteMessage(existingInFavorVote: boolean, existingAgainstVote: boolean, voteType: VoteType) {
    if (voteType === VoteType.REMOVE) {
      return VOTE_MESSAGES.REMOVE;
    } else if (voteType === VoteType.IN_FAVOR && existingAgainstVote) {
      return VOTE_MESSAGES.PREVIOUS_AGAINST;
    } else if (voteType === VoteType.AGAINST && existingInFavorVote) {
      return VOTE_MESSAGES.PREVIOUS_IN_FAVOR;
    } else if (voteType === VoteType.IN_FAVOR) {
      return VOTE_MESSAGES.IN_FAVOR;
    } else if (voteType === VoteType.AGAINST) {
      return VOTE_MESSAGES.AGAINST;
    }

    throw new InternalServerErrorException('Invalid vote type');
  }

  private validateVoteType(existingInFavorVote: boolean, existingAgainstVote: boolean, voteType: VoteType) {
    if (!existingInFavorVote && !existingAgainstVote && voteType === VoteType.REMOVE) {
      throw new BadRequestException('Cannot prepare to clear a vote that does not exist.');
    }

    if (
      (existingInFavorVote && voteType === VoteType.IN_FAVOR) ||
      (existingAgainstVote && voteType === VoteType.AGAINST)
    ) {
      throw new BadRequestException(`You have already voted '${voteType}' for this application.`);
    }
  }

  private async getApplicationData(applicationId: number) {
    const applicationData = await this.grantApplicationService.findGrantApplication(applicationId);
    if (!applicationData) {
      throw new NotFoundException('Application not found');
    }
    return applicationData;
  }

  private async validateGrantApplicationVotingStatus(application) {
    //  **todo: check right stage- StageCode == Qualification**
    if (application.status !== WorkflowStatus.IN_PROGRESS) {
      throw new BadRequestException('Voting is not open for this application.');
    }
  }

  private async validateUserVoteEligibility(userPayload: RequestUserPayload, walletId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userPayload.id },
    });

    if (!user) {
      throw new InternalServerErrorException(
        `User with ID ${userPayload.id} not found while validating vote eligibility.`,
      );
    }

    if (!user.phoneNumber) {
      throw new ForbiddenException('You are not eligible to vote because you have not provided a phone number.');
    }

    if (!user.isPhoneVerified) {
      throw new ForbiddenException('You are not eligible to vote because your phone number is not verified.');
    }

    if (!user.addresses.some((address) => address === walletId)) {
      throw new ForbiddenException('You are not eligible to vote because you do not have access to this wallet.');
    }

    this.logger.log(`User ${userPayload.id} is eligible for voting (phone KYC verified).`);
  }
}
