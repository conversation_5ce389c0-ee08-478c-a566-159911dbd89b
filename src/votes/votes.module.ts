import { ApplicationVotesController } from './application-votes.controller';
import { AuthModule } from '../auth/auth.module';
import { GrantApplicationModule } from '../grant-application/grant-application.module';
import { HederaModule } from '../hedera/hedera.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
import { Vote } from './entities/vote.entity';
import { VotesService } from './votes.service';

@Module({
  imports: [TypeOrmModule.forFeature([Vote, User]), AuthModule, HederaModule, GrantApplicationModule],
  controllers: [ApplicationVotesController],
  providers: [VotesService],
})
export class VotesModule {}
