import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { GrantApplication } from '../../grant-application/entities/grant-application.entity';

@Entity('votes')
export class Vote {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(() => GrantApplication)
  @JoinColumn()
  grantApplication: GrantApplication;

  @Column({ type: 'int', default: 0 })
  inFavorVotes: number;

  @Column({ type: 'int', default: 0 })
  againstVotes: number;

  @Column({ type: 'varchar', array: true })
  walletsInFavor: string[];

  @Column({ type: 'varchar', array: true })
  walletsAgainst: string[];

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}
