import { Grant<PERSON>allStageSetting } from './entities/grant-call-stage-setting.entity';
import { StageCode } from '../workflow/enums/stage-code.enum';

export type GrantCallUrlDtoKey = (typeof STAGE_URL_DTO_KEYS)[keyof typeof STAGE_URL_DTO_KEYS];
export type GrantCallTimingProperties = Partial<
  Pick<GrantCallStageSetting, 'startDate' | 'endDate' | 'durationSeconds'>
>;

export const REQUIRED_TIMING_STAGES: StageCode[] = [
  StageCode.GC_OPEN_FOR_APPLICATIONS,
  StageCode.GC_COMMUNITY_VOTING,
  StageCode.GC_FINAL_COMMUNITY_VOTING,
];
export const URL_CONFIGURABLE_STAGES: StageCode[] = [
  StageCode.GA_SCREENING,
  StageCode.GA_DUE_DILIGENCE,
  StageCode.GA_INTERVIEW,
  StageCode.GA_TOWN_HALL,
];

export enum StageTimingType {
  DATE_RANGE,
  DURATION,
}

export const STAGE_TIMING_EXPECTATION: Partial<Record<StageCode, StageTimingType>> = {
  [StageCode.GC_OPEN_FOR_APPLICATIONS]: StageTimingType.DATE_RANGE,
  [StageCode.GC_COMMUNITY_VOTING]: StageTimingType.DURATION,
  [StageCode.GC_FINAL_COMMUNITY_VOTING]: StageTimingType.DURATION,
};

export const STAGE_URL_DTO_KEYS = {
  [StageCode.GA_SCREENING]: 'screeningFormUrl',
  [StageCode.GA_DUE_DILIGENCE]: 'dueDiligenceFormUrl',
  [StageCode.GA_INTERVIEW]: 'interviewSchedulingUrl',
  [StageCode.GA_TOWN_HALL]: 'townHallSchedulingUrl',
} as const;

export type GrantCallTimingSettings = {
  openForApplicationStart: string | null;
  openForApplicationEnd: string | null;
  communityVotingTime1: number | null;
  communityVotingTime2: number | null;
};

export const ALL_POSSIBLE_SETTING_STAGES = Array.from(
  new Set([
    ...REQUIRED_TIMING_STAGES,
    ...(Object.keys(STAGE_URL_DTO_KEYS).filter((k) => !!STAGE_URL_DTO_KEYS[k as StageCode]) as StageCode[]),
  ]),
);

export const BULK_ACTION_APP_SOURCE_MAP: Partial<Record<StageCode, StageCode[]>> = {
  [StageCode.GC_ONBOARDING]: [StageCode.GA_DUE_DILIGENCE, StageCode.GA_TOWN_HALL],
  [StageCode.GC_SCREENING]: [StageCode.GA_SCREENING],
  [StageCode.GC_COMMUNITY_VOTING]: [StageCode.GA_QUALIFICATION],
  [StageCode.GC_FINAL_COMMUNITY_VOTING]: [StageCode.GA_FINAL_QUALIFICATION],
};
