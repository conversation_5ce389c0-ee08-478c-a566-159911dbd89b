import { AuthModule } from '../auth/auth.module';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantApplicationModule } from '../grant-application/grant-application.module';
import { GrantCall } from './entities/grant-call.entity';
import { Grant<PERSON>allController } from './grant-call.controller';
import { GrantCallMapper } from './grant-call.mapper';
import { GrantCallService } from './grant-call.service';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { MailModule } from '../notifications/mail/mail.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';

@Module({
  imports: [
    AuthModule,
    GrantApplicationModule,
    MailModule,
    TypeOrmModule.forFeature([
      GrantCall,
      WorkflowStepDefinition,
      WorkflowTemplate,
      WorkflowState,
      GrantProgram,
      GrantApplication,
    ]),
  ],
  controllers: [GrantCallController],
  providers: [GrantCallService, GrantCallMapper, WorkflowTransitionService, WorkflowService],
  exports: [GrantCallMapper],
})
export class GrantCallModule {}
