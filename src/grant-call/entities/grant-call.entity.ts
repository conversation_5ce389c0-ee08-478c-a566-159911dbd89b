import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { BusinessCategory } from '../enums/business-category.enum';
import { GrantApplication } from '../../grant-application/entities/grant-application.entity';
import { GrantCallStageSetting } from './grant-call-stage-setting.entity';
import { GrantDistributionRule } from './grant-distribution-rule.entity';
import { GrantProgram } from '../../grant-program/entities/grant-program.entity';
import { NumericColumnTransformer } from '../../database/column-transformers';
import { User } from '../../auth/entities/user.entity';
import { WorkflowState } from '../../workflow/entities/workflow-state.entity';

@Entity()
export class GrantCall {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true })
  grantCallSlug: string;

  @ManyToOne(() => GrantProgram, (grantProgram) => grantProgram.grantCalls)
  grantProgram: GrantProgram;

  @Column()
  name: string;

  @Column()
  description: string;

  @Column()
  businessCategory: BusinessCategory;

  @Column('simple-array', { nullable: true })
  categories: string[] | null;

  @Column({
    type: 'decimal',
    precision: 18,
    scale: 2,
    nullable: true,
    transformer: new NumericColumnTransformer(),
  })
  totalGrantAmount: number | null;

  @Column({ type: 'int', nullable: false })
  createdById: number;

  @ManyToOne(() => User, {
    nullable: false,
    onDelete: 'RESTRICT',
    eager: false,
  })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'int', nullable: true })
  workflowStateId: number | null;

  @OneToOne(() => WorkflowState, {
    nullable: true,
    onDelete: 'RESTRICT',
    eager: false,
  })
  @JoinColumn({ name: 'workflowStateId' })
  workflowState: WorkflowState | null;

  @OneToMany(() => GrantCallStageSetting, (setting) => setting.grantCall, {
    cascade: true,
    eager: false,
  })
  stageSettings: GrantCallStageSetting[];

  @OneToMany(() => GrantDistributionRule, (rule) => rule.grantCall, {
    cascade: true,
    eager: false,
  })
  distributionRules: GrantDistributionRule[];

  @OneToMany(() => GrantApplication, (application) => application.grantCall)
  grantApplications: GrantApplication[];

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
