import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

import { DistributionType } from '../enums/distribution-type.enum';
import { GrantCall } from './grant-call.entity';
import { NumericColumnTransformer } from '../../database/column-transformers'; // Adjust path if needed

@Entity('grant_distribution_rules')
@Index(['grantCallId', 'rank'], { unique: true })
export class GrantDistributionRule {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'int' })
  grantCallId: number;

  @ManyToOne(() => GrantCall, (gc) => gc.distributionRules, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  @JoinColumn()
  grantCall: GrantCall;

  @Column({ type: 'int' })
  rank: number;

  @Column({ type: 'enum', enum: DistributionType, enumName: 'DistributionType' })
  type: DistributionType;

  @Column({
    type: 'decimal',
    precision: 18,
    scale: 2,
    transformer: new NumericColumnTransformer(),
  })
  value: number;
}
