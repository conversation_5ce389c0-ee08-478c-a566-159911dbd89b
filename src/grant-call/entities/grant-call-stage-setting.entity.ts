import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

import { GrantCall } from './grant-call.entity';
import { WorkflowStepDefinition } from '../../workflow/entities/workflow-step-definition.entity';

@Entity('grant_call_stage_settings')
@Index(['grantCallId', 'workflowStepDefinitionId'], { unique: true })
export class GrantCallStageSetting {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'int' })
  grantCallId: number;

  @ManyToOne(() => GrantCall, (gc) => gc.stageSettings, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  @JoinColumn()
  grantCall: GrantCall;

  @Column({ type: 'int' })
  workflowStepDefinitionId: number;

  @ManyToOne(() => WorkflowStepDefinition, {
    onDelete: 'RESTRICT',
    nullable: false,
    eager: true,
  })
  @Join<PERSON>olumn({ name: 'workflowStepDefinitionId' })
  workflowStepDefinition: WorkflowStepDefinition;

  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  startDate: Date | null;

  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  endDate: Date | null;

  @Column({
    type: 'integer',
    nullable: true,
  })
  durationSeconds: number | null;

  @Column({
    type: 'text',
    nullable: true,
  })
  stageUrl: string | null;
}
