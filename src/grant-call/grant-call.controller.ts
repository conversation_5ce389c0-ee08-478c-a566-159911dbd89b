import { Body, Controller, Get, HttpStatus, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { Roles } from '../auth/role.decorator';
import { RolesGuard } from '../auth/role.guard';
import {
  CreateGrantCallDto,
  GrantCallDetailResponseDto,
  FindOneGrantCallResponseDto,
  UpdateGrantCallDto,
  ConditionalBulkTransitionResponseDto,
} from './dto';
import { GrantCallService } from './grant-call.service';
import { RequestUser, RequestUserPayload } from '../auth/request-user.decorator';
import { StageWithCountResponseDto } from '../workflow/dto/stage-with-count.response.dto';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { GrantCallApplicationStagesResponseDto } from './dto/grant-call-application-stage-response.dto';
import { BulkTransitionResponseDto } from '../workflow/dto/bulk-transition-response.dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { FindGrantApplicationResponseDto, GetGrantApplicationsQueryDto } from '../grant-application/dto';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { FinalizeGrantCallResponseDto } from './dto/finalize-grant-call-response.dto';

@ApiTags('Grant Call')
@Controller('grant-call')
export class GrantCallController {
  constructor(
    private readonly grantCallService: GrantCallService,
    private readonly grantApplicationService: GrantApplicationService,
  ) {}

  @ApiOperation({ summary: 'Get the defined workflow stage definitions for Grant Calls' })
  @ApiResponse({
    status: 200,
    description: 'List of stage definitions for Grant Calls.',
    type: [StageDefinitionResponseDto],
  })
  @Get('/stages')
  getGrantApplicationStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return this.grantCallService.getCallStageDefinitions();
  }

  @ApiOperation({ summary: 'Get Grant Call workflow stages with counts of calls in each stage.' })
  @ApiResponse({ status: 200, type: [StageWithCountResponseDto] })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Get('/stage-counts')
  geGrantCallStageCounts(): Promise<StageWithCountResponseDto[]> {
    return this.grantCallService.getCallStagesWithCounts();
  }

  @ApiOperation({ summary: 'Get application stage summaries (with counts) for a specific grant call.' })
  @ApiResponse({
    status: 200,
    description: 'Application stage summaries retrieved successfully.',
    type: GrantCallApplicationStagesResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Grant Call not found.' })
  @Get(':slug/application-stage-summaries')
  async getApplicationStageSummaries(@Param('slug') slug: string): Promise<GrantCallApplicationStagesResponseDto> {
    return this.grantCallService.getApplicationStageSummaries(slug);
  }

  @ApiResponse({
    status: 201,
    type: GrantCallDetailResponseDto,
    description: 'Grant Call created successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'When the given grant program, the coordinator or members are not found.' })
  @ApiOperation({ summary: 'Create a new grant call' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post()
  create(
    @Body() createGrantCallDto: CreateGrantCallDto,
    @RequestUser() user: RequestUserPayload,
  ): Promise<GrantCallDetailResponseDto> {
    return this.grantCallService.create(createGrantCallDto, user.id);
  }

  @ApiResponse({
    status: 200,
    description: 'The grant call has been successfully retrieved.',
    type: FindOneGrantCallResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  @ApiOperation({ summary: "Get a grant call by it's slug" })
  @Get(':slug')
  findOne(@Param('slug') slug: string): Promise<FindOneGrantCallResponseDto> {
    return this.grantCallService.findOne(slug);
  }

  @ApiResponse({
    status: 200,
    type: GrantCallDetailResponseDto,
    description: 'The grant call has been successfully updated.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  @ApiOperation({ summary: 'Update a grant call' })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Patch(':slug')
  update(
    @Param('slug') slug: string,
    @Body() updateGrantCall: UpdateGrantCallDto,
  ): Promise<GrantCallDetailResponseDto> {
    return this.grantCallService.update(slug, updateGrantCall);
  }

  @ApiResponse({
    status: 200,
    description: 'The applications were successfully retrieved.',
    type: [FindGrantApplicationResponseDto],
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'If the grant call is not found.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'In case of invalid query params.' })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant call.',
    example: 'grant-call-1',
  })
  @ApiBearerAuth()
  @Get(':slug/grant-applications')
  getGrantApplications(
    @Param('slug') slug: string,
    @Query()
    filterOptions: GetGrantApplicationsQueryDto,
    @RequestUser() user: RequestUserPayload,
  ): Promise<FindGrantApplicationResponseDto[]> {
    return this.grantApplicationService.findGrantApplications({ grantCallSlug: slug, ...filterOptions }, user?.id);
  }

  @ApiOperation({ summary: 'Advance Grant Call to the next workflow stage' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant Call successfully transitioned.',
    type: SingleTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or its workflow state not found.' })
  @ApiResponse({ status: HttpStatus.UNPROCESSABLE_ENTITY, description: 'terminal state' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post(':slug/transition')
  async transitionGrantCall(@Param('slug') slug: string): Promise<SingleTransitionResponseDto> {
    return this.grantCallService.transitionGrantCallToNextStep(slug);
  }

  @ApiOperation({ summary: "Bulk transition 'Ready' Applications FROM Due Diligence stage" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Bulk transition processed successfully.',
    type: BulkTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required Step Definition not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Grant Call not in expected stage or target App stage missing.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post(':slug/applications/bulk-transition-ready-due-diligence')
  async bulkTransitionReadyDueDiligenceApps(@Param('slug') slug: string): Promise<BulkTransitionResponseDto> {
    return await this.grantCallService.bulkTransitionReadyApplicationsAtDueDiligence(slug);
  }

  @ApiOperation({
    summary: 'Advance Grant Call from Screening to Community Voting.',
    description:
      "Advances a Grant Call from the 'Screening' stage to 'Community Voting' if all its applications " +
      "currently in 'Screening' are 'Screening Ready'. " +
      "Upon successful advancement, these applications will move to the 'Qualification' stage.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant call and applications successfully advanced from Screening.',
    type: ConditionalBulkTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow definitions not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Transition rule not met (e.g., not all applications ready, or call not in Screening stage).',
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post(':slug/advance-from-screening')
  async advanceGrantCallFromScreening(@Param('slug') slug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return this.grantCallService.advanceGrantCallFromScreening(slug);
  }

  @ApiOperation({
    summary: 'Advance Grant Call from Onboarding to Final Community Voting.',
    description:
      'Advances a Grant Call from the (GC_ONBOARDING) stage to (GC_FINAL_COMMUNITY_VOTING) ' +
      "if all its applications currently in (GA_TOWN_HALL) are 'Town Hall Ready'. " +
      'Upon successful advancement, these applications will move to the (GA_FINAL_QUALIFICATION) stage.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant call and applications successfully advanced from Onboarding.',
    type: ConditionalBulkTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow definitions not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Transition rule not met (e.g., not all applications ready, or call not in Onboarding stage).',
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post(':slug/advance-from-onboarding')
  async advanceGrantCallFromOnboarding(@Param('slug') slug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return this.grantCallService.advanceGrantCallFromOnboarding(slug);
  }

  @ApiOperation({
    summary: 'Advance Grant Call from Community Voting to Onboarding.',
    description:
      "Advances a Grant Call from 'Community Voting' to 'Onboarding' if all its applications " +
      "in 'Qualification' are 'Ready'. Applications will move to 'Interview'.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant call and applications successfully advanced from Community Voting.',
    type: ConditionalBulkTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow definitions not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description:
      "Transition rule not met (e.g., not all applications ready, or grant call not in 'Community Voting' stage).",
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post(':slug/advance-from-community-voting')
  async advanceGrantCallFromCommunityVoting(
    @Param('slug') slug: string,
  ): Promise<ConditionalBulkTransitionResponseDto> {
    return this.grantCallService.advanceGrantCallFromCommunityVoting(slug);
  }

  @ApiOperation({
    summary: 'Process final applications and advance Grant Call to its finalized stage.',
    description:
      'Processes applications in the final qualification stage ' +
      'and then transitions the Grant Call to its next designated stage (typically GC_FINALIZED).',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant Call finalized and relevant applications processed.',
    type: FinalizeGrantCallResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow definitions not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post(':slug/finalize')
  async finalizeGrantCall(@Param('slug') slug: string): Promise<FinalizeGrantCallResponseDto> {
    return this.grantCallService.processFinalApplicationsAndFinalizeCall(slug);
  }
}
