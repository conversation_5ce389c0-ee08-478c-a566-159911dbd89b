import { IsNotEmpty, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { GrantCallBaseDto } from './grant-call-base.dto';

export class CreateGrantCallDto extends GrantCallBaseDto {
  @ApiProperty({
    description: 'The slug of the grant program this grant call should belong to.',
    example: 'my-awesome-grant-program-hdiow',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  grantProgramSlug: string;
}
