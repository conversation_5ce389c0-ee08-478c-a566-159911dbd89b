import { Grant<PERSON>allStageCode, StageCode } from '../../workflow/enums/stage-code.enum';

import { ApiProperty } from '@nestjs/swagger';

export class FinalizeGrantCallResponseDto {
  @ApiProperty({
    description: 'The number of grant applications whose status was updated during the finalization process.',
    example: 5,
    type: Number,
  })
  applicationsStatusUpdatedCount: number;

  @ApiProperty({
    description: 'The new stage the Grant Call has moved to after successful finalization (e.g., GC_FINALIZED).',
    enum: StageCode,
    enumName: 'GrantCallStageCode',
    example: GrantCallStageCode.FINALIZED,
  })
  grantCallNewStage: StageCode;
}
