import { ApiProperty } from '@nestjs/swagger';
import { StageWithCountResponseDto } from '../../workflow/dto/stage-with-count.response.dto';

export class GrantCallApplicationStagesResponseDto {
  @ApiProperty({
    example: 'research-grant-stellar-probes-xyz',
    description: 'The unique slug of the Grant Call these stages belong to.',
  })
  grantCallSlug: string;

  @ApiProperty({
    example: 42,
    description: 'The total number of applications submitted to this grant call across all stages.',
  })
  totalApplications: number;

  @ApiProperty({
    type: [StageWithCountResponseDto],
    description:
      'An array summarizing each relevant application workflow stage and the count of applications within it for this grant call.',
  })
  stages: StageWithCountResponseDto[];
}
