import { ApiProperty } from '@nestjs/swagger';
import { BulkTransitionResponseDto } from '../../workflow/dto/bulk-transition-response.dto';
import { GrantCallStageCode } from '../../workflow/enums/stage-code.enum';

export class ConditionalBulkTransitionResponseDto extends BulkTransitionResponseDto {
  @ApiProperty({
    description: 'The new stage the Grant Call has moved to after successful transition.',
    enum: GrantCallStageCode,
    enumName: 'GrantCallStageCode',
    example: GrantCallStageCode.COMMUNITY_VOTING,
  })
  grantCallNewStage: GrantCallStageCode;
}
