import { ApiProperty } from '@nestjs/swagger';
import { BusinessCategory } from '../enums/business-category.enum';
import { IsNotEmpty } from 'class-validator';
import { SimpleGrantProgramDto } from '../../grant-program/dto';
import { UserDto } from '../../auth/dto';

export class GrantCallDto {
  @ApiProperty({
    example: 1,
    description: 'The uniquely identifying of the grant call.',
  })
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    example: 'awesome-organization-grant-call-jdjaij',
    description: 'The uniquely identifying slug of the grant call.',
  })
  @IsNotEmpty()
  grantCallSlug: string;

  @ApiProperty({
    example: 'Test Grant Call',
    description: 'The name of the grant call.',
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'This is our orgs first grant call.',
    description: 'The description of the grant call.',
  })
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    example: BusinessCategory.STARTUP,
    enum: BusinessCategory,
    enumName: 'BusinessCategory',
    description: 'The business category of the grant call. Can be either STARTUP, ENTERPRISE or GOVERNMENT.',
  })
  @IsNotEmpty()
  businessCategory: BusinessCategory;

  @ApiProperty({
    example: ['Healthcare', 'Technology'],
    description: 'The industries that the grant call is targeting.',
  })
  @IsNotEmpty()
  targetIndustries: string[];

  @ApiProperty({
    example: '2021-09-01T00:00:00.000Z',
    description: 'The start date of the grant call in ISO 8601 format (date, time, time zone always UTC).',
  })
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty({
    example: '2021-09-30T00:00:00.000Z',
    description: 'The end date of the grant call in ISO 8601 format (date, time, time zone always UTC)..',
  })
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({
    example: '2021-09-01T00:00:00.000Z',
    description: 'The last updated date of the grant call in ISO 8601 format (date, time, time zone always UTC).',
  })
  @IsNotEmpty()
  updatedAt: Date;

  @ApiProperty({
    example: 1000,
    description: 'The minimum grant size of the grant call.',
  })
  @IsNotEmpty()
  minGrantSize: number;

  @ApiProperty({
    example: 10000,
    description: 'The maximum grant size of the grant call.',
  })
  @IsNotEmpty()
  maxGrantSize: number;

  @ApiProperty({
    type: UserDto,
    description: 'The grant call coordinator.',
  })
  @IsNotEmpty()
  grantCallCoordinator: UserDto;

  @ApiProperty({
    type: () => SimpleGrantProgramDto,
    description: 'The grant program that the grant call belongs to.',
  })
  @IsNotEmpty()
  grantProgram: SimpleGrantProgramDto;

  @ApiProperty({
    example: 50,
    description: 'Count of applications for grant call.',
  })
  @IsNotEmpty()
  grantApplicationsCount: number;
}

export class AuthenticatedGrantCallDto extends GrantCallDto {
  @ApiProperty({
    example: null,
    description: 'The grant program is not part of this response.',
  })
  @IsNotEmpty()
  grantProgram: null;
}
