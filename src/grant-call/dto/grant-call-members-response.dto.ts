import { ApiProperty } from '@nestjs/swagger';
import { UserDto } from '../../auth/dto';
import { IsNotEmpty } from 'class-validator';

export class GrantCallMember extends UserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the grant call member.',
  })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: true,
    description: 'If the member is the grant call coordinator.',
  })
  @IsNotEmpty()
  isCoordinator: boolean;
}

export class GrantCallMembersResponse {
  @ApiProperty({
    example: true,
    description: 'The success status of the response.',
  })
  @IsNotEmpty()
  success: boolean;

  @ApiProperty({
    type: [GrantCallMember],
    description: 'The grant call members.',
  })
  @IsNotEmpty()
  data: GrantCallMember[];
}
