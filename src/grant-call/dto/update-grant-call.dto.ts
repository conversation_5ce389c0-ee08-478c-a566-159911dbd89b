import { PickType } from '@nestjs/swagger';
import { GrantCallBaseDto } from './grant-call-base.dto';

/**
 * DTO for editing a grant call
 * This extends the UpdateGrantCallDto so that all fields available on creation are also editable.
 */
export class UpdateGrantCallDto extends PickType(GrantCallBaseDto, [
  'name',
  'description',
  'screeningFormUrl',
  'dueDiligenceFormUrl',
  'interviewSchedulingUrl',
  'townHallSchedulingUrl',
]) {}
