import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { UpdateGrantCallDto } from '../update-grant-call.dto';

describe('UpdateGrantCall DTO', () => {
  it('should validate successfully with valid input', async () => {
    const validInput = {
      name: 'Valid Update Grant Call',
      description: 'Valid description for update.',
      screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
      dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
      interviewSchedulingUrl: 'https://calendly.com/your-org/30min',
      townHallSchedulingUrl: 'https://calendly.com/your-org/town-hall-q1',
    };

    const dto = plainToClass(UpdateGrantCallDto, validInput);
    const errors = await validate(dto);

    expect(errors.length).toBe(0);
  });

  it('should fail validation if name exceeds maximum length', async () => {
    const input = {
      name: 'a'.repeat(121),
    };

    const dto = plainToClass(UpdateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'name')).toBe(true);
  });

  it('should fail validation if description is not a string', async () => {
    const input = {
      description: 123,
      name: 'Valid Update Grant Call',
      screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
      dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
    };

    const dto = plainToClass(UpdateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'description')).toBe(true);
  });
});
