import { BusinessCategory } from '../../enums/business-category.enum';
import { CreateGrantCallDto } from '../create-grant-call.dto';
import { GrantCategory } from '../../enums/grant-category.enum';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

describe('CreateGrantCall DTO', () => {
  it('should validate successfully with valid input', async () => {
    const validInput = {
      grantProgramSlug: 'my-awesome-grant-program-hdiowl',
      name: 'Valid Update Grant Call',
      description: 'Valid description for update.',
      businessCategory: BusinessCategory.ENTERPRISE,
      categories: [GrantCategory.DIGITAL_IDENTITY],
      totalGrantAmount: 120000.5,
      openForApplicationStart: '2024-10-01T00:00:00Z',
      openForApplicationEnd: '2024-10-31T23:59:59Z',
      communityVotingTime1: 604800,
      communityVotingTime2: 259200,
      grantDistribution: [60, 25, 15],
      screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
      dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
      interviewSchedulingUrl: 'https://calendly.com/your-org/30min',
      townHallSchedulingUrl: 'https://calendly.com/your-org/town-hall-q1',
    };

    const dto = plainToClass(CreateGrantCallDto, validInput);
    const errors = await validate(dto);

    expect(errors.length).toBe(0);
  });

  it('should fail validation if grantProgramSlug is missing', async () => {
    const input = {
      name: 'Grant Call 1',
      description: 'This is a grant call',
    };

    const dto = plainToClass(CreateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('grantProgramSlug');
  });

  it('should fail validation if name is missing', async () => {
    const input = {
      grantProgramSlug: 'my-awesome-grant-program-hdiowl',
      description: 'This is a grant call',
    };

    const dto = plainToClass(CreateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'name')).toBe(true);
  });

  it('should fail validation if description is missing', async () => {
    const input = {
      grantProgramSlug: 'my-awesome-grant-program-hdiowl',
      name: 'Grant Call 1',
    };

    const dto = plainToClass(CreateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'description')).toBe(true);
  });

  it('should fail validation if name exceeds maximum length', async () => {
    const input = {
      grantProgramSlug: 'my-awesome-grant-program-hdiowl',
      name: 'a'.repeat(121),
      description: 'This is a grant call',
    };

    const dto = plainToClass(CreateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'name')).toBe(true);
  });

  it('should validate successfully when screeningFormUrl is omitted', async () => {
    const input = {
      grantProgramSlug: 'my-awesome-grant-program-hdiowl',
      name: 'Grant Call 1',
      description: 'This is a grant call',
      businessCategory: BusinessCategory.ENTERPRISE,
      categories: [GrantCategory.DIGITAL_IDENTITY],
      totalGrantAmount: 120000.5,
      openForApplicationStart: '2024-10-01T00:00:00Z',
      openForApplicationEnd: '2024-10-31T23:59:59Z',
      communityVotingTime1: 604800,
      communityVotingTime2: 259200,
      grantDistribution: [60, 25, 15],
      dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
    };

    const dto = plainToClass(CreateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'screeningFormUrl')).toBe(true);
  });

  it('should fail validation when dueDiligenceFormUrl is omitted', async () => {
    const input = {
      grantProgramSlug: 'my-awesome-grant-program-hdiowl',
      name: 'Grant Call 1',
      description: 'This is a grant call',
      businessCategory: BusinessCategory.ENTERPRISE,
      categories: [GrantCategory.DIGITAL_IDENTITY],
      totalGrantAmount: 120000.5,
      openForApplicationStart: '2024-10-01T00:00:00Z',
      openForApplicationEnd: '2024-10-31T23:59:59Z',
      communityVotingTime1: 604800,
      communityVotingTime2: 259200,
      grantDistribution: [60, 25, 15],
      screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
    };

    const dto = plainToClass(CreateGrantCallDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'dueDiligenceFormUrl')).toBe(true);
  });

  it('should validate successfully with optional interview and town hall URLs missing', async () => {
    const validInput = {
      grantProgramSlug: 'my-awesome-grant-program-hdiowl',
      name: 'Valid Update Grant Call',
      description: 'Valid description for update.',
      businessCategory: BusinessCategory.ENTERPRISE,
      categories: [GrantCategory.DIGITAL_IDENTITY],
      totalGrantAmount: 120000.5,
      openForApplicationStart: '2024-10-01T00:00:00Z',
      openForApplicationEnd: '2024-10-31T23:59:59Z',
      communityVotingTime1: 604800,
      communityVotingTime2: 259200,
      grantDistribution: [60, 25, 15],
      screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
      dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
      // interviewSchedulingUrl and townHallSchedulingUrl are intentionally omitted
    };

    const dto = plainToClass(CreateGrantCallDto, validInput);
    const errors = await validate(dto);

    expect(errors.length).toBe(0);
  });
});
