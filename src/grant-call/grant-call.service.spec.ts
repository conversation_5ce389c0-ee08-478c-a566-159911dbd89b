import {
  ConditionalBulkTransitionResponseDto,
  CreateGrantCallDto,
  FindOneGrantCallResponseDto,
  GrantCallBaseDto,
} from './dto';
import { GrantCallStageCode, StageCode } from '../workflow/enums/stage-code.enum';
import { Test, TestingModule } from '@nestjs/testing';

import { ALL_POSSIBLE_SETTING_STAGES } from './grant-call.constants';
import { BusinessCategory } from './enums/business-category.enum';
import { ConfigService } from '@nestjs/config';
import { DistributionType } from './enums/distribution-type.enum';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantCall } from './entities/grant-call.entity';
import { GrantCallMapper } from './grant-call.mapper';
import { GrantCallService } from './grant-call.service';
import { GrantCallStageSetting } from './entities/grant-call-stage-setting.entity';
import { GrantCategory } from './enums/grant-category.enum';
import { GrantDistributionRule } from './entities/grant-distribution-rule.entity';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { MailService } from '../notifications/mail/mail.service';
import { Repository } from 'typeorm';
import { User } from '../auth/entities/user.entity';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { generateUniqueSlug } from '../utils/slugify.util';
import { getRepositoryToken } from '@nestjs/typeorm';

jest.mock('../utils/slugify.util');

const mockedGenerateUniqueSlug = generateUniqueSlug as jest.MockedFunction<
  (name: string, checker: (s: string) => Promise<boolean>, defaultBase?: string) => Promise<string>
>;

const mockGrantProgram = (): GrantProgram =>
  ({
    id: 1,
    grantProgramSlug: 'test-program-slug',
    name: 'Test Program',
    grantCalls: [],
    workflowState: {
      currentStepDefinition: {
        code: StageCode.GP_OPEN,
      },
    },
    grantProgramCoordinator: {
      id: 1,
      displayName: 'Test Coordinator',
    },
  }) as GrantProgram;

const mockUser = (): User => ({ id: 101, email: '<EMAIL>' }) as User;

const mockWorkflowStepDef = (id: number, code: StageCode): WorkflowStepDefinition =>
  ({ id, code, name: `Step ${code}` }) as WorkflowStepDefinition;

const mockGrantCallStageSetting = (idSuffix: number, wfId: number): GrantCallStageSetting =>
  ({
    id: 500 + idSuffix,
    grantCallId: 5,
    grantCall: {} as GrantCall,
    workflowStepDefinitionId: wfId,
    workflowStepDefinition: {} as WorkflowStepDefinition,
    startDate: new Date('2024-01-10'),
    endDate: null,
    durationSeconds: null,
    stageUrl: null,
  }) as GrantCallStageSetting;

const mockGrantDistributionRule = (idSuffix: number, rank: number, value: number): GrantDistributionRule =>
  ({
    id: 600 + idSuffix,
    grantCallId: 5,
    grantCall: {} as GrantCall,
    rank,
    type: DistributionType.PERCENTAGE,
    value,
  }) as GrantDistributionRule;

const mockGrantCall = (id: number, slug: string, existingRelations: boolean = false): GrantCall =>
  ({
    id,
    grantCallSlug: slug,
    name: 'Original Grant Call',
    description: 'Original Description',
    businessCategory: BusinessCategory.STARTUP,
    categories: [GrantCategory.CONSUMER_LOYALTY],
    totalGrantAmount: 50000,
    grantProgramId: 1,
    grantProgram: mockGrantProgram(),
    createdById: mockUser().id,
    createdBy: mockUser(),
    stageSettings: existingRelations ? [mockGrantCallStageSetting(1, 10), mockGrantCallStageSetting(2, 20)] : [],
    distributionRules: existingRelations ? [mockGrantDistributionRule(1, 1, 60)] : [],
    createdAt: new Date(),
    updatedAt: new Date(),
    workflowStateId: null,
    workflowState: null,
    applicationStages: [],
  }) as unknown as GrantCall;

const mockCreateDto = (): CreateGrantCallDto => ({
  grantProgramSlug: 'test-program-slug',
  name: 'New Grant Call',
  description: 'Test Description',
  businessCategory: BusinessCategory.GOVERNMENT,
  categories: [GrantCategory.SUSTAINABILITY, GrantCategory.OTHER],
  totalGrantAmount: 100000,
  openForApplicationStart: '2024-01-01T00:00:00Z',
  openForApplicationEnd: '2024-01-31T23:59:59Z',
  communityVotingTime1: 604800,
  communityVotingTime2: 259200,
  grantDistribution: [50, 30, 20],
  screeningFormUrl: 'https://forms.clickup.com/123',
  dueDiligenceFormUrl: 'https://forms.clickup.com/456',
  interviewSchedulingUrl: 'https://calendly.com/your-org/30min',
  townHallSchedulingUrl: 'https://calendly.com/your-org/town-hall-q1',
});

const mockUpdateDto = (): GrantCallBaseDto => ({
  name: 'Updated Grant Call Name',
  description: 'Updated Description',
  businessCategory: BusinessCategory.ENTERPRISE,
  categories: [GrantCategory.DIGITAL_IDENTITY],
  totalGrantAmount: 150000,
  openForApplicationStart: '2024-02-01T00:00:00Z',
  openForApplicationEnd: '2024-02-28T23:59:59Z',
  communityVotingTime1: 700000,
  communityVotingTime2: 300000,
  grantDistribution: [70, 20, 10],
  screeningFormUrl: 'https://forms.clickup.com/updated-123',
  dueDiligenceFormUrl: 'https://forms.clickup.com/updated-456',
  interviewSchedulingUrl: 'https://calendly.com/your-org/updated-30min',
  townHallSchedulingUrl: 'https://calendly.com/your-org/updated-town-hall',
});

const mockMapper = { mapGrantCallToDetailDto: jest.fn(), calculateIsAbleToChangeStageManually: jest.fn() };
const mockGrantCallRepoTransactional = { findOne: jest.fn(), save: jest.fn(), create: jest.fn() };
const mockWorkflowStateRepoTransactional = { create: jest.fn(), save: jest.fn() };
const mockDistRuleRepoTransactional = { remove: jest.fn() };
const mockStageSettingRepoTransactional = { remove: jest.fn() };
const mockProgramRepoTransactional = { findOneBy: jest.fn() };
const mockWfStepDefRepoTransactional = { find: jest.fn(), findOneBy: jest.fn() };
const mockWfTemplateRepoTransactional = { find: jest.fn(), findOneBy: jest.fn() };
const mockUserRepoTransactional = {
  findOneBy: jest.fn(),
};

const transactionalRepositoryMap = new Map<any, any>([
  [GrantCall, mockGrantCallRepoTransactional],
  [GrantDistributionRule, mockDistRuleRepoTransactional],
  [GrantCallStageSetting, mockStageSettingRepoTransactional],
  [GrantProgram, mockProgramRepoTransactional],
  [WorkflowStepDefinition, mockWfStepDefRepoTransactional],
  [User, mockUserRepoTransactional],
  [WorkflowState, mockWorkflowStateRepoTransactional],
  [WorkflowTemplate, mockWfTemplateRepoTransactional],
]);

const mockTransactionalEntityManager = {
  getRepository: jest.fn((entity) => {
    const repo = transactionalRepositoryMap.get(entity);
    if (repo) return repo;
    const entityName = typeof entity === 'function' ? entity.name : entity;
    throw new Error(`Mock getRepository not configured for ${entityName}`);
  }),
};

const mockGrantCallRepositoryValue = {
  findOne: mockGrantCallRepoTransactional.findOne,
  save: jest.fn(),
  manager: {
    transaction: jest.fn().mockImplementation(async (isoLevel, callback) => {
      const actualCallback = typeof isoLevel === 'function' ? isoLevel : callback;
      return await actualCallback(mockTransactionalEntityManager);
    }),
  },
};

const mockWorkflowTransitionService = {
  transitionSingleStateToNextStep: jest.fn(),
  findWorkflowStateByEntity: jest.fn(),
  bulkTransitionReadyApplicationsForCall: jest.fn(),
};

const createMockRepository = () => ({
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  createQueryBuilder: jest.fn(),
});

describe('GrantCallService', () => {
  let service: GrantCallService;
  let grantCallMapper;
  let grantCallRepository: Repository<GrantCall>;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GrantCallService,
        WorkflowService,
        ConfigService,
        { provide: GrantCallMapper, useValue: mockMapper },
        {
          provide: getRepositoryToken(WorkflowStepDefinition),
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(WorkflowState),
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(WorkflowTemplate),
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(GrantProgram),
          useValue: {
            find: jest.fn(),
          },
        },
        { provide: GrantCallMapper, useValue: mockMapper },
        {
          provide: getRepositoryToken(GrantProgram),
          useClass: Repository,
        },
        {
          provide: WorkflowTransitionService,
          useValue: mockWorkflowTransitionService,
        },
        {
          provide: getRepositoryToken(WorkflowStepDefinition),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(WorkflowState),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(GrantCall),
          useValue: mockGrantCallRepositoryValue,
        },
        {
          provide: getRepositoryToken(GrantApplication),
          useClass: Repository,
        },
        {
          provide: MailService,
          useValue: {
            sendMail: jest.fn(),
            sendNewApplicationNotification: jest.fn(),
            sendGrantCallInvitation: jest.fn(),
            sendGrantCallCoordinatorInvitation: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<GrantCallService>(GrantCallService);
    grantCallMapper = module.get(GrantCallMapper);
    grantCallRepository = module.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    let createDto: CreateGrantCallDto;
    let coordinatorUserMock: User;
    let creatorId: number;
    let mockProg: Partial<GrantProgram>;
    let mockStepDefs: Partial<WorkflowStepDefinition>[];
    let stepDefMap: Map<StageCode, WorkflowStepDefinition>;
    let mockSettings: Partial<GrantCallStageSetting>[];
    let mockRules: Partial<GrantDistributionRule>[];
    let mockClosedStepDef: Partial<WorkflowStepDefinition>;

    beforeEach(() => {
      createDto = mockCreateDto();
      coordinatorUserMock = mockUser();
      creatorId = mockUser().id;
      mockProg = mockGrantProgram();
      mockStepDefs = [mockWorkflowStepDef(10, StageCode.GC_OPEN_FOR_APPLICATIONS)];
      stepDefMap = new Map(mockStepDefs.map((def) => [def.code, def as WorkflowStepDefinition]));
      mockClosedStepDef = mockWorkflowStepDef(99, StageCode.GC_CLOSED);
      mockSettings = [mockGrantCallStageSetting(1, 10)];
      mockRules = [
        mockGrantDistributionRule(9, 1, 50),
        mockGrantDistributionRule(8, 2, 30),
        mockGrantDistributionRule(6, 3, 20),
      ];

      jest
        .spyOn(service as any, 'getProgramAndStageDefinitions')
        .mockResolvedValue({ grantProgram: mockProg, stepDefMap: stepDefMap });
      mockedGenerateUniqueSlug.mockResolvedValue('new-grant-call-slug');

      jest.spyOn(service as any, 'buildStageSettings').mockReturnValue(mockSettings);
      jest.spyOn(service as any, 'buildDistributionRules').mockReturnValue(mockRules);
      mockWfStepDefRepoTransactional.findOneBy.mockResolvedValue(mockClosedStepDef);
      mockUserRepoTransactional.findOneBy.mockResolvedValue(coordinatorUserMock);
      mockGrantCallRepoTransactional.save.mockImplementation((gc) => Promise.resolve({ ...gc, id: 99 }));
    });

    it('should successfully create a grant call', async () => {
      const expectedSlug = 'new-grant-call-slug';
      const result = await service.create(createDto, creatorId);

      expect(result).toEqual({ grantCallSlug: expectedSlug });
      expect(grantCallRepository.manager.transaction).toHaveBeenCalledTimes(1);
      expect((service as any).getProgramAndStageDefinitions).toHaveBeenCalledWith(
        createDto.grantProgramSlug,
        ALL_POSSIBLE_SETTING_STAGES,
        mockTransactionalEntityManager,
      );
      expect(mockedGenerateUniqueSlug).toHaveBeenCalledWith(createDto.name, expect.any(Function), 'grant-call');

      expect((service as any).buildStageSettings).toHaveBeenCalledWith(createDto, stepDefMap);
      expect((service as any).buildDistributionRules).toHaveBeenCalledWith(createDto);

      expect(mockGrantCallRepoTransactional.save).toHaveBeenCalledTimes(1);
      expect(mockGrantCallRepoTransactional.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: createDto.name,
          grantCallSlug: expectedSlug,
          description: createDto.description,
          businessCategory: createDto.businessCategory,
          categories: createDto.categories,
          totalGrantAmount: createDto.totalGrantAmount,
          createdById: creatorId,
          grantProgram: mockProg,
          stageSettings: mockSettings,
          distributionRules: mockRules,
        }),
      );
    });

    it('should map URL fields to stage settings via stageUrl property', async () => {
      const urlDto = {
        screeningFormUrl: 'https://forms.clickup.com/screening',
        dueDiligenceFormUrl: 'https://forms.clickup.com/due-diligence',
        interviewSchedulingUrl: 'https://calendly.com/interview',
        townHallSchedulingUrl: 'https://calendly.com/town-hall',
      };

      const testDto = { ...createDto, ...urlDto };

      // Test each URL mapping from DTO to stage settings
      const screeningResult = service['determineUrlProperty'](StageCode.GA_SCREENING, testDto);
      expect(screeningResult).toEqual({ stageUrl: urlDto.screeningFormUrl });

      const dueDiligenceResult = service['determineUrlProperty'](StageCode.GA_DUE_DILIGENCE, testDto);
      expect(dueDiligenceResult).toEqual({ stageUrl: urlDto.dueDiligenceFormUrl });

      const interviewResult = service['determineUrlProperty'](StageCode.GA_INTERVIEW, testDto);
      expect(interviewResult).toEqual({ stageUrl: urlDto.interviewSchedulingUrl });

      const townHallResult = service['determineUrlProperty'](StageCode.GA_TOWN_HALL, testDto);
      expect(townHallResult).toEqual({ stageUrl: urlDto.townHallSchedulingUrl });
    });
  });

  describe('findOne', () => {
    const testSlug = 'test-grant-call-slug';
    let mockGrantCall: GrantCall;
    let expectedDto: FindOneGrantCallResponseDto;
    let mockCreator: User;

    beforeEach(() => {
      mockCreator = {
        id: 1,
        displayName: 'Test Creator',
        email: '<EMAIL>',

        passwordHash: '',
        emailVerified: true,
        disabled: false,
        userRole: undefined,
        authProvider: '',
        externalId: '',
        phone: null,
        verificationCodes: [],
        createdGrantCalls: [],
        applications: [],
        grantCallMemberships: [],
      } as unknown as User;

      mockGrantCall = new GrantCall();

      Object.assign(mockGrantCall, {
        id: 5,
        grantCallSlug: testSlug,
        name: 'Test Grant Call',
        description: 'A description',
        businessCategory: BusinessCategory.STARTUP,
        categories: [GrantCategory.SUSTAINABILITY],
        totalGrantAmount: 50000,
        createdById: mockCreator.id,
        createdBy: mockCreator,
        createdAt: new Date('2023-01-01T10:00:00Z'),
        updatedAt: new Date('2023-01-10T11:00:00Z'),
        stageSettings: [
          {
            id: 10,
            grantCallId: 5,
            workflowStepDefinitionId: 1,
            grantCall: undefined,
            startDate: new Date('2024-02-01T00:00:00.000Z'),
            endDate: new Date('2024-08-15T23:59:59.000Z'),
            durationSeconds: null,
            stageUrl: null,
            workflowStepDefinition: { id: 1, code: StageCode.GC_OPEN_FOR_APPLICATIONS } as WorkflowStepDefinition,
          } as GrantCallStageSetting,
          {
            id: 11,
            grantCallId: 5,
            workflowStepDefinitionId: 2,
            grantCall: undefined,
            startDate: null,
            endDate: null,
            durationSeconds: 604800,
            stageUrl: null,
            workflowStepDefinition: { id: 2, code: StageCode.GC_COMMUNITY_VOTING } as WorkflowStepDefinition,
          } as GrantCallStageSetting,
          {
            id: 12,
            grantCallId: 5,
            workflowStepDefinitionId: 3,
            grantCall: undefined,
            startDate: null,
            endDate: null,
            durationSeconds: 259200,
            stageUrl: null,
            workflowStepDefinition: { id: 3, code: StageCode.GC_FINAL_COMMUNITY_VOTING } as WorkflowStepDefinition,
          } as GrantCallStageSetting,
          {
            id: 13,
            grantCallId: 5,
            workflowStepDefinitionId: 4,
            grantCall: undefined,
            startDate: null,
            endDate: null,
            durationSeconds: null,
            stageUrl: 'http://screen.url',
            workflowStepDefinition: { id: 4, code: StageCode.GA_SCREENING } as WorkflowStepDefinition,
          } as GrantCallStageSetting,
        ],
        distributionRules: [
          { id: 20, grantCallId: 5, grantCall: undefined, rank: 1, type: 'PERCENTAGE', value: 50 },
          { id: 21, grantCallId: 5, grantCall: undefined, rank: 3, type: 'PERCENTAGE', value: 20 },
          { id: 22, grantCallId: 5, grantCall: undefined, rank: 2, type: 'PERCENTAGE', value: 30 },
        ] as GrantDistributionRule[],
        workflowStateId: null,
        workflowState: null,
        grantApplications: [],
        grantProgram: mockGrantProgram(),
      });

      expectedDto = {
        grantCallSlug: testSlug,
        name: 'Test Grant Call',
        description: 'A description',
        businessCategory: BusinessCategory.STARTUP,
        categories: [GrantCategory.SUSTAINABILITY],
        totalGrantAmount: 50000,
        openForApplicationStart: '2024-02-01T00:00:00.000Z',
        openForApplicationEnd: '2024-08-15T23:59:59.000Z',
        updatedAt: new Date(),
        communityVotingTime1: 604800,
        communityVotingTime2: 259200,
        screeningFormUrl: 'http://screen.url',
        dueDiligenceFormUrl: null,
        interviewSchedulingUrl: null,
        townHallSchedulingUrl: null,
        grantDistribution: [50, 30, 20],
        createdBy: { id: 1, displayName: 'Test Creator', email: '<EMAIL>' },
        grantApplicationsCount: 10,
        status: StageCode.GC_OPEN_FOR_APPLICATIONS,
        grantProgram: {
          ...mockGrantProgram(),
          status: StageCode.GP_OPEN,
        },
        isAbleToChangeStageManually: true,
      };

      mockGrantCallRepoTransactional.findOne.mockResolvedValue(mockGrantCall);
      grantCallMapper.mapGrantCallToDetailDto.mockResolvedValue(expectedDto);
    });

    it('should call grantCallRepository.findOne with correct parameters', async () => {
      await service.findOne(testSlug);
      expect(grantCallRepository.findOne).toHaveBeenCalledTimes(1);
      expect(grantCallRepository.findOne).toHaveBeenCalledWith({
        where: { grantCallSlug: testSlug },
        relations: {
          stageSettings: { workflowStepDefinition: true },
          workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } },
          grantProgram: {
            grantProgramCoordinator: true,
            workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } },
          },
          distributionRules: true,
          createdBy: true,
        },
        order: {
          distributionRules: { id: 'ASC' },
        },
      });
    });
  });

  describe('update', () => {
    let updateDto: GrantCallBaseDto;
    let existingSlug: string;
    let foundGrantCall: GrantCall;
    let mockStepDefs: WorkflowStepDefinition[];
    let stepDefMap: Map<StageCode, WorkflowStepDefinition>;
    let mockNewSettings: GrantCallStageSetting[];

    beforeEach(() => {
      updateDto = mockUpdateDto();
      existingSlug = 'existing-slug-456';
      foundGrantCall = mockGrantCall(5, existingSlug, true);

      mockStepDefs = [
        mockWorkflowStepDef(10, StageCode.GC_OPEN_FOR_APPLICATIONS),
        mockWorkflowStepDef(20, StageCode.GA_SCREENING),
      ];
      stepDefMap = new Map(mockStepDefs.map((def) => [def.code, def]));
      mockNewSettings = [mockGrantCallStageSetting(3, 10), mockGrantCallStageSetting(4, 20)];

      mockGrantCallRepoTransactional.findOne.mockResolvedValue(foundGrantCall);
      mockGrantCallRepoTransactional.save.mockImplementation((gc) => Promise.resolve(gc));
      mockStageSettingRepoTransactional.remove.mockResolvedValue(undefined);
      mockProgramRepoTransactional.findOneBy.mockResolvedValue(foundGrantCall.grantProgram);

      jest
        .spyOn(service as any, 'getProgramAndStageDefinitions')
        .mockResolvedValue({ grantProgram: foundGrantCall.grantProgram, stepDefMap: stepDefMap });
      jest.spyOn(service as any, 'buildStageSettings').mockReturnValue(mockNewSettings);
    });

    it('should successfully update a grant call', async () => {
      const result = await service.update(existingSlug, updateDto);

      expect(result).toEqual({ grantCallSlug: existingSlug });

      expect(grantCallRepository.manager.transaction).toHaveBeenCalledTimes(1);

      expect(mockGrantCallRepoTransactional.findOne).toHaveBeenCalledTimes(1);
      expect(mockGrantCallRepoTransactional.findOne).toHaveBeenCalledWith({
        where: { grantCallSlug: existingSlug },
        relations: ['grantProgram', 'stageSettings', 'stageSettings.workflowStepDefinition'],
      });

      expect(mockGrantCallRepoTransactional.save).toHaveBeenCalledTimes(1);
      expect(mockGrantCallRepoTransactional.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: foundGrantCall.id,
          grantCallSlug: existingSlug,
          createdById: foundGrantCall.createdById,
          grantProgram: foundGrantCall.grantProgram,
          name: updateDto.name,
          description: updateDto.description,
          stageSettings: expect.any(Array),
        }),
      );
    });
  });

  describe('conditionallyAdvanceGrantCallStage', () => {
    const mockGrantCallSlug = 'a-slug';
    const mockGrantCallId = 123;
    const mockAppSourceStage = StageCode.GA_SCREENING;
    const mockAppStepId = 456;
    const mockAppTransitionResult = { transitionedCount: 3 };
    const mockUpdatedGcState = { stepDefinitionCode: StageCode.GC_COMMUNITY_VOTING } as any;

    let getGrantCallIdBySlugSpy: jest.SpyInstance;
    let deriveApplicationSourceStepIdSpy: jest.SpyInstance;

    beforeEach(() => {
      getGrantCallIdBySlugSpy = jest.spyOn(service as any, 'getGrantCallIdBySlug').mockResolvedValue(mockGrantCallId);
      deriveApplicationSourceStepIdSpy = jest
        .spyOn(service as any, 'deriveApplicationSourceStepId')
        .mockResolvedValue(mockAppStepId);

      mockWorkflowTransitionService.bulkTransitionReadyApplicationsForCall.mockResolvedValue(mockAppTransitionResult);
      mockWorkflowTransitionService.transitionSingleStateToNextStep.mockResolvedValue(mockUpdatedGcState);
    });

    it('should call its dependencies in order and return the correct structure', async () => {
      const result = await (service as any).conditionallyAdvanceGrantCallStage(mockGrantCallSlug, mockAppSourceStage);

      expect(getGrantCallIdBySlugSpy).toHaveBeenCalledWith(mockGrantCallSlug);
      expect(deriveApplicationSourceStepIdSpy).toHaveBeenCalledWith(mockGrantCallId, mockAppSourceStage);
      expect(mockWorkflowTransitionService.bulkTransitionReadyApplicationsForCall).toHaveBeenCalledWith(
        mockGrantCallId,
        mockAppStepId,
      );
      expect(mockWorkflowTransitionService.transitionSingleStateToNextStep).toHaveBeenCalledWith(
        WorkflowEntityType.CALL,
        mockGrantCallId,
      );
      expect(result).toEqual({
        transitionedCount: mockAppTransitionResult.transitionedCount,
        grantCallNewStage: mockUpdatedGcState.stepDefinitionCode,
      });
    });
  });

  describe('Advance Grant Call Stages', () => {
    let conditionallyAdvanceGrantCallStageSpy: jest.SpyInstance;
    const mockGrantCallSlug = 'test-call-slug';
    const mockConditionalResponse: ConditionalBulkTransitionResponseDto = {
      transitionedCount: 5,
      grantCallNewStage: GrantCallStageCode.COMMUNITY_VOTING,
    };

    beforeEach(() => {
      conditionallyAdvanceGrantCallStageSpy = jest
        .spyOn(service as any, 'conditionallyAdvanceGrantCallStage')
        .mockResolvedValue(mockConditionalResponse);
    });

    describe('advanceGrantCallFromScreening', () => {
      it('should call conditionallyAdvanceGrantCallStage with GA_SCREENING and return its result', async () => {
        const result = await service.advanceGrantCallFromScreening(mockGrantCallSlug);

        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledTimes(1);
        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledWith(mockGrantCallSlug, StageCode.GA_SCREENING);
        expect(result).toEqual(mockConditionalResponse);
      });
    });

    describe('advanceGrantCallFromOnboarding', () => {
      it('should call conditionallyAdvanceGrantCallStage with GA_TOWN_HALL and return its result', async () => {
        const result = await service.advanceGrantCallFromOnboarding(mockGrantCallSlug);

        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledTimes(1);
        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledWith(mockGrantCallSlug, StageCode.GA_TOWN_HALL);
        expect(result).toEqual(mockConditionalResponse);
      });
    });
  });
});
