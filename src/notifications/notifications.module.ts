import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MailModule } from './mail/mail.module';
import { Module } from '@nestjs/common';
import { NotificationPreferenceController } from './preferences/preferences.controller';
import { NotificationPreferenceService } from './preferences/preferences.service';
import { SnsModule } from './channels/sns/sns.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
import { UserNotificationPreferences } from './entities/user-notification-preferences.entity';

@Module({
  imports: [
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRATION_TIME') },
      }),
    }),
    TypeOrmModule.forFeature([UserNotificationPreferences, User]),
    MailModule,
    SnsModule,
  ],

  controllers: [NotificationPreferenceController],
  providers: [NotificationPreferenceService],
  exports: [NotificationPreferenceService],
})
export class NotificationsModule {}
