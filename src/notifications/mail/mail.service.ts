import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { GrantApplication } from '../../grant-application/entities/grant-application.entity';
import { GrantCall } from '../../grant-call/entities/grant-call.entity';
import { MailerService } from '@nestjs-modules/mailer';
import { User } from '../../auth/entities/user.entity';
import { getNameFromEmail } from '../../utils/string.util';

@Injectable()
export class MailService {
  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
  ) {}

  private readonly logger = new Logger(MailService.name);

  private async sendEmail<T>(to: string, subject: string, template: string, context: T): Promise<boolean> {
    try {
      await this.mailerService.sendMail({
        to,
        subject,
        template,
        context,
      });
    } catch (error) {
      this.logger.error(`Failed sending "${subject}" email: ${error}`);
      return false;
    }
    return true;
  }

  /**
   * Sends an account recovery email to the user.
   * @param user The user to send the email to
   * @param recoveryLink The link for account recovery
   * @returns True if the email was sent successfully, false otherwise
   */
  async sendAccountRecoveryEmail(user: User, recoveryLink: string): Promise<boolean> {
    const subject = 'Account Recovery';
    const template = './account-recovery.template.hbs';
    const context = {
      name: user.displayName,
      url: recoveryLink,
    };
    return this.sendEmail(user.email, subject, template, context);
  }

  /**
   * Sends an email verification code to the user.
   * @param user The user to send the email to
   * @param otp The one-time password for email verification
   * @returns True if the email was sent successfully, false otherwise
   */
  async sendEmailVerification(user: User, otp: string): Promise<boolean> {
    const subject = 'Email Verification Code';
    const template = './user-verification.template.hbs';
    const context = {
      name: user.displayName,
      otp: otp,
    };
    return this.sendEmail(user.email, subject, template, context);
  }

  /**
   * Sends an email to the grant call coordinator informing them that a new application was submitted and they are the
   * assignee.
   * @param coordinator The user to send the email to
   * @param application The grant application that was submitted
   * @returns True if the email was sent successfully, false otherwise
   */
  async sendNewApplicationNotification(
    coordinator: User,
    application: GrantApplication,
    grantCall: GrantCall,
    applicationLink: string,
  ): Promise<boolean> {
    const subject = 'New Grant Application';
    const template = './new-grant-application.template.hbs';
    const context = {
      name: coordinator.displayName,
      applicationTitle: application.title,
      applicationLink,
      grantCallName: grantCall.name,
    };
    return this.sendEmail(coordinator.email, subject, template, context);
  }

  /**
   * Sends an email to user that has just been added to a grant call.
   * @param member The user to send the email to
   * @param grantCall The grant call details. Must be loaded with the grant program.
   * @returns True if the email was sent successfully, false otherwise
   */
  async sendGrantCallInvitation(member: User, grantCall: GrantCall, isCoordinator: boolean): Promise<boolean> {
    const subject = 'Grant Call Invitation';
    const template = './grant-call-invitation.template.hbs';
    const grantCallLink = `${this.configService.get('FRONTEND_BASE_URL')}/grant-programs/${grantCall.grantProgram.grantProgramSlug}/grant-call/${grantCall.grantCallSlug}/applications`;
    const context = {
      name: member.displayName,
      role: isCoordinator ? 'coordinator' : 'member',
      grantCallName: grantCall.name,
      grantCallLink,
    };
    return this.sendEmail(member.email, subject, template, context);
  }

  /**
   * Sends an email to non existing user to be added as a coordinator to a grant call.
   * @param email The user to send the email to
   * @returns True if the email was sent successfully, false otherwise
   */
  async sendGrantCallCoordinatorInvitation(email: string): Promise<boolean> {
    const subject = 'Grant Call Invitation';
    const template = './grant-call-coordinator-invitation.template.hbs';
    const registrationUrl = `${this.configService.get('FRONTEND_BASE_URL')}/connect-wallet`;
    const context = {
      name: getNameFromEmail(email),
      registrationUrl,
    };
    const emailSent = await this.sendEmail(email, subject, template, context);

    if (!emailSent) {
      this.logger.error(`Failed sending invitation email to "${email}"`);
      throw new HttpException('Email sending failed', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return emailSent;
  }

  async sendApplicationApprovedNotification(
    applicantEmail: string,
    applicantName: string,
    applicationTitle: string,
    applicationLink: string,
  ): Promise<boolean> {
    const subject = 'Grant Application Approved';
    const template = './application-approved.template.hbs';
    const context = {
      name: applicantName,
      applicationTitle,
      applicationLink,
    };
    return this.sendEmail(applicantEmail, subject, template, context);
  }

  async sendApplicationMovedNotification(
    applicantEmail: string,
    applicantName: string,
    applicationTitle: string,
    newStageName: string,
    applicationLink: string,
  ): Promise<boolean> {
    const subject = 'Application Moved to Next Stage';
    const template = './application-moved.template.hbs';
    const context = {
      name: applicantName,
      applicationTitle,
      applicationLink,
      stageTitle: newStageName,
    };
    return this.sendEmail(applicantEmail, subject, template, context);
  }

  /**
   * Sends an email to the applicant informing them that their application was rejected.
   * @param applicant The user to send the email to
   * @param application The grant application that was rejected
   * @returns True if the email was sent successfully, false otherwise
   */
  async sendApplicationRejectedNotification(
    applicantEmail: string,
    applicantName: string,
    applicationTitle: string,
    applicationLink: string,
    reason?: string,
  ): Promise<boolean> {
    const subject = 'Application Rejected';
    const template = './application-rejected.template.hbs';
    const context = {
      name: applicantName,
      applicationTitle,
      applicationLink,
      reason,
    };
    return this.sendEmail(applicantEmail, subject, template, context);
  }

  async sendBalanceAlertEmail(
    email: string,
    name: string,
    alertLevel: 'alert' | 'warning' | 'critical',
    balance: number,
  ): Promise<boolean> {
    const subject = 'Balance Alert';
    const template = `./balance-${alertLevel}.template.hbs`;
    const context = {
      balance,
      name,
    };
    return this.sendEmail(email, subject, template, context);
  }
}
