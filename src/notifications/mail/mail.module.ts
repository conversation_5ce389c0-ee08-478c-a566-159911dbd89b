import * as AWS from '@aws-sdk/client-ses';
import * as path from 'path';

import { ConfigService } from '@nestjs/config';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { MailService } from './mail.service';
import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';

@Module({
  imports: [
    MailerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const endpoint = configService.get<string>('AWS_SES_ENDPOINT');
        return {
          transport: {
            SES: {
              ses: new AWS.SES({
                apiVersion: '2010-12-01',
                region: configService.get<string>('AWS_SES_REGION'),
                credentials: {
                  accessKeyId: configService.get<string>('AWS_SES_ACCESS_KEY'),
                  secretAccessKey: configService.get<string>('AWS_SES_SECRET_KEY'),
                },
                ...(endpoint && { endpoint }),
              }),
              aws: AWS,
            },
            sendingRate: 1, // 1 messages per second
          },
          defaults: {
            from: `THG Funding Platform <${configService.get<string>('AWS_SES_FROM_EMAIL')}>`,
          },
          preview: configService.get<string>('EMAIL_PREVIEW') === 'true',
          template: {
            dir: path.join(process.cwd(), 'dist', 'notifications', 'mail', 'templates'),

            adapter: new HandlebarsAdapter(undefined, {
              inlineCssEnabled: true,
              inlineCssOptions: {
                inlineStyleTags: true,
              },
            }),
            options: {
              strict: true,
            },
          },
          options: {
            partials: {
              dir: path.join(process.cwd(), 'dist', 'notifications', 'mail', 'templates/partials'),
              options: { strict: true },
            },
          },
        };
      },
    }),
  ],
  providers: [MailService],
  exports: [MailService],
})
export class MailModule {}
