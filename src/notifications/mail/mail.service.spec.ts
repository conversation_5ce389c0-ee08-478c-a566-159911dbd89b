import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@nestjs/config';
import { GrantApplication } from '../../grant-application/entities/grant-application.entity';
import { GrantCall } from '../../grant-call/entities/grant-call.entity';
import { GrantProgram } from '../../grant-program/entities/grant-program.entity';
import { MailService } from './mail.service';
import { MailerService } from '@nestjs-modules/mailer';
import { StageCode } from '../../workflow/enums/stage-code.enum';
import { User } from '../../auth/entities/user.entity';
import { getNameFromEmail } from '../../utils/string.util';

// Mock the getNameFromEmail function if it's from an external module
jest.mock('../../utils/string.util', () => ({
  getNameFromEmail: jest.fn(),
}));

describe('MailService', () => {
  let service: MailService;
  let mailerService: jest.Mocked<MailerService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MailService,
        {
          provide: MailerService,
          useFactory: () => ({
            sendMail: jest.fn(),
          }),
        },
        {
          provide: ConfigService,
          useFactory: () => ({
            get: jest.fn((key: string) => {
              if (key === 'FRONTEND_BASE_URL') {
                return 'https://localhost:3000';
              }
              return null;
            }),
          }),
        },
      ],
    }).compile();

    service = module.get<MailService>(MailService);
    mailerService = module.get(MailerService);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmail', () => {
    it('should send an email successfully', async () => {
      mailerService.sendMail.mockResolvedValue(undefined); // Mock successful sending

      const result = await service['sendEmail']('<EMAIL>', 'Test Subject', './test.template.hbs', {
        name: 'Test User',
      });

      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: './test.template.hbs',
        context: {
          name: 'Test User',
        },
      });
      expect(result).toBe(true);
    });

    it('should handle failure when sending an email', async () => {
      mailerService.sendMail.mockRejectedValue(new Error('Failed to send email')); // Mock failure

      const result = await service['sendEmail']('<EMAIL>', 'Test Subject', './test.template.hbs', {
        name: 'Test User',
      });

      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: './test.template.hbs',
        context: {
          name: 'Test User',
        },
      });
      expect(result).toBe(false);
    });
  });

  describe('sendAccountRecoveryEmail', () => {
    it('should send an account recovery email', async () => {
      const user = { email: '<EMAIL>', displayName: 'Test User' } as User;
      const recoveryLink = 'https://example.com/recover';

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendAccountRecoveryEmail(user, recoveryLink);

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'Account Recovery',
        './account-recovery.template.hbs',
        {
          name: 'Test User',
          url: recoveryLink,
        },
      );
      expect(result).toBe(true);
    });
  });

  describe('sendEmailVerification', () => {
    it('should send an email verification code', async () => {
      const user = { email: '<EMAIL>', displayName: 'Test User' } as User;
      const otp = '123456';

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendEmailVerification(user, otp);

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'Email Verification Code',
        './user-verification.template.hbs',
        {
          name: 'Test User',
          otp: otp,
        },
      );
      expect(result).toBe(true);
    });
  });

  describe('sendNewApplicationNotification', () => {
    it('should send a new application notification', async () => {
      const coordinator = { email: '<EMAIL>', displayName: 'Coordinator' } as User;
      const application = { title: 'Test Application' } as GrantApplication;
      const grantCall = { name: 'Test Grant Call' } as GrantCall;
      const applicationLink = 'https://example.com/application/1';

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendNewApplicationNotification(coordinator, application, grantCall, applicationLink);

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'New Grant Application',
        './new-grant-application.template.hbs',
        {
          name: 'Coordinator',
          applicationTitle: 'Test Application',
          applicationLink,
          grantCallName: 'Test Grant Call',
        },
      );
      expect(result).toBe(true);
    });
  });

  describe('sendGrantCallInvitation', () => {
    it('should send a grant call invitation to a member', async () => {
      const member = { email: '<EMAIL>', displayName: 'Test Member' } as User;
      const grantProgram = new GrantProgram();
      grantProgram.grantProgramSlug = 'test-program';
      const grantCall = { name: 'Test Grant Call', grantProgram } as GrantCall;
      grantCall.grantCallSlug = 'test-call';
      configService.get.mockReturnValue('https://example.com');

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendGrantCallInvitation(member, grantCall, false);

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'Grant Call Invitation',
        './grant-call-invitation.template.hbs',
        {
          name: 'Test Member',
          role: 'member',
          grantCallName: 'Test Grant Call',
          grantCallLink: `https://example.com/grant-programs/${grantCall.grantProgram.grantProgramSlug}/grant-call/${grantCall.grantCallSlug}/applications`,
        },
      );
      expect(result).toBe(true);
    });

    it('should send a grant call invitation to a coordinator', async () => {
      const member = { email: '<EMAIL>', displayName: 'Test Member' } as User;
      const grantProgram = new GrantProgram();
      grantProgram.grantProgramSlug = 'test-program';
      const grantCall = { name: 'Test Grant Call', grantProgram } as GrantCall;
      grantCall.grantCallSlug = 'test-call';
      configService.get.mockReturnValue('https://example.com');

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendGrantCallInvitation(member, grantCall, true);

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'Grant Call Invitation',
        './grant-call-invitation.template.hbs',
        {
          name: 'Test Member',
          role: 'coordinator',
          grantCallName: 'Test Grant Call',
          grantCallLink: `https://example.com/grant-programs/${grantCall.grantProgram.grantProgramSlug}/grant-call/${grantCall.grantCallSlug}/applications`,
        },
      );
      expect(result).toBe(true);
    });
  });

  describe('sendApplicationApprovedNotification', () => {
    it('should send an application approved notification', async () => {
      const applicant = { email: '<EMAIL>', displayName: 'Applicant' } as User;
      const application = { title: 'Test Application' } as GrantApplication;
      const applicationLink = 'https://example.com/application/1';

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendApplicationApprovedNotification(
        applicant.email,
        applicant.displayName,
        application.title,
        applicationLink,
      );

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'Grant Application Approved',
        './application-approved.template.hbs',
        {
          name: 'Applicant',
          applicationTitle: 'Test Application',
          applicationLink,
        },
      );
      expect(result).toBe(true);
    });
  });

  describe('sendApplicationMovedNotification', () => {
    it('should send an application moved notification', async () => {
      const applicant = { email: '<EMAIL>', displayName: 'Applicant' } as User;
      const application = { title: 'Test Application' } as GrantApplication;

      const applicationLink = 'https://example.com/application/1';

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendApplicationMovedNotification(
        applicant.email,
        applicant.displayName,
        application.title,
        StageCode.GA_TOWN_HALL,
        applicationLink,
      );

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'Application Moved to Next Stage',
        './application-moved.template.hbs',
        {
          name: 'Applicant',
          applicationTitle: 'Test Application',
          applicationLink,
          stageTitle: StageCode.GA_TOWN_HALL,
        },
      );
      expect(result).toBe(true);
    });
  });

  describe('sendApplicationRejectedNotification', () => {
    it('should send an application rejected notification', async () => {
      const applicant = { email: '<EMAIL>', displayName: 'Applicant' } as User;
      const application = { title: 'Test Application' } as GrantApplication;
      const applicationLink = 'https://example.com/application/1';

      jest.spyOn(service as any, 'sendEmail').mockResolvedValue(true);

      const result = await service.sendApplicationRejectedNotification(
        applicant.email,
        applicant.displayName,
        application.title,
        applicationLink,
      );

      expect(service['sendEmail']).toHaveBeenCalledWith(
        '<EMAIL>',
        'Application Rejected',
        './application-rejected.template.hbs',
        {
          name: 'Applicant',
          applicationTitle: 'Test Application',
          applicationLink,
        },
      );
      expect(result).toBe(true);
    });
  });

  describe('sendGrantCallCoordinatorInvitation', () => {
    it('should send an invitation email with the correct parameters', async () => {
      const email = '<EMAIL>';

      (getNameFromEmail as jest.Mock).mockReturnValue('Test User');
      mailerService.sendMail = jest.fn().mockResolvedValue(true);

      const result = await service.sendGrantCallCoordinatorInvitation(email);

      expect(result).toBe(true);
      expect(getNameFromEmail).toHaveBeenCalledWith(email);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: email,
        subject: 'Grant Call Invitation',
        template: './grant-call-coordinator-invitation.template.hbs',
        context: {
          name: 'Test User',
          registrationUrl: `${configService.get<string>('FRONTEND_BASE_URL')}/connect-wallet`,
        },
      });
    });
  });
});
