import { IsOptional, IsObject, Validate, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { NotificationType } from '../entities/user-notification-preferences.entity';

// Custom validator to check if keys are valid NotificationType enum values
@ValidatorConstraint({ name: 'isValidNotificationTypeKeys', async: false })
export class IsValidNotificationTypeKeysConstraint implements ValidatorConstraintInterface {
  validate(preferences: any) {
    if (!preferences) {
      return true;
    }
    if (typeof preferences !== 'object') {
      return false;
    }

    for (const key in preferences) {
      if (!Object.values(NotificationType).includes(key as NotificationType)) {
        // Invalid key - not in NotificationType enum
        return false;
      }
      if (typeof preferences[key] !== 'boolean') {
        // Value should be boolean
        return false;
      }
    }
    // All keys are valid NotificationType enum values and values are boolean
    return true;
  }

  defaultMessage() {
    return `preferences object must have keys that are valid NotificationType enum values and boolean values.`;
  }
}

export class NotificationPreferencesDto {
  @ApiPropertyOptional({
    description:
      'Notification preferences object. Keys are NotificationType enum values, values are booleans indicating if the notification is enabled.',
    example: {
      'application-approved': true,
      'application-moved': false,
      'application-rejected': true,
      'grant-application-assignee-changed': false,
      'grant-call-invitation': true,
      'new-grant-application': false,
    },
    type: 'object',
    properties: Object.fromEntries(
      Object.values(NotificationType).map((enumValue) => [
        enumValue,
        {
          type: 'boolean',
          description: `Enable/disable ${enumValue} notification for type: ${enumValue}`,
        },
      ]),
    ),
    additionalProperties: false,
  })
  @IsOptional()
  @IsObject()
  @Validate(IsValidNotificationTypeKeysConstraint)
  preferences?: { [key in NotificationType]?: boolean };
}
