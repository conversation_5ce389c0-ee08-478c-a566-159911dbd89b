import { validate } from 'class-validator';
import { NotificationPreferencesDto } from '../notification-preferences.dto';
import { NotificationType } from '../../entities/user-notification-preferences.entity';

describe('NotificationPreferencesDto', () => {
  it('should validate successfully with valid preferences', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = {
      [NotificationType.APPLICATION_APPROVED]: true,
      [NotificationType.APPLICATION_MOVED]: false,
      [NotificationType.APPLICATION_REJECTED]: true,
      [NotificationType.GRANT_APPLICATION_ASSIGNEE_CHANGED]: false,
      [NotificationType.GRANT_CALL_INVITATION]: true,
      [NotificationType.NEW_GRANT_APPLICATION]: false,
    };

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should validate successfully with empty preferences', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = {};

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation with invalid preferences', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = {
      [NotificationType.APPLICATION_APPROVED]: 'invalid', // Invalid value
    } as any;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isValidNotificationTypeKeys');
  });

  it('should fail validation with additional properties', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = {
      [NotificationType.APPLICATION_APPROVED]: true,
      invalid_property: true,
    } as any;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isValidNotificationTypeKeys');
  });

  it('should validate successfully with optional preferences', async () => {
    const dto = new NotificationPreferencesDto();

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should validate successfully with preferences being null', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = null;

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation with preferences being an invalid type', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = 'invalid' as any; // Invalid type

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isValidNotificationTypeKeys');
  });

  it('should fail validation with preferences containing a valid key but invalid value type', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = {
      [NotificationType.APPLICATION_APPROVED]: 'invalid', // Invalid value type
    } as any;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isValidNotificationTypeKeys');
  });

  it('should fail validation with preferences containing an invalid key', async () => {
    const dto = new NotificationPreferencesDto();
    dto.preferences = {
      invalid_key: true, // Invalid key
    } as any;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isValidNotificationTypeKeys');
  });
});
