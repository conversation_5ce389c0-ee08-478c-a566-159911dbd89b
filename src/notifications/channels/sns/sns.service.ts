import { BadRequestException, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { PublishCommand, SNSClient, SNSClientConfig } from '@aws-sdk/client-sns';
import { isValidNumber, parsePhoneNumber } from 'libphonenumber-js';

import { ConfigService } from '@nestjs/config';

@Injectable()
export class SnsService {
  private readonly snsClient: SNSClient;
  private readonly logger = new Logger(SnsService.name);

  constructor(private configService: ConfigService) {
    let snsConfig: SNSClientConfig = {};

    const useLocalStack = this.configService.get<string>('USE_LOCALSTACK', 'false');

    if (useLocalStack === 'true') {
      // LocalStack Configuration
      snsConfig = {
        endpoint: this.configService.get<string>('LOCALSTACK_SNS_ENDPOINT', 'http://localhost:4566'),
        region: this.configService.get<string>('LOCALSTACK_REGION', 'eu-central-1'),
        credentials: {
          accessKeyId: this.configService.get<string>('LOCALSTACK_ACCESS_KEY_ID', 'test'),
          secretAccessKey: this.configService.get<string>('LOCALSTACK_SECRET_ACCESS_KEY', 'test'),
        },
      };
      this.logger.log('Using LocalStack SNS configuration');
    } else {
      // Real AWS SNS Configuration
      snsConfig = {
        region: this.configService.get<string>('AWS_SES_REGION', 'eu-central-1'),
        credentials: {
          accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
          secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY'),
        },
      };
    }

    this.snsClient = new SNSClient(snsConfig);
  }

  async publishSMS(phoneNumber: string, message: string): Promise<void> {
    try {
      const parsedPhoneNumber = parsePhoneNumber(phoneNumber);
      if (!isValidNumber(parsedPhoneNumber.number)) {
        throw new BadRequestException(`Invalid phone number format: ${phoneNumber}`);
      }
    } catch (error) {
      this.logger.error(`Phone number validation error for ${phoneNumber}`, error);
      throw new BadRequestException(`Invalid phone number format: ${phoneNumber}`);
    }
    const params = {
      // TopicArn: 'arn:aws:sns:eu-central-2:AKIAQEIP3JRBCI2EJB5X:tha-dev-funding-platform-sms-verification',
      PhoneNumber: phoneNumber,
      Message: message,
    };

    try {
      const command = new PublishCommand(params);
      const result = await this.snsClient.send(command);
      this.logger.log(`SNS Message sent to ${phoneNumber}, MessageId: ${result.MessageId}`);
    } catch (error) {
      this.logger.error(`Error sending SNS message to ${phoneNumber}`, error);

      if (error.name === 'ThrottlingException' || error.$metadata?.httpStatusCode === 429) {
        this.logger.warn(`SNS Throttling encountered. Consider implementing retry logic.`, error);
        throw new InternalServerErrorException('SNS service is experiencing high traffic. Please try again later.');
      }

      throw error;
    }
  }
}
