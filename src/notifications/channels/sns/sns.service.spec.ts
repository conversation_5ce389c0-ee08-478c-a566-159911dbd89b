import { BadRequestException, InternalServerErrorException, Logger } from '@nestjs/common';
import { PublishCommand, SNSClient } from '@aws-sdk/client-sns';
import { Test, TestingModule } from '@nestjs/testing';
import { isValidNumber, parsePhoneNumber } from 'libphonenumber-js';

import { ConfigService } from '@nestjs/config';
import { SnsService } from './sns.service';

jest.mock('@aws-sdk/client-sns', () => {
  const mockSend = jest.fn(); // Define send mock here
  const mockSNSClientConstructor = jest.fn(() => {
    return {
      send: mockSend,
    };
  });

  const mockPublishCommandConstructor = jest.fn().mockImplementation((params) => {
    return { params };
  });

  return {
    SNSClient: mockSNSClientConstructor,
    PublishCommand: mockPublishCommandConstructor,
    mockSNSClientSend: mockSend,
  };
});
jest.mock('libphonenumber-js', () => ({
  parsePhoneNumber: jest.fn(),
  isValidNumber: jest.fn(),
}));

describe('SnsService', () => {
  let service: SnsService;
  let configService: ConfigService;
  let mockSnsClientSend: jest.Mock;
  let mockParsePhoneNumber: jest.Mock;
  let mockIsValidNumber: jest.Mock;
  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    const module: TestingModule = await Test.createTestingModule({
      providers: [SnsService, ConfigService, Logger],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn(),
      })
      .overrideProvider(Logger)
      .useValue(mockLogger)
      .compile();

    configService = module.get<ConfigService>(ConfigService);
    const mockSNSClientInstance = (SNSClient as jest.Mock).mock.results[0]?.value;
    mockSnsClientSend = mockSNSClientInstance?.send;
    mockParsePhoneNumber = parsePhoneNumber as jest.Mock;
    mockIsValidNumber = isValidNumber as jest.Mock;
  });

  // describe('constructor', () => {
  //   it('should initialize SNSClient with AWS config when USE_LOCALSTACK is false', () => {
  //     (configService.get as jest.Mock)
  //       .mockReturnValueOnce('false')
  //       .mockReturnValueOnce('eu-central-1')
  //       .mockReturnValueOnce('test')
  //       .mockReturnValueOnce('test');
  //     new SnsService(configService);
  //     expect(SNSClient).toHaveBeenCalledWith({
  //       region: 'eu-central-1',
  //       credentials: {
  //         accessKeyId: 'test',
  //         secretAccessKey: 'test',
  //       },
  //     });
  //   });

  //   it('should initialize SNSClient with LocalStack config when USE_LOCALSTACK is true', () => {
  //     (configService.get as jest.Mock)
  //       .mockReturnValueOnce('true')
  //       .mockReturnValueOnce('http://localhost:4566')
  //       .mockReturnValueOnce('eu-central-1')
  //       .mockReturnValueOnce('test')
  //       .mockReturnValueOnce('test');

  //     new SnsService(configService);
  //     expect(SNSClient).toHaveBeenCalledWith({
  //       endpoint: 'http://localhost:4566',
  //       region: 'eu-central-1',
  //       credentials: {
  //         accessKeyId: 'test',
  //         secretAccessKey: 'test',
  //       },
  //     });
  //   });
  // });

  describe('publishSMS', () => {
    beforeEach(() => {
      service = new SnsService(configService);
    });
    it('should successfully publish SMS and log success', async () => {
      const phoneNumber = '+15005550006';
      const message = 'Hello SMS!';
      mockParsePhoneNumber.mockReturnValue({ number: phoneNumber });
      mockIsValidNumber.mockReturnValue(true);
      mockSnsClientSend.mockResolvedValue({ MessageId: 'mockMessageId' });

      await service.publishSMS(phoneNumber, message);
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(parsePhoneNumber).toHaveBeenCalledWith(phoneNumber);
      expect(isValidNumber).toHaveBeenCalledWith(phoneNumber);
      expect(PublishCommand).toHaveBeenCalledWith({
        PhoneNumber: phoneNumber,
        Message: message,
      });
      expect(mockSnsClientSend).toHaveBeenCalled();

      await new Promise((resolve) => setTimeout(resolve, 0)); // Allow async operations to complete
    });

    it('should throw BadRequestException for invalid phone number format', async () => {
      const phoneNumber = 'invalid-phone-number';
      const message = 'Hello SMS!';
      mockParsePhoneNumber.mockImplementation(() => {
        throw new Error('Invalid phone number');
      });

      await expect(service.publishSMS(phoneNumber, message)).rejects.toThrowError(BadRequestException);
      await expect(service.publishSMS(phoneNumber, message)).rejects.toHaveProperty(
        'message',
        `Invalid phone number format: ${phoneNumber}`,
      );

      expect(mockSnsClientSend).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if isValidNumber returns false', async () => {
      const phoneNumber = '+15005550006';
      const message = 'Hello SMS!';
      mockParsePhoneNumber.mockReturnValue({ number: phoneNumber });
      mockIsValidNumber.mockReturnValue(false);

      await expect(service.publishSMS(phoneNumber, message)).rejects.toThrowError(BadRequestException);
      await expect(service.publishSMS(phoneNumber, message)).rejects.toHaveProperty(
        'message',
        `Invalid phone number format: ${phoneNumber}`,
      );

      expect(mockSnsClientSend).not.toHaveBeenCalled();
    });

    it('should throw InternalServerErrorException for ThrottlingException', async () => {
      const phoneNumber = '+15005550006';
      const message = 'Hello SMS!';
      mockParsePhoneNumber.mockReturnValue({ number: phoneNumber });
      mockIsValidNumber.mockReturnValue(true);
      const throttlingError = new Error('Throttling Exception');
      throttlingError.name = 'ThrottlingException';
      mockSnsClientSend.mockRejectedValue(throttlingError);

      await expect(service.publishSMS(phoneNumber, message)).rejects.toThrowError(InternalServerErrorException);
      await expect(service.publishSMS(phoneNumber, message)).rejects.toHaveProperty(
        'message',
        'SNS service is experiencing high traffic. Please try again later.',
      );
    });

    it('should throw InternalServerErrorException for 429 status code', async () => {
      const phoneNumber = '+15005550006';
      const message = 'Hello SMS!';
      mockParsePhoneNumber.mockReturnValue({ number: phoneNumber });
      mockIsValidNumber.mockReturnValue(true);
      const statusCode429Error = new Error('Status Code 429');
      (statusCode429Error as any).$metadata = { httpStatusCode: 429 };
      mockSnsClientSend.mockRejectedValue(statusCode429Error);

      await expect(service.publishSMS(phoneNumber, message)).rejects.toThrowError(InternalServerErrorException);
      await expect(service.publishSMS(phoneNumber, message)).rejects.toHaveProperty(
        'message',
        'SNS service is experiencing high traffic. Please try again later.',
      );
    });

    it('should re-throw generic errors from SNSClient', async () => {
      const phoneNumber = '+15005550006';
      const message = 'Hello SMS!';
      mockParsePhoneNumber.mockReturnValue({ number: phoneNumber });
      mockIsValidNumber.mockReturnValue(true);
      const genericError = new Error('Generic SNS Error');
      mockSnsClientSend.mockRejectedValue(genericError);

      await expect(service.publishSMS(phoneNumber, message)).rejects.toThrowError(genericError);
    });
  });
});
