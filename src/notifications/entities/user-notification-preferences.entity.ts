import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  ManyToOne,
  Column,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';

export enum NotificationType {
  APPLICATION_APPROVED = 'application-approved',
  APPLICATION_MOVED = 'application-moved',
  APPLICATION_REJECTED = 'application-rejected',
  GRANT_APPLICATION_ASSIGNEE_CHANGED = 'grant-application-assignee-changed',
  GRANT_CALL_INVITATION = 'grant-call-invitation',
  NEW_GRANT_APPLICATION = 'new-grant-application',
}

@Entity('user_notification_preferences')
export class UserNotificationPreferences {
  @ApiProperty({
    description: 'Unique identifier for the notification preference.',
    example: 1,
  })
  @PrimaryGeneratedColumn('increment')
  id: number;

  @ApiProperty({
    description: 'User associated with this notification preference.',
    type: () => User,
  })
  @ManyToOne(() => User, (user) => user.notificationPreferences, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ApiProperty({
    description: 'ID of the user. Foreign key referencing the User entity.',
    example: 123,
  })
  @Column()
  userId: number;

  @ApiProperty({
    description: 'Type of notification for this preference.',
    enum: NotificationType,
    enumName: 'NotificationType',
    example: NotificationType.NEW_GRANT_APPLICATION,
  })
  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  notificationType: NotificationType;

  @ApiProperty({
    description: 'Indicates whether this notification type is enabled for the user.',
    default: true,
    example: true,
  })
  @Column({ default: true })
  enabled: boolean;

  @ApiProperty({
    description: 'Date and time when this notification preference was created (ISO 8601 format).',
    example: '2024-03-15T10:30:00.000Z',
    format: 'date-time',
  })
  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @ApiProperty({
    description: 'Date and time when this notification preference was last updated (ISO 8601 format).',
    example: '2024-03-15T11:45:30.000Z',
    format: 'date-time',
  })
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
