import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserNotificationPreferences, NotificationType } from '../entities/user-notification-preferences.entity';
import { User } from '../../auth/entities/user.entity';

@Injectable()
export class NotificationPreferenceService {
  constructor(
    @InjectRepository(UserNotificationPreferences)
    private notificationPreferencesRepository: Repository<UserNotificationPreferences>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async getNotificationPreferences(userId: number): Promise<{ notificationPreferences: NotificationType[] }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const preferences = await this.notificationPreferencesRepository.find({
      // Fetch only enabled preferences
      where: { user: { id: userId }, enabled: true },
    });

    const notificationPreferences = preferences.map((pref) => pref.notificationType);

    return { notificationPreferences };
  }

  async updateNotificationPreferences(
    userId: number,
    preferencesDto: { [key in NotificationType]?: boolean } | undefined,
  ): Promise<{ updated: boolean }> {
    const user = await this.validateUser(userId);

    return this.notificationPreferencesRepository.manager.transaction(async (transactionalEntityManager) => {
      // Flag to track if any update was performed
      let updateMade = false;

      for (const notificationType in preferencesDto) {
        const enabled = preferencesDto[notificationType as NotificationType];

        if (enabled === undefined) {
          continue;
        }

        // Find existing preference within the transaction
        const preferences = await transactionalEntityManager.findOne(UserNotificationPreferences, {
          where: { user: { id: userId }, notificationType: notificationType as NotificationType },
          relations: ['user'],
        });

        if (preferences) {
          if (preferences.enabled !== enabled) {
            // Check if value actually changed before updating
            preferences.enabled = enabled;
            await transactionalEntityManager.save(UserNotificationPreferences, preferences);
            updateMade = true;
          }
        } else {
          const newPreference = transactionalEntityManager.create(UserNotificationPreferences, {
            user,
            notificationType: notificationType as NotificationType,
            enabled,
          });
          await transactionalEntityManager.save(UserNotificationPreferences, newPreference);
          updateMade = true;
        }
      }

      return { updated: updateMade }; // Return the update status
    });
  }

  private async validateUser(userId: number): Promise<User | undefined> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .where('user.id = :userId', { userId })
      .withDeleted()
      .getOne();

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    if (user.deletedAt) {
      throw new BadRequestException('Account is deleted and cannot be modified. Please recover your account first.');
    }

    return user;
  }
}
