import { Controller, Get, Put, Body, UseGuards, Req, Request } from '@nestjs/common';
import { ApiB<PERSON>erAuth, ApiOkResponse, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { NotificationPreferenceService } from './preferences.service';
import { AuthGuard } from '../../auth/auth.guard';
import { NotificationPreferencesDto } from '../dto/notification-preferences.dto';
import { User } from '../../auth/entities/user.entity';
import { NotificationType } from '../entities/user-notification-preferences.entity';
import { SuccessResponse } from '../../common/dto/success-response.dto';

interface AuthenticatedRequest extends Request {
  user: User;
}

@ApiTags('Notification Preferences')
@Controller('users/me/notification-preferences')
@ApiBearerAuth()
@UseGuards(AuthGuard)
export class NotificationPreferenceController {
  constructor(private readonly notificationPreferenceService: NotificationPreferenceService) {}

  @Get()
  @ApiOperation({ summary: 'Get current user notification preferences' })
  @ApiOkResponse({
    description:
      'Successful retrieval of enabled notification preferences. Returns an object with an array of enabled notification types.', // Modified description to emphasize "enabled"
    schema: {
      type: 'object',
      required: ['notificationPreferences'],
      properties: {
        notificationPreferences: {
          type: 'array',
          items: {
            type: 'string',
            enum: Object.values(NotificationType),
            description: 'Enabled notification types for the user.',
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getPreferences(@Req() req: AuthenticatedRequest): Promise<{ notificationPreferences: NotificationType[] }> {
    const userId = req.user.id;
    return this.notificationPreferenceService.getNotificationPreferences(userId);
  }

  @Put()
  @ApiOperation({ summary: 'Update current user notification preferences' })
  @ApiResponse({
    status: 200,
    description: 'Notification preferences updated successfully',
    type: SuccessResponse,
    example: { success: true, message: 'Notification preferences updated successfully' },
  })
  @ApiResponse({
    status: 200,
    description: 'No updates applied',
    type: SuccessResponse,
    example: {
      success: false,
      message:
        'No notification preferences were updated as no changes were provided or preferences were already set to the requested values.',
    },
  })
  @ApiResponse({ status: 400, description: 'Bad Request - Validation failed' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updatePreferences(@Req() req: AuthenticatedRequest, @Body() updatePreferencesDto: NotificationPreferencesDto) {
    const userId = req.user.id;
    const updateResult = await this.notificationPreferenceService.updateNotificationPreferences(
      userId,
      updatePreferencesDto.preferences,
    );

    if (updateResult.updated) {
      return { message: 'Notification preferences updated successfully', success: true };
    } else {
      return {
        success: false,
        message:
          'No notification preferences were updated as no changes were provided or preferences were already set to the requested values.',
      };
    }
  }
}
