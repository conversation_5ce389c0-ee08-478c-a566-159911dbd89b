import { Test, TestingModule } from '@nestjs/testing';
import { NotificationPreferenceService } from './preferences.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UserNotificationPreferences, NotificationType } from '../entities/user-notification-preferences.entity';
import { User } from '../../auth/entities/user.entity';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { EntityManager } from 'typeorm';

const mockUserNotificationPreferencesRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  manager: {
    transaction: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  },
});

const mockUserRepository = () => ({
  findOne: jest.fn(),
  save: jest.fn(),
  createQueryBuilder: jest.fn().mockReturnValue({
    where: jest.fn().mockReturnThis(),
    withDeleted: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
  }),
  manager: {
    transaction: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  },
});

describe('NotificationPreferenceService', () => {
  let service: NotificationPreferenceService;
  let notificationPreferencesRepository: ReturnType<typeof mockUserNotificationPreferencesRepository>;
  let userRepository: ReturnType<typeof mockUserRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationPreferenceService,
        {
          provide: getRepositoryToken(UserNotificationPreferences),
          useFactory: mockUserNotificationPreferencesRepository,
        },
        {
          provide: getRepositoryToken(User),
          useFactory: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<NotificationPreferenceService>(NotificationPreferenceService);
    notificationPreferencesRepository = module.get(getRepositoryToken(UserNotificationPreferences));
    userRepository = module.get(getRepositoryToken(User));

    // Mock transaction manager's transaction method
    notificationPreferencesRepository.manager.transaction.mockImplementation((fn) =>
      fn(notificationPreferencesRepository.manager),
    );

    // Mock transaction manager's transaction method
    userRepository.manager.transaction.mockImplementation(async (transactionCode) => {
      return await transactionCode(userRepository as unknown as EntityManager);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getNotificationPreferences', () => {
    it('should return an empty array if user has no enabled notification preferences', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;
      userRepository.findOne.mockResolvedValue(user);
      notificationPreferencesRepository.find.mockResolvedValue([]);

      const result = await service.getNotificationPreferences(userId);

      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(notificationPreferencesRepository.find).toHaveBeenCalledWith({
        where: { user: { id: userId }, enabled: true },
      });
      expect(result).toEqual({ notificationPreferences: [] });
    });

    it('should return an array of enabled notification types for a user', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;
      userRepository.findOne.mockResolvedValue(user);

      const mockPreferences = [
        {
          id: 1,
          notificationType: NotificationType.APPLICATION_APPROVED,
          enabled: true,
          user,
        } as UserNotificationPreferences,
        {
          id: 2,
          notificationType: NotificationType.APPLICATION_MOVED,
          enabled: true,
          user,
        } as UserNotificationPreferences,
        {
          id: 3,
          notificationType: NotificationType.APPLICATION_REJECTED,
          enabled: false,
          user,
        } as UserNotificationPreferences, // Disabled
      ];
      notificationPreferencesRepository.find.mockResolvedValue(mockPreferences.filter((p) => p.enabled));

      const result = await service.getNotificationPreferences(userId);

      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(notificationPreferencesRepository.find).toHaveBeenCalledWith({
        where: { user: { id: userId }, enabled: true },
      });
      expect(result).toEqual({
        notificationPreferences: [NotificationType.APPLICATION_APPROVED, NotificationType.APPLICATION_MOVED],
      });
    });

    it('should throw NotFoundException if user does not exist', async () => {
      const userId = 1;
      userRepository.findOne.mockResolvedValue(undefined);

      await expect(service.getNotificationPreferences(userId)).rejects.toThrowError(NotFoundException);
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(notificationPreferencesRepository.find).not.toHaveBeenCalled();
    });
  });

  describe('updateNotificationPreferences', () => {
    it('should return updated false if preferencesDto is empty', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;

      jest.spyOn(service as any, 'validateUser').mockResolvedValue(user);

      const result = await service.updateNotificationPreferences(userId, {});

      expect(service['validateUser']).toHaveBeenCalledWith(userId);
      expect(notificationPreferencesRepository.manager.transaction).toHaveBeenCalled();
      expect(notificationPreferencesRepository.manager.findOne).not.toHaveBeenCalled();
      expect(notificationPreferencesRepository.manager.create).not.toHaveBeenCalled();
      expect(notificationPreferencesRepository.manager.save).not.toHaveBeenCalled();
      expect(result).toEqual({ updated: false });
    });

    it('should update an existing notification preference to enabled', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;
      jest.spyOn(service as any, 'validateUser').mockResolvedValue(user);

      const existingPreference = {
        id: 1,
        notificationType: NotificationType.APPLICATION_MOVED,
        enabled: false, // Initially disabled
        user,
      } as UserNotificationPreferences;
      notificationPreferencesRepository.manager.findOne.mockResolvedValue(existingPreference);
      notificationPreferencesRepository.manager.save.mockResolvedValue(existingPreference);

      const preferencesDto = { [NotificationType.APPLICATION_MOVED]: true }; // Enable APPLICATION_MOVED
      const result = await service.updateNotificationPreferences(userId, preferencesDto);

      expect(service['validateUser']).toHaveBeenCalledWith(userId);
      expect(notificationPreferencesRepository.manager.transaction).toHaveBeenCalledWith(expect.any(Function));
      expect(notificationPreferencesRepository.manager.findOne).toHaveBeenCalledWith(UserNotificationPreferences, {
        where: { user: { id: userId }, notificationType: NotificationType.APPLICATION_MOVED },
        relations: ['user'],
      });
      expect(existingPreference.enabled).toBe(true);
      expect(notificationPreferencesRepository.manager.save).toHaveBeenCalledWith(
        UserNotificationPreferences,
        existingPreference,
      );
      expect(result).toEqual({ updated: true });
    });

    it('should update an existing notification preference to disabled', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;
      userRepository.findOne.mockResolvedValue(user);

      const existingPreference = {
        id: 1,
        notificationType: NotificationType.APPLICATION_APPROVED,
        enabled: true, // Initially enabled
        user,
      } as UserNotificationPreferences;
      jest.spyOn(service as any, 'validateUser').mockResolvedValue(user);

      notificationPreferencesRepository.manager.findOne.mockResolvedValue(existingPreference);
      notificationPreferencesRepository.save.mockResolvedValue(existingPreference);

      const preferencesDto = { [NotificationType.APPLICATION_APPROVED]: false };
      const result = await service.updateNotificationPreferences(userId, preferencesDto);

      expect(service['validateUser']).toHaveBeenCalledWith(userId);
      expect(notificationPreferencesRepository.manager.transaction).toHaveBeenCalledWith(expect.any(Function));
      expect(notificationPreferencesRepository.manager.findOne).toHaveBeenCalledWith(UserNotificationPreferences, {
        where: { user: { id: userId }, notificationType: NotificationType.APPLICATION_APPROVED },
        relations: ['user'],
      });
      expect(existingPreference.enabled).toBe(false); // Verify preference object was updated
      expect(notificationPreferencesRepository.manager.save).toHaveBeenCalledWith(
        UserNotificationPreferences,
        existingPreference,
      );
      expect(result).toEqual({ updated: true });
    });

    it('should return updated false if preference is already set to requested value', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;
      userRepository.findOne.mockResolvedValue(user);

      const existingPreference = {
        id: 1,
        notificationType: NotificationType.APPLICATION_APPROVED,
        enabled: true, // Already enabled
        user,
      } as UserNotificationPreferences;

      jest.spyOn(service as any, 'validateUser').mockResolvedValue(user);
      notificationPreferencesRepository.manager.findOne.mockResolvedValue(existingPreference);

      const preferencesDto = { [NotificationType.APPLICATION_APPROVED]: true }; // Request to enable when already enabled
      const result = await service.updateNotificationPreferences(userId, preferencesDto);

      expect(service['validateUser']).toHaveBeenCalledWith(userId);
      expect(notificationPreferencesRepository.manager.transaction).toHaveBeenCalled();
      expect(notificationPreferencesRepository.manager.findOne).toHaveBeenCalledWith(UserNotificationPreferences, {
        where: { user: { id: userId }, notificationType: NotificationType.APPLICATION_APPROVED },
        relations: ['user'],
      });
      expect(notificationPreferencesRepository.save).not.toHaveBeenCalled(); // Save should not be called
      expect(result).toEqual({ updated: false });
    });

    it('should create a new notification preference if it does not exist and set to enabled', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;
      userRepository.findOne.mockResolvedValue(user);

      notificationPreferencesRepository.manager.findOne.mockResolvedValue(undefined); // No existing preference
      const newPreference = {
        id: 2,
        notificationType: NotificationType.APPLICATION_MOVED,
        enabled: true,
        user,
      } as UserNotificationPreferences;

      jest.spyOn(service as any, 'validateUser').mockResolvedValue(user);

      notificationPreferencesRepository.manager.create.mockReturnValue(newPreference);
      notificationPreferencesRepository.manager.save.mockResolvedValue(newPreference);

      const preferencesDto = { [NotificationType.APPLICATION_MOVED]: true };
      const result = await service.updateNotificationPreferences(userId, preferencesDto);

      expect(service['validateUser']).toHaveBeenCalledWith(userId);
      expect(notificationPreferencesRepository.manager.transaction).toHaveBeenCalled();
      expect(notificationPreferencesRepository.manager.findOne).toHaveBeenCalledWith(UserNotificationPreferences, {
        where: { user: { id: userId }, notificationType: NotificationType.APPLICATION_MOVED },
        relations: ['user'],
      });
      expect(notificationPreferencesRepository.manager.create).toHaveBeenCalledWith(UserNotificationPreferences, {
        user,
        notificationType: NotificationType.APPLICATION_MOVED,
        enabled: true,
      });
      expect(notificationPreferencesRepository.manager.save).toHaveBeenCalledWith(
        UserNotificationPreferences,
        newPreference,
      );
      expect(result).toEqual({ updated: true });
    });

    it('should create a new notification preference if it does not exist and set to disabled', async () => {
      const userId = 1;
      const user = new User();
      user.id = userId;
      userRepository.findOne.mockResolvedValue(user);

      notificationPreferencesRepository.manager.findOne.mockResolvedValue(undefined); // No existing preference
      const newPreference = {
        id: 2,
        notificationType: NotificationType.APPLICATION_MOVED,
        enabled: false,
        user,
      } as UserNotificationPreferences;
      jest.spyOn(service as any, 'validateUser').mockResolvedValue(user);

      notificationPreferencesRepository.manager.create.mockReturnValue(newPreference);
      notificationPreferencesRepository.manager.save.mockResolvedValue(newPreference);

      const preferencesDto = { [NotificationType.APPLICATION_MOVED]: false };
      const result = await service.updateNotificationPreferences(userId, preferencesDto);

      expect(notificationPreferencesRepository.manager.transaction).toHaveBeenCalled();
      expect(notificationPreferencesRepository.manager.findOne).toHaveBeenCalledWith(UserNotificationPreferences, {
        where: { user: { id: userId }, notificationType: NotificationType.APPLICATION_MOVED },
        relations: ['user'],
      });
      expect(notificationPreferencesRepository.manager.create).toHaveBeenCalledWith(UserNotificationPreferences, {
        user,
        notificationType: NotificationType.APPLICATION_MOVED,
        enabled: false,
      });
      expect(notificationPreferencesRepository.manager.save).toHaveBeenCalledWith(
        UserNotificationPreferences,
        newPreference,
      );
      expect(result).toEqual({ updated: true });
    });

    it('should throw NotFoundException if user does not exist during update', async () => {
      const userId = 1;
      jest.spyOn(userRepository.createQueryBuilder(), 'getOne').mockResolvedValue(undefined);

      await expect((service as any).validateUser(userId)).rejects.toThrowError(NotFoundException);
      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(2);
      expect(userRepository.createQueryBuilder().getOne).toHaveBeenCalled();
    });

    it('should throw BadRequestException if user is deleted', async () => {
      const userId = 1;
      const mockDeletedUser = { id: userId, deletedAt: new Date() };
      jest.spyOn(userRepository.createQueryBuilder(), 'getOne').mockResolvedValue(mockDeletedUser);

      await expect((service as any).validateUser(userId)).rejects.toThrowError(BadRequestException);
      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(2);
      expect(userRepository.createQueryBuilder().getOne).toHaveBeenCalled();
    });
  });
});
