import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('App')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiResponse({
    status: 200,
    description: 'The app is running',
  })
  @ApiOperation({
    summary: 'Check if the app is running',
  })
  getHello() {
    return this.appService.getHello();
  }
}
