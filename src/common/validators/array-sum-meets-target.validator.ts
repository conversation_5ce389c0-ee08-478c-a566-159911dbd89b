import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  isArray,
  isNumber,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ name: 'isArraySumEqualTo100', async: false })
export class IsArraySumMeetsTargetConstraint implements ValidatorConstraintInterface {
  validate(values: unknown) {
    if (!isArray(values) || !values.every((v) => isNumber(v))) {
      return true;
    }

    const targetSum = 100;
    const tolerance = 0.01;
    const actualSum = values.reduce((sum, value) => sum + value, 0);

    return Math.abs(actualSum - targetSum) < tolerance;
  }

  defaultMessage(args: ValidationArguments) {
    let actualSum = 'N/A';
    if (isArray(args.value)) {
      actualSum = args.value.reduce((sum, v) => sum + (isNumber(v) ? v : 0), 0).toFixed(2);
    }
    return `Sum of distribution percentages must be exactly 100% (calculated: ${actualSum}%)`;
  }
}

export function IsArraySumEqualTo100(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsArraySumMeetsTargetConstraint,
    });
  };
}
