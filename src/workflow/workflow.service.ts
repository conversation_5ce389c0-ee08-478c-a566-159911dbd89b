import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkflowStepDefinition } from './entities/workflow-step-definition.entity';
import { WorkflowTemplate } from './entities/workflow-template.entity';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { WorkflowEntityType } from './enums/workflow-entity-type.enum';
import { StageDefinitionResponseDto } from './dto/stage-definition.response.dto';
import { StageWithCountResponseDto } from './dto/stage-with-count.response.dto';
import { EntityWorkflowConfig } from './types/workflow-config.types';

@Injectable()
export class WorkflowService {
  private _entityConfigs: ReadonlyMap<WorkflowEntityType, EntityWorkflowConfig> | null = null;

  constructor(
    @InjectRepository(WorkflowStepDefinition)
    private readonly stepDefRepository: Repository<WorkflowStepDefinition>,
    @InjectRepository(WorkflowTemplate)
    private readonly templateRepository: Repository<WorkflowTemplate>,
    @InjectRepository(GrantCall)
    private readonly grantCallRepository: Repository<GrantCall>,
    @InjectRepository(GrantApplication)
    private readonly grantApplicationRepository: Repository<GrantApplication>,
    @InjectRepository(GrantProgram)
    private readonly grantProgramRepository: Repository<GrantProgram>,
  ) {}

  async getStageDefinitionsForEntityType(entityType: WorkflowEntityType): Promise<StageDefinitionResponseDto[]> {
    const template = await this.templateRepository.findOneBy({ entityType: entityType });

    const definitions = await this.stepDefRepository.find({
      where: { workflowTemplateId: template.id },
      order: { sequenceNumber: 'ASC' },
    });

    return definitions.map((def) => this.stageDefinitionToDto(def));
  }

  public stageDefinitionToDto(stepDefinition: WorkflowStepDefinition): StageDefinitionResponseDto {
    return {
      id: stepDefinition.id,
      workflowTemplateId: stepDefinition.workflowTemplateId,
      name: stepDefinition.name,
      code: stepDefinition.code,
      position: stepDefinition.sequenceNumber,
      transitionType: stepDefinition.transitionType,
      description: stepDefinition.description,
      isTerminal: stepDefinition.isTerminal,
    };
  }

  async getStageSummariesWithCounts(
    stagesEntityType: WorkflowEntityType,
    filterCountsByParentId?: number,
  ): Promise<StageWithCountResponseDto[]> {
    const template = await this.templateRepository.findOneBy({ entityType: stagesEntityType });

    const stageDefinitions = await this.stepDefRepository.find({
      where: { workflowTemplateId: template.id },
      order: { sequenceNumber: 'ASC' },
    });

    const countConfig = this.entityConfigs.get(stagesEntityType);
    const queryBuilder = countConfig.repository.createQueryBuilder('item');
    const stageDefIds = stageDefinitions.map((def) => def.id).filter((id) => id != null);

    queryBuilder
      .innerJoin('item.workflowState', 'wfState')
      .select('wfState.currentStepDefinitionId', 'stageDefinitionId')
      .addSelect('COUNT(item.id)', 'itemCount')
      .where('wfState.currentStepDefinitionId IN (:...stageDefIds)', { stageDefIds });

    if (filterCountsByParentId) {
      queryBuilder.andWhere(`item.${countConfig.parentForeignKeyField} = :parentId`, {
        parentId: filterCountsByParentId,
      });
    }

    queryBuilder.groupBy('wfState.currentStepDefinitionId');

    const countsData = await queryBuilder.getRawMany();
    const countsMap = new Map<number, number>();
    countsData.forEach((row) => {
      if (row.stageDefinitionId) {
        countsMap.set(row.stageDefinitionId, parseInt(row.itemCount, 10));
      }
    });

    return stageDefinitions.map((s) => this.mapStepDefinitionToDto(s, countsMap.get(s.id)));
  }

  async countChildEntities(childEntityType: WorkflowEntityType, filterCountsByParentId?: number) {
    const childConfig = this.entityConfigs.get(childEntityType);

    const whereCondition = filterCountsByParentId
      ? {
          [childConfig.parentForeignKeyField]: {
            [childConfig.parentRelationIdField]: filterCountsByParentId,
          },
        }
      : {};

    return childConfig.repository.count({ where: whereCondition });
  }

  public mapStepDefinitionToDto(def: WorkflowStepDefinition, count?: number): StageWithCountResponseDto {
    return {
      stageDefinitionId: def.id,
      stageCode: def.code,
      stageName: def.name,
      position: def.sequenceNumber ?? 0,
      count: count ?? 0,
    };
  }

  private get entityConfigs(): ReadonlyMap<WorkflowEntityType, EntityWorkflowConfig> {
    if (!this._entityConfigs) {
      this._entityConfigs = new Map<WorkflowEntityType, EntityWorkflowConfig>([
        [
          WorkflowEntityType.APPLICATION,
          {
            repository: this.grantApplicationRepository,
            parentForeignKeyField: 'grantCall',
            parentRelationIdField: 'id',
            expectedParentType: WorkflowEntityType.CALL,
          },
        ],
        [
          WorkflowEntityType.CALL,
          {
            repository: this.grantCallRepository,
            parentForeignKeyField: 'grantProgram',
            parentRelationIdField: 'id',
            expectedParentType: WorkflowEntityType.PROGRAM,
          },
        ],
        [
          WorkflowEntityType.PROGRAM,
          {
            repository: this.grantProgramRepository,
          },
        ],
      ]);
    }
    return this._entityConfigs;
  }
}
