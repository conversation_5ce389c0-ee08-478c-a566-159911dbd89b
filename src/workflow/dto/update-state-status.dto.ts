import { <PERSON><PERSON>num, IsNotEmpty, <PERSON><PERSON><PERSON>In, IsString, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { WorkflowStatus } from '../enums/workflow-status.enum';

export class UpdateStateStatusDto {
  @ApiProperty({ enum: WorkflowStatus, enumName: 'WorkflowStatus' })
  @IsNotEmpty()
  @IsEnum(WorkflowStatus)
  @IsNotIn([WorkflowStatus.WITHDRAWN], {
    message: `Coordinator cannot change status to '${WorkflowStatus.WITHDRAWN}'. Application owners should use the dedicated withdraw endpoint.`,
  })
  status: WorkflowStatus;

  @ApiProperty({ description: 'Reason for the action', type: String })
  @IsNotEmpty()
  @IsString()
  @MaxLength(1000)
  reason: string;
}
