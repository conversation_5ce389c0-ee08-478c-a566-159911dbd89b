import { ApiProperty } from '@nestjs/swagger';
import { StageCode } from '../enums/stage-code.enum';
import { StageTransitionType } from '../enums/stage-transition-type.enum';

export class StageDefinitionResponseDto {
  @ApiProperty({ example: 1, description: 'Unique ID of the workflow stage definition.' })
  id: number;

  @ApiProperty({ example: 2, description: 'ID of the workflow template this stage belongs to.' })
  workflowTemplateId: number;

  @ApiProperty({ example: 'Open for Applications', description: 'name of the stage.' })
  name: string;

  @ApiProperty({
    description: 'Unique code identifying the stage.',
    enum: StageCode,
    enumName: 'StageCode',
    example: StageCode.GC_OPEN_FOR_APPLICATIONS,
  })
  code: StageCode;

  @ApiProperty({ example: 10, description: 'Order of the stage within its workflow template.' })
  position: number;

  @ApiProperty({
    description: 'Mechanism for transitioning out of this stage.',
    enum: StageTransitionType,
    enumName: 'StageTransitionType',
    example: StageTransitionType.MANUAL,
  })
  transitionType: StageTransitionType;

  @ApiProperty({ required: false, nullable: true, description: 'Optional detailed description of the stage.' })
  description?: string | null;

  @ApiProperty({ example: false, description: 'Indicates if this is a final stage in the workflow.' })
  isTerminal: boolean;
}
