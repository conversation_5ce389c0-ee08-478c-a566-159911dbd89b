import { ApiProperty } from '@nestjs/swagger';
import { GrantApplicationStageCode, StageCode } from '../enums/stage-code.enum';

export class StageWithCountResponseDto {
  @ApiProperty({ example: 1, description: 'Unique ID of the workflow stage definition.' })
  stageDefinitionId: number;

  @ApiProperty({
    enum: GrantApplicationStageCode,
    enumName: 'GrantApplicationStageCode',
    example: GrantApplicationStageCode.INTERVIEW,
    description: 'Code identifying the stage.',
  })
  stageCode: StageCode;

  @ApiProperty({ example: 'Application Review', description: 'name of the stage.' })
  stageName: string;

  @ApiProperty({ example: 20, description: 'Order of the stage within its workflow.' })
  position: number;

  @ApiProperty({ example: 15, description: 'Number of items currently in this stage.' })
  count: number;
}
