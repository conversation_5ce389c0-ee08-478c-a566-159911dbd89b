import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

import { StageCode } from '../enums/stage-code.enum';
import { StageTransitionType } from '../enums/stage-transition-type.enum';
import { WorkflowTemplate } from './workflow-template.entity';

@Entity('workflow_step_definitions')
@Index(['workflowTemplateId', 'sequenceNumber'], { unique: true })
@Index(['workflowTemplateId', 'code'], { unique: true })
export class WorkflowStepDefinition {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'int' })
  workflowTemplateId: number;

  @ManyToOne(() => WorkflowTemplate, (template) => template.steps, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  @JoinColumn({ name: 'workflowTemplateId' })
  workflowTemplate: WorkflowTemplate;

  @Column({ length: 100 })
  name: string;

  @Column({
    type: 'enum',
    enum: StageCode,
    enumName: 'StageCode',
  })
  code: StageCode;

  @Column({ type: 'int' })
  sequenceNumber: number;

  @Column({
    type: 'enum',
    enum: StageTransitionType,
    enumName: 'StageTransitionType',
    default: StageTransitionType.MANUAL,
  })
  transitionType: StageTransitionType;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'boolean', default: false })
  isTerminal: boolean;
}
