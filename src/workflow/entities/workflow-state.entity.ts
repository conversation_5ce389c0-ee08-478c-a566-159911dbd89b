import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';

import { WorkflowStatus } from '../enums/workflow-status.enum';
import { WorkflowStepDefinition } from './workflow-step-definition.entity';
import { WorkflowTemplate } from './workflow-template.entity';

@Entity('workflow_state')
export class WorkflowState {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'int', nullable: true })
  workflowTemplateId: number | null;

  @ManyToOne(() => WorkflowTemplate, {
    nullable: true,
    onDelete: 'RESTRICT',
    eager: false,
  })
  @JoinColumn({ name: 'workflowTemplateId' })
  workflowTemplate: WorkflowTemplate | null;

  @Column({ type: 'int', nullable: true })
  currentStepDefinitionId: number | null;

  @ManyToOne(() => WorkflowStepDefinition, {
    nullable: true,
    onDelete: 'RESTRICT',
    eager: false,
  })
  @JoinColumn({ name: 'currentStepDefinitionId', referencedColumnName: 'id' })
  currentStepDefinition: WorkflowStepDefinition | null;

  @Column({
    type: 'enum',
    enum: WorkflowStatus,
    enumName: 'WorkflowStatus',
    default: WorkflowStatus.IN_PROGRESS,
    nullable: false,
  })
  status: WorkflowStatus;

  @Column({ type: 'timestamptz', nullable: true })
  currentStepTransitionedAt: Date | null;

  @Column({ type: 'timestamptz', nullable: true })
  currentStepEndsAt: Date | null;
}
