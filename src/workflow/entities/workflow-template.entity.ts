import { Column, Entity, Index, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

import { WorkflowEntityType } from '../enums/workflow-entity-type.enum';
import { WorkflowStepDefinition } from './workflow-step-definition.entity';

@Entity('workflow_templates')
export class WorkflowTemplate {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Index()
  @Column({ length: 150, unique: true })
  name: string;

  @Column({
    type: 'enum',
    enum: WorkflowEntityType,
    enumName: 'WorkflowEntityType',
  })
  entityType: WorkflowEntityType;

  @OneToMany(() => WorkflowStepDefinition, (step) => step.workflowTemplate, {
    cascade: true,
    eager: false,
  })
  steps: WorkflowStepDefinition[];
}
