export enum StageCode {
  GP_OPEN = 'GP_O<PERSON><PERSON>',
  GP_FINALIZED = 'GP_FINALIZED',

  GC_CLOSED = 'GC_CLOSED',
  GC_OPEN_FOR_APPLICATIONS = 'GC_OPEN_FOR_APPLICATIONS',
  GC_SCREENING = 'GC_SCREENING',
  GC_COMMUNITY_VOTING = 'GC_COMMUNITY_VOTING',
  GC_ONBOARDING = 'GC_ONBOARDING',
  GC_FINAL_COMMUNITY_VOTING = 'GC_FINAL_COMMUNITY_VOTING',
  GC_FINALIZED = 'GC_FINALIZED',

  GA_SCREENING = 'GA_SCREENING',
  GA_QUALIFICATION = 'GA_QUALIFICATION',
  GA_INTERVIEW = 'GA_INTERVIEW',
  GA_DUE_DILIGENCE = 'GA_DUE_DILIGENCE',
  GA_TOWN_HALL = 'GA_TOWN_HALL',
  GA_FINAL_QUALIFICATION = 'GA_FINAL_QUALIFICATION',
}

export enum GrantProgramStageCode {
  OPEN = 'GP_OPEN',
  FINALIZED = 'GP_FINALIZED',
}

export enum GrantCallStageCode {
  CLOSED = 'GC_CLOSED',
  OPEN_FOR_APPLICATIONS = 'GC_OPEN_FOR_APPLICATIONS',
  SCREENING = 'GC_SCREENING',
  COMMUNITY_VOTING = 'GC_COMMUNITY_VOTING',
  ONBOARDING = 'GC_ONBOARDING',
  FINAL_COMMUNITY_VOTING = 'GC_FINAL_COMMUNITY_VOTING',
  FINALIZED = 'GC_FINALIZED',
}

export enum GrantApplicationStageCode {
  SCREENING = 'GA_SCREENING',
  QUALIFICATION = 'GA_QUALIFICATION',
  INTERVIEW = 'GA_INTERVIEW',
  DUE_DILIGENCE = 'GA_DUE_DILIGENCE',
  TOWN_HALL = 'GA_TOWN_HALL',
  FINAL_QUALIFICATION = 'GA_FINAL_QUALIFICATION',
}
