import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { Grant<PERSON>all } from '../grant-call/entities/grant-call.entity';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkflowService } from './workflow.service';
import { WorkflowStepDefinition } from './entities/workflow-step-definition.entity';
import { WorkflowTemplate } from './entities/workflow-template.entity';
import { WorkflowTransitionService } from './workflow-transition.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([WorkflowTemplate, Grant<PERSON>all, GrantApplication, GrantProgram, WorkflowStepDefinition]),
  ],
  providers: [WorkflowService, WorkflowTransitionService],
  exports: [WorkflowService, WorkflowTransitionService],
})
export class WorkflowModule {}
