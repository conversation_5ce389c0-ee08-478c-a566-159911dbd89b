import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';

import { JwtScope } from './auth.service';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authToken = this.extractTokenFromHeader(request);
    if (!authToken) {
      throw new UnauthorizedException('No session token provided');
    }
    let decodedData;
    try {
      decodedData = await this.jwtService.verifyAsync(authToken);
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
    if (decodedData.payload.scope !== JwtScope.SESSION_TOKEN) {
      throw new UnauthorizedException('Invalid token scope');
    }
    if (
      !decodedData.payload.emailVerified &&
      request.path !== '/auth/send-otp' &&
      request.path !== '/auth/verify-otp'
    ) {
      // If the accounts email address was not yet verified we deny access to all endpoints besides the ones of the
      // email verification flow
      throw new UnauthorizedException('Email not verified');
    }

    request['user'] = decodedData.payload;
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
