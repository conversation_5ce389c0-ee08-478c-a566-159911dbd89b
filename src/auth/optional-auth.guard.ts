import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';

import { JwtScope } from './auth.service';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { RequestUserPayload } from './request-user.decorator';

@Injectable()
export class OptionalAuthGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const authToken = this.extractTokenFromHeader(request);

    if (authToken) {
      try {
        const decodedData = await this.jwtService.verifyAsync(authToken);
        if (decodedData?.payload?.scope === JwtScope.SESSION_TOKEN && decodedData?.payload?.emailVerified) {
          request['user'] = decodedData.payload as RequestUserPayload;
        }
      } catch (error) {
        throw new UnauthorizedException(error.message);
      }
    }

    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
