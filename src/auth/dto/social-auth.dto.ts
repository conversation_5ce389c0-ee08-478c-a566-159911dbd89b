import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, Is<PERSON><PERSON>, ValidateNested, IsNotEmpty, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { AuthenticatedUserDto } from './authenticated-user.dto';

export class SocialAuthUserDto {
  @ApiProperty({
    example: '<PERSON>',
    description: "The user's full name.",
  })
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: "The user's email address.",
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    example: 'https://example.com/profile.jpg',
    description: "The URL of the user's profile image.",
  })
  @IsOptional()
  @IsString()
  image: string;

  @ApiProperty({
    example: '0.0.4501344',
    description: "The user's wallet address with which they want to sign up.",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description: 'The JWT token that was sent from the backend after requesting the challenge.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  challengeToken: string;

  @ApiProperty({
    example:
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c',
    description: "The signature that was generated by the user's wallet during the request-challenge process.",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  signature: string;
}

export class SocialLoginRequestDto {
  @ApiProperty({
    type: SocialAuthUserDto,
    description: 'User information obtained from the social provider.',
  })
  @ValidateNested()
  @Type(() => SocialAuthUserDto)
  @IsNotEmpty()
  user: SocialAuthUserDto;

  @ApiProperty({
    example: '2023-12-31T23:59:59.999Z',
    description: 'The expiration date and time of the token.',
  })
  @IsNotEmpty()
  @IsString()
  expires: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1Ni...example',
    description: 'ID token received from the social provider.',
  })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({
    example: 'google',
    description: 'The social login provider used for authentication.',
    enum: ['google', 'linkedin', 'github', 'twitter'],
    enumName: 'LoginProvider',
  })
  @IsNotEmpty()
  @IsEnum(['google', 'linkedin', 'github', 'twitter'])
  provider: 'google' | 'linkedin' | 'github' | 'twitter';
}

export class SocialAuthResponse {
  @ApiProperty({
    example: true,
    description: 'Whether the user was successfully signed in.',
  })
  @IsNotEmpty()
  @IsBoolean()
  success: boolean;

  @ApiProperty({
    example: true,
    description:
      'Indicates whether the user is a new user or an existing user. If true, the user is new and has just been registered. If false, the user is an existing user who has successfully signed in.',
  })
  @IsNotEmpty()
  @IsBoolean()
  isNewUser: boolean;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description: "User session token containing a user ID and the user's role.",
  })
  @IsNotEmpty()
  sessionToken: string;

  @ApiProperty({
    type: AuthenticatedUserDto,
    description: 'Information about the signed in user',
  })
  @IsNotEmpty()
  @ValidateNested()
  user: AuthenticatedUserDto;
}
