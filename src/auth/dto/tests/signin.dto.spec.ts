import { validate } from 'class-validator';
import { SignIn } from '../signin.dto';

describe('SignIn DTO', () => {
  it('should validate successfully with valid input', async () => {
    const signInDto = new SignIn();
    signInDto.address = '0.0.4501344';
    signInDto.challengeToken = 'valid-token';
    signInDto.signature = 'valid-signature';

    const errors = await validate(signInDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if address is missing', async () => {
    const signInDto = new SignIn();
    signInDto.challengeToken = 'valid-token';
    signInDto.signature = 'valid-signature';

    const errors = await validate(signInDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if challengeToken is missing', async () => {
    const signInDto = new SignIn();
    signInDto.address = '0.0.4501344';
    signInDto.signature = 'valid-signature';

    const errors = await validate(signInDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if signature is missing', async () => {
    const signInDto = new SignIn();
    signInDto.address = '0.0.4501344';
    signInDto.challengeToken = 'valid-token';

    const errors = await validate(signInDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signature');
  });

  it('should fail validation if address is not a string', async () => {
    const signInDto = new SignIn();
    signInDto.address = 12345 as any;
    signInDto.challengeToken = 'valid-token';
    signInDto.signature = 'valid-signature';

    const errors = await validate(signInDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if challengeToken is not a string', async () => {
    const signInDto = new SignIn();
    signInDto.address = '0.0.4501344';
    signInDto.challengeToken = 12345 as any;
    signInDto.signature = 'valid-signature';

    const errors = await validate(signInDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if signature is not a string', async () => {
    const signInDto = new SignIn();
    signInDto.address = '0.0.4501344';
    signInDto.challengeToken = 'valid-token';
    signInDto.signature = 12345 as any;

    const errors = await validate(signInDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signature');
  });
});
