import { validate } from 'class-validator';
import { RequestChallenge, RequestChallengeResponse } from '../request-challenge.dto';

describe('RequestChallenge DTO', () => {
  it('should validate successfully with valid input', async () => {
    const requestChallengeDto = new RequestChallenge();
    requestChallengeDto.address = '0.0.4501344';

    const errors = await validate(requestChallengeDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if address is missing', async () => {
    const requestChallengeDto = new RequestChallenge();

    const errors = await validate(requestChallengeDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if address is not a string', async () => {
    const requestChallengeDto = new RequestChallenge();
    requestChallengeDto.address = 12345 as any;

    const errors = await validate(requestChallengeDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });
});

describe('RequestChallengeResponse DTO', () => {
  it('should validate successfully with valid input', async () => {
    const requestChallengeResponse = new RequestChallengeResponse();
    requestChallengeResponse.success = true;
    requestChallengeResponse.challenge = 'example.com wants you to sign in with your Hedera account: 0.0.4422816...';
    requestChallengeResponse.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    requestChallengeResponse.accountExists = true;
    requestChallengeResponse.accountDeleted = false;

    const errors = await validate(requestChallengeResponse);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if success is missing', async () => {
    const requestChallengeResponse = new RequestChallengeResponse();
    requestChallengeResponse.challenge = 'example.com wants you to sign in with your Hedera account: 0.0.4422816...';
    requestChallengeResponse.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    requestChallengeResponse.accountExists = true;

    const errors = await validate(requestChallengeResponse);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('success');
  });

  it('should fail validation if challenge is missing', async () => {
    const requestChallengeResponse = new RequestChallengeResponse();
    requestChallengeResponse.success = true;
    requestChallengeResponse.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    requestChallengeResponse.accountExists = true;

    const errors = await validate(requestChallengeResponse);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challenge');
  });

  it('should fail validation if challengeToken is missing', async () => {
    const requestChallengeResponse = new RequestChallengeResponse();
    requestChallengeResponse.success = true;
    requestChallengeResponse.challenge = 'example.com wants you to sign in with your Hedera account: 0.0.4422816...';
    requestChallengeResponse.accountExists = true;

    const errors = await validate(requestChallengeResponse);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if accountExists is missing', async () => {
    const requestChallengeResponse = new RequestChallengeResponse();
    requestChallengeResponse.success = true;
    requestChallengeResponse.challenge = 'example.com wants you to sign in with your Hedera account: 0.0.4422816...';
    requestChallengeResponse.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(requestChallengeResponse);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('accountExists');
  });
});
