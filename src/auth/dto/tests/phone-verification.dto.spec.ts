import { validate } from 'class-validator';
import { PhoneNumberDto, VerifyPhoneNumberDto } from '../phone-verification.dto';
import { getCountries } from 'libphonenumber-js';

describe('PhoneNumberDto', () => {
  describe('phoneNumber', () => {
    it('should pass validation for a valid phone number', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '1234567890';
      dto.countryCode = 'US';
      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should fail validation for an empty phone number', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '';
      dto.countryCode = 'US';
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        matches: 'phoneNumber must contain only digits (1 to 15 digits)',
        isNotEmpty: 'phoneNumber should not be empty',
      });
    });

    it('should fail validation for a phone number with non-digit characters', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '************';
      dto.countryCode = 'US';
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        matches: 'phoneNumber must contain only digits (1 to 15 digits)',
      });
    });

    it('should fail validation for a phone number that is too long', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '1234567890123456'; // 16 digits
      dto.countryCode = 'US';
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        matches: 'phoneNumber must contain only digits (1 to 15 digits)',
      });
    });

    it('should pass validation for a phone number with 1 digit', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '1';
      dto.countryCode = 'US';
      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should pass validation for a phone number with 15 digits', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '123456789012345'; // 15 digits
      dto.countryCode = 'US';
      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });
  });

  describe('countryCode', () => {
    it('should pass validation for a valid country code', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '1234567890';
      dto.countryCode = 'US';
      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should fail validation for an empty country code', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '1234567890';
      dto.countryCode = '' as any;
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        isEnum: `countryCode must be one of the following values: ${getCountries().join(', ')}`,
      });
    });

    it('should fail validation for an invalid country code', async () => {
      const dto = new PhoneNumberDto();
      dto.phoneNumber = '1234567890';
      dto.countryCode = 'ZZ' as any;
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        isEnum: `countryCode must be one of the following values: ${getCountries().join(', ')}`,
      });
    });
  });
});

describe('VerifyPhoneNumberDto', () => {
  describe('otp', () => {
    it('should pass validation for a valid 6-digit OTP', async () => {
      const dto = new VerifyPhoneNumberDto();
      dto.otp = '123456';
      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should fail validation for an empty OTP', async () => {
      const dto = new VerifyPhoneNumberDto();
      dto.otp = '';
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        isLength: 'otp must be longer than or equal to 6 characters',
        isNotEmpty: 'otp should not be empty',
        matches: 'OTP must be a 6-digit number',
      });
    });

    it('should fail validation for an OTP with non-digit characters', async () => {
      const dto = new VerifyPhoneNumberDto();
      dto.otp = '123abc';
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        matches: 'OTP must be a 6-digit number',
      });
    });

    it('should fail validation for an OTP that is too short', async () => {
      const dto = new VerifyPhoneNumberDto();
      dto.otp = '12345';
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        isLength: 'otp must be longer than or equal to 6 characters',
        matches: 'OTP must be a 6-digit number',
      });
    });

    it('should fail validation for an OTP that is too long', async () => {
      const dto = new VerifyPhoneNumberDto();
      dto.otp = '1234567';
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toEqual({
        isLength: 'otp must be shorter than or equal to 6 characters',
        matches: 'OTP must be a 6-digit number',
      });
    });
  });
});
