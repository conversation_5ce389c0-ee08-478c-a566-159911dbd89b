import { validate } from 'class-validator';
import { AuthenticatedUserDto, FetchUserResponse } from '../authenticated-user.dto';
import { UserRole } from '../../entities/user.entity';

describe('AuthenticatedUserDto', () => {
  it('should validate successfully with valid input', async () => {
    // Arrange: Create a valid AuthenticatedUserDto object
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.addresses = ['0.0.4501344'];
    authenticatedUserDto.email = '<EMAIL>';
    authenticatedUserDto.phoneNumber = '+15551234567';
    authenticatedUserDto.emailVerified = true;
    authenticatedUserDto.isPhoneVerified = true;
    authenticatedUserDto.createdAt = new Date('2021-07-01T00:00:00.000Z');
    authenticatedUserDto.role = UserRole.USER;

    // Act: Validate the object
    const errors = await validate(authenticatedUserDto);

    // Assert: Expect no validation errors
    expect(errors.length).toBe(0);
  });

  it('should fail validation if email is missing', async () => {
    // Arrange: Create an AuthenticatedUserDto object with missing email
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.addresses = ['0.0.4501344'];
    authenticatedUserDto.emailVerified = true;
    authenticatedUserDto.createdAt = new Date('2021-07-01T00:00:00.000Z');
    authenticatedUserDto.role = UserRole.USER;

    // Act: Validate the object
    const errors = await validate(authenticatedUserDto);

    // Assert: Expect validation errors for missing email
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });

  it('should fail validation if emailVerified is missing', async () => {
    // Arrange: Create an AuthenticatedUserDto object with missing emailVerified
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.addresses = ['0.0.4501344'];
    authenticatedUserDto.email = '<EMAIL>';
    authenticatedUserDto.createdAt = new Date('2021-07-01T00:00:00.000Z');
    authenticatedUserDto.role = UserRole.USER;

    // Act: Validate the object
    const errors = await validate(authenticatedUserDto);

    // Assert: Expect validation errors for missing emailVerified
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('emailVerified');
  });

  it('should fail validation if createdAt is missing', async () => {
    // Arrange: Create an AuthenticatedUserDto object with missing createdAt
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.addresses = ['0.0.4501344'];
    authenticatedUserDto.email = '<EMAIL>';
    authenticatedUserDto.emailVerified = true;
    authenticatedUserDto.role = UserRole.USER;

    // Act: Validate the object
    const errors = await validate(authenticatedUserDto);

    // Assert: Expect validation errors for missing createdAt
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('createdAt');
  });

  it('should fail validation if role is missing', async () => {
    // Arrange: Create an AuthenticatedUserDto object with missing role
    const authenticatedUserDto = new AuthenticatedUserDto();

    authenticatedUserDto.addresses = ['0.0.4501344'];
    authenticatedUserDto.email = '<EMAIL>';
    authenticatedUserDto.emailVerified = true;
    authenticatedUserDto.createdAt = new Date('2021-07-01T00:00:00.000Z');

    // Act: Validate the object
    const errors = await validate(authenticatedUserDto);

    // Assert: Expect validation errors for missing role
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('role');
  });
});

describe('FetchUserResponse', () => {
  it('should validate successfully with valid input', async () => {
    // Arrange: Create a valid FetchUserResponse object
    const fetchUserResponse = new FetchUserResponse();
    fetchUserResponse.success = true;
    fetchUserResponse.user = new AuthenticatedUserDto();
    fetchUserResponse.user.addresses = ['0.0.4501344'];
    fetchUserResponse.user.email = '<EMAIL>';
    fetchUserResponse.user.emailVerified = true;
    fetchUserResponse.user.createdAt = new Date('2021-07-01T00:00:00.000Z');
    fetchUserResponse.user.role = UserRole.USER;

    // Act: Validate the object
    const errors = await validate(fetchUserResponse);

    // Assert: Expect no validation errors
    expect(errors.length).toBe(0);
  });

  it('should fail validation if success is missing', async () => {
    // Arrange: Create a valid FetchUserResponse object with missing success
    const fetchUserResponse = new FetchUserResponse();
    fetchUserResponse.user = new AuthenticatedUserDto();
    fetchUserResponse.user.addresses = ['0.0.4501344'];
    fetchUserResponse.user.email = '<EMAIL>';
    fetchUserResponse.user.emailVerified = true;
    fetchUserResponse.user.createdAt = new Date('2021-07-01T00:00:00.000Z');
    fetchUserResponse.user.role = UserRole.USER;

    // Act: Validate the object
    const errors = await validate(fetchUserResponse);

    // Assert: Expect no validation errors
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('success');
  });

  it('should fail validation if user is missing', async () => {
    /// Arrange: Create a valid FetchUserResponse object
    const fetchUserResponse = new FetchUserResponse();
    fetchUserResponse.success = true;

    // Act: Validate the object
    const errors = await validate(fetchUserResponse);

    // Assert: Expect no validation errors
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('user');
  });
});
