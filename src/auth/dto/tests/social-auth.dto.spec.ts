import { validate } from 'class-validator';
import { SocialAuthUserDto, SocialLoginRequestDto, SocialAuthResponse } from '../social-auth.dto';
import { AuthenticatedUserDto } from '../authenticated-user.dto';
import { UserRole } from '../../entities/user.entity';

describe('SocialAuthUserDto', () => {
  it('should pass validation with valid data', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = '<PERSON>';
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should pass validation if name is optional and missing', async () => {
    const dto = new SocialAuthUserDto();
    // dto.name is missing (optional)
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should pass validation if email is optional and missing', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    // dto.email is missing (optional)
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should pass validation if image is optional and missing', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    // dto.image is missing (optional)
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if address is missing', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    // dto.address is missing (required)
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'address' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if challengeToken is missing', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    // dto.challengeToken is missing (required)
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'challengeToken' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if signature is missing', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    // dto.signature is missing (required)

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'signature' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if name is not a string', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 123 as any; // Invalid type
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((error) => error.property === 'name' && error.constraints && error.constraints.isString)).toBe(
      true,
    );
  });

  it('should fail validation if email is not a string', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = 123 as any; // Invalid type
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((error) => error.property === 'email' && error.constraints && error.constraints.isString)).toBe(
      true,
    );
  });

  it('should fail validation if image is not a string', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    dto.image = 123 as any; // Invalid type
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((error) => error.property === 'image' && error.constraints && error.constraints.isString)).toBe(
      true,
    );
  });

  it('should fail validation if address is not a string', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = 123 as any; // Invalid type
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'address' && error.constraints && error.constraints.isString),
    ).toBe(true);
  });

  it('should fail validation if challengeToken is not a string', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 123 as any; // Invalid type
    dto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'challengeToken' && error.constraints && error.constraints.isString),
    ).toBe(true);
  });

  it('should fail validation if signature is not a string', async () => {
    const dto = new SocialAuthUserDto();
    dto.name = 'John Doe';
    dto.email = '<EMAIL>';
    dto.image = 'https://example.com/profile.jpg';
    dto.address = '0x1234567890abcdef1234567890abcdef12345678';
    dto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    dto.signature = 123 as any; // Invalid type

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'signature' && error.constraints && error.constraints.isString),
    ).toBe(true);
  });
});

describe('SocialAuthSessionDto', () => {
  it('should pass validation with valid data', async () => {
    const userDto = new SocialAuthUserDto();
    userDto.name = 'John Doe';
    userDto.address = '0x1234567890abcdef1234567890abcdef12345678';
    userDto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    userDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const dto = new SocialLoginRequestDto();
    dto.user = userDto;
    dto.expires = '2023-12-31T23:59:59.999Z';
    dto.token = 'eyJhbGciOiHIUzI1...example';
    dto.provider = 'google';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if user is missing', async () => {
    const dto = new SocialLoginRequestDto();
    // dto.user is missing (required)
    dto.expires = '2023-12-31T23:59:59.999Z';
    dto.token = 'eyJhbGciOiHIUzI1...example';
    dto.provider = 'google';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((error) => error.property === 'user' && error.constraints && error.constraints.isNotEmpty)).toBe(
      true,
    );
  });

  it('should fail validation if expires is missing', async () => {
    const userDto = new SocialAuthUserDto();
    userDto.name = 'John Doe';
    userDto.address = '0x1234567890abcdef1234567890abcdef12345678';
    userDto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    userDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const dto = new SocialLoginRequestDto();
    dto.user = userDto;
    // dto.expires is missing (required)
    dto.token = 'eyJhbGciOiHIUzI1...example';
    dto.provider = 'google';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'expires' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if token is missing', async () => {
    const userDto = new SocialAuthUserDto();
    userDto.name = 'John Doe';
    userDto.address = '0x1234567890abcdef1234567890abcdef12345678';
    userDto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    userDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const dto = new SocialLoginRequestDto();
    dto.user = userDto;
    dto.expires = '2023-12-31T23:59:59.999Z';
    // dto.token is missing (required)
    dto.provider = 'google';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'token' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if provider is missing', async () => {
    const userDto = new SocialAuthUserDto();
    userDto.name = 'John Doe';
    userDto.address = '0x1234567890abcdef1234567890abcdef12345678';
    userDto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    userDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const dto = new SocialLoginRequestDto();
    dto.user = userDto;
    dto.expires = '2023-12-31T23:59:59.999Z';
    dto.token = 'eyJhbGciOiHIUzI1...example';
    // dto.provider is missing (required)

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'provider' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if provider is not an enum value', async () => {
    const userDto = new SocialAuthUserDto();
    userDto.name = 'John Doe';
    userDto.address = '0x1234567890abcdef1234567890abcdef12345678';
    userDto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    userDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const dto = new SocialLoginRequestDto();
    dto.user = userDto;
    dto.expires = '2023-12-31T23:59:59.999Z';
    dto.token = 'eyJhbGciOiHIUzI1...example';
    dto.provider = 'invalid-provider' as any; // Invalid enum value

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((error) => error.property === 'provider' && error.constraints && error.constraints.isEnum)).toBe(
      true,
    );
  });

  it('should fail validation if expires is not a string', async () => {
    const userDto = new SocialAuthUserDto();
    userDto.name = 'John Doe';
    userDto.address = '0x1234567890abcdef1234567890abcdef12345678';
    userDto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    userDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const dto = new SocialLoginRequestDto();
    dto.user = userDto;
    dto.expires = 123 as any; // Invalid type
    dto.token = 'eyJhbGciOiHIUzI1...example';
    dto.provider = 'google';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'expires' && error.constraints && error.constraints.isString),
    ).toBe(true);
  });

  it('should fail validation if token is not a string', async () => {
    const userDto = new SocialAuthUserDto();
    userDto.name = 'John Doe';
    userDto.address = '0x1234567890abcdef1234567890abcdef12345678';
    userDto.challengeToken = 'eyJhbGciOiHIUzI1...A_678oMVg';
    userDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const dto = new SocialLoginRequestDto();
    dto.user = userDto;
    dto.expires = '2023-12-31T23:59:59.999Z';
    dto.token = 123 as any; // Invalid type
    dto.provider = 'google';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((error) => error.property === 'token' && error.constraints && error.constraints.isString)).toBe(
      true,
    );
  });
});

describe('SocialAuthResponse', () => {
  it('should pass validation with valid data', async () => {
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.id = 1;
    authenticatedUserDto.role = UserRole.USER;
    authenticatedUserDto.addresses = ['0.0.4501344'];
    authenticatedUserDto.email = '<EMAIL>';
    authenticatedUserDto.phoneNumber = '+15551234567';
    authenticatedUserDto.emailVerified = true;
    authenticatedUserDto.isPhoneVerified = true;
    authenticatedUserDto.createdAt = new Date('2021-07-01T00:00:00.000Z');

    const dto = new SocialAuthResponse();
    dto.success = true;
    dto.isNewUser = false;
    dto.sessionToken = 'eyJhbGciOiHIUzI1...example';
    dto.user = authenticatedUserDto;

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if success is missing', async () => {
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.id = 1;
    authenticatedUserDto.role = UserRole.USER;

    const dto = new SocialAuthResponse();
    // dto.success is missing (required)
    dto.isNewUser = false;
    dto.sessionToken = 'eyJhbGciOiHIUzI1...example';
    dto.user = authenticatedUserDto;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'success' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if isNewUser is missing', async () => {
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.id = 1;
    authenticatedUserDto.role = UserRole.USER;

    const dto = new SocialAuthResponse();
    dto.success = true;
    // dto.isNewUser is missing (required)
    dto.sessionToken = 'eyJhbGciOiHIUzI1...example';
    dto.user = authenticatedUserDto;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'isNewUser' && error.constraints && error.constraints.isNotEmpty),
    ).toBe(true);
  });

  it('should fail validation if user is missing', async () => {
    const dto = new SocialAuthResponse();
    dto.success = true;
    dto.isNewUser = false;
    dto.sessionToken = 'eyJhbGciOiHIUzI1...example';
    // dto.user is missing (required)

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((error) => error.property === 'user' && error.constraints && error.constraints.isNotEmpty)).toBe(
      true,
    );
  });

  it('should fail validation if success is not a boolean', async () => {
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.role = UserRole.USER;

    const dto = new SocialAuthResponse();
    dto.success = 'true' as any; // Invalid type
    dto.isNewUser = false;
    dto.sessionToken = 'eyJhbGciOiHIUzI1Ni...example';
    dto.user = authenticatedUserDto;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'success' && error.constraints && error.constraints.isBoolean),
    ).toBe(true);
  });

  it('should fail validation if isNewUser is not a boolean', async () => {
    const authenticatedUserDto = new AuthenticatedUserDto();
    authenticatedUserDto.id = 1;
    authenticatedUserDto.role = UserRole.USER;

    const dto = new SocialAuthResponse();
    dto.success = true;
    dto.isNewUser = 'false' as any; // Invalid type
    dto.sessionToken = 'eyJhbGciOiHIUzI1Ni...example';
    dto.user = authenticatedUserDto;

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(
      errors.some((error) => error.property === 'isNewUser' && error.constraints && error.constraints.isBoolean),
    ).toBe(true);
  });
});
