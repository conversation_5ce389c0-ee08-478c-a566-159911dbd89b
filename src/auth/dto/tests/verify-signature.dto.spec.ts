import { validate } from 'class-validator';
import { VerifySignature } from '../verify-signature.dto';

describe('VerifySignature DTO', () => {
  it('should validate successfully with valid input', async () => {
    const verifySignatureDto = new VerifySignature();
    verifySignatureDto.address = '0.0.4501344';
    verifySignatureDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    verifySignatureDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(verifySignatureDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if address is missing', async () => {
    const verifySignatureDto = new VerifySignature();
    verifySignatureDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    verifySignatureDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(verifySignatureDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if challengeToken is missing', async () => {
    const verifySignatureDto = new VerifySignature();
    verifySignatureDto.address = '0.0.4501344';
    verifySignatureDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(verifySignatureDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if signature is missing', async () => {
    const verifySignatureDto = new VerifySignature();
    verifySignatureDto.address = '0.0.4501344';
    verifySignatureDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(verifySignatureDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signature');
  });

  it('should fail validation if address is not a string', async () => {
    const verifySignatureDto = new VerifySignature();
    verifySignatureDto.address = 12345 as any;
    verifySignatureDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    verifySignatureDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(verifySignatureDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if challengeToken is not a string', async () => {
    const verifySignatureDto = new VerifySignature();
    verifySignatureDto.address = '0.0.4501344';
    verifySignatureDto.challengeToken = 12345 as any;
    verifySignatureDto.signature =
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c';

    const errors = await validate(verifySignatureDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if signature is not a string', async () => {
    const verifySignatureDto = new VerifySignature();
    verifySignatureDto.address = '0.0.4501344';
    verifySignatureDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    verifySignatureDto.signature = 12345 as any;

    const errors = await validate(verifySignatureDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signature');
  });
});
