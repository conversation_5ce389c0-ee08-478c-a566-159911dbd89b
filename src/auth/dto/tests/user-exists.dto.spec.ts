import { validate } from 'class-validator';
import { UserExists } from '../user-exists.dto';

describe('UserExists DTO', () => {
  it('should validate successfully with valid input', async () => {
    const userExistsDto = new UserExists();
    userExistsDto.email = '****************************';

    const errors = await validate(userExistsDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if email is missing', async () => {
    const userExistsDto = new UserExists();

    const errors = await validate(userExistsDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });

  it('should fail validation if email is not a string', async () => {
    const userExistsDto = new UserExists();
    userExistsDto.email = 12345 as any;

    const errors = await validate(userExistsDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });

  it('should fail validation if email is an empty string', async () => {
    const userExistsDto = new UserExists();
    userExistsDto.email = '';

    const errors = await validate(userExistsDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });
});
