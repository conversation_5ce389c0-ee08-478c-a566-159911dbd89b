import { validate } from 'class-validator';
import { RecoverAccount } from '../recover-wallet.dto';

describe('RecoverAccount', () => {
  it('should validate successfully with valid input', async () => {
    const recoverWalletDto = new RecoverAccount();
    recoverWalletDto.email = '<EMAIL>';

    const errors = await validate(recoverWalletDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if email is missing', async () => {
    const recoverWalletDto = new RecoverAccount();

    const errors = await validate(recoverWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });

  it('should fail validation if email format is invalid', async () => {
    const recoverWalletDto = new RecoverAccount();
    recoverWalletDto.email = 'invalid-email';

    const errors = await validate(recoverWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });
});
