import { validate } from 'class-validator';
import { VerifyOtp, VerifyOtpResponse } from '../verify-otp.dto';

describe('VerifyOtp DTO', () => {
  it('should validate successfully with valid input', async () => {
    const verifyOtpDto = new VerifyOtp();
    verifyOtpDto.otp = '123456';

    const errors = await validate(verifyOtpDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if otp is missing', async () => {
    const verifyOtpDto = new VerifyOtp();

    const errors = await validate(verifyOtpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('otp');
  });

  it('should fail validation if otp is not a string', async () => {
    const verifyOtpDto = new VerifyOtp();
    verifyOtpDto.otp = 123456 as any;

    const errors = await validate(verifyOtpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('otp');
  });
});

describe('VerifyOtpResponse DTO', () => {
  it('should validate successfully with valid input', async () => {
    const verifyOtpResponse = new VerifyOtpResponse();
    verifyOtpResponse.success = true;
    verifyOtpResponse.sessionToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(verifyOtpResponse);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if success is missing', async () => {
    const verifyOtpResponse = new VerifyOtpResponse();
    verifyOtpResponse.sessionToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(verifyOtpResponse);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('success');
  });

  it('should fail validation if sessionToken is missing', async () => {
    const verifyOtpResponse = new VerifyOtpResponse();
    verifyOtpResponse.success = true;

    const errors = await validate(verifyOtpResponse);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('sessionToken');
  });

  it('should fail validation if sessionToken is not a string', async () => {
    const verifyOtpResponse = new VerifyOtpResponse();
    verifyOtpResponse.success = true;
    verifyOtpResponse.sessionToken = 12345 as any;

    const errors = await validate(verifyOtpResponse);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('sessionToken');
  });
});
