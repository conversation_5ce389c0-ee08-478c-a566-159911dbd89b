import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { UpdateDisplayNameDto } from '../update-display-name.dto';

describe('UpdateDisplayNameDto', () => {
  it('should validate successfully with valid input', async () => {
    const input = {
      displayName: 'John Doe',
    };

    const dto = plainToClass(UpdateDisplayNameDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBe(0);
  });

  it('should fail validation if displayName is missing', async () => {
    const input = {};

    const dto = plainToClass(UpdateDisplayNameDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('displayName');
  });

  it('should fail validation if displayName is empty', async () => {
    const input = {
      displayName: '',
    };

    const dto = plainToClass(UpdateDisplayNameDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('displayName');
  });

  it('should fail validation if displayName exceeds maximum length', async () => {
    const input = {
      displayName: 'a'.repeat(51),
    };

    const dto = plainToClass(UpdateDisplayNameDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('displayName');
  });

  it('should fail validation if displayName is not a string', async () => {
    const input = {
      displayName: 12345,
    };

    const dto = plainToClass(UpdateDisplayNameDto, input);
    const errors = await validate(dto);

    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('displayName');
  });
});
