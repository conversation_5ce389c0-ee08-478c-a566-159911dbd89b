import { validate } from 'class-validator';
import { SignUp } from '../signup.dto';

describe('SignUp DTO', () => {
  it('should validate successfully with valid input', async () => {
    const signUpDto = new SignUp();
    signUpDto.address = '0.0.4501344';
    signUpDto.email = '<EMAIL>';
    signUpDto.displayName = 'John <PERSON>';
    signUpDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    signUpDto.signature = 'valid-signature';

    const errors = await validate(signUpDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if address is missing', async () => {
    const signUpDto = new SignUp();
    signUpDto.email = '<EMAIL>';
    signUpDto.displayName = 'John <PERSON>e';
    signUpDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    signUpDto.signature = 'valid-signature';

    const errors = await validate(signUpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if email is missing', async () => {
    const signUpDto = new SignUp();
    signUpDto.address = '0.0.4501344';
    signUpDto.displayName = 'John Doe';
    signUpDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    signUpDto.signature = 'valid-signature';

    const errors = await validate(signUpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });

  it('should fail validation if email format is invalid', async () => {
    const signUpDto = new SignUp();
    signUpDto.address = '0.0.4501344';
    signUpDto.email = 'invalid-email';
    signUpDto.displayName = 'John Doe';
    signUpDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    signUpDto.signature = 'valid-signature';

    const errors = await validate(signUpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('email');
  });

  it('should fail validation if displayName is missing', async () => {
    const signUpDto = new SignUp();
    signUpDto.address = '0.0.4501344';
    signUpDto.email = '<EMAIL>';
    signUpDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    signUpDto.signature = 'valid-signature';

    const errors = await validate(signUpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('displayName');
  });

  it('should fail validation if displayName exceeds maximum length', async () => {
    const signUpDto = new SignUp();
    signUpDto.address = '0.0.4501344';
    signUpDto.email = '<EMAIL>';
    signUpDto.displayName = 'a'.repeat(73); // Exceeds 72 characters
    signUpDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';
    signUpDto.signature = 'valid-signature';

    const errors = await validate(signUpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('displayName');
  });

  it('should fail validation if challengeToken is missing', async () => {
    const signUpDto = new SignUp();
    signUpDto.address = '0.0.4501344';
    signUpDto.email = '<EMAIL>';
    signUpDto.displayName = 'John Doe';
    signUpDto.signature = 'valid-signature';

    const errors = await validate(signUpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if signature is missing', async () => {
    const signUpDto = new SignUp();
    signUpDto.address = '0.0.4501344';
    signUpDto.email = '<EMAIL>';
    signUpDto.displayName = 'John Doe';
    signUpDto.challengeToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(signUpDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signature');
  });
});
