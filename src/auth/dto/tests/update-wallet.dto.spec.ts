import { validate } from 'class-validator';
import { UpdateWallet } from '../update-wallet.dto';

describe('UpdateWallet DTO', () => {
  it('should validate successfully with valid input', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = '0.0.4501344';
    updateWalletDto.challengeToken = 'valid-token';
    updateWalletDto.signature = 'valid-signature';
    updateWalletDto.recoveryToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if address is missing', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.challengeToken = 'valid-token';
    updateWalletDto.signature = 'valid-signature';
    updateWalletDto.recoveryToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if challengeToken is missing', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = '0.0.4501344';
    updateWalletDto.signature = 'valid-signature';
    updateWalletDto.recoveryToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if signature is missing', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = '0.0.4501344';
    updateWalletDto.challengeToken = 'valid-token';
    updateWalletDto.recoveryToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signature');
  });

  it('should fail validation if recoveryToken is missing', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = '0.0.4501344';
    updateWalletDto.challengeToken = 'valid-token';
    updateWalletDto.signature = 'valid-signature';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('recoveryToken');
  });

  it('should fail validation if address is not a string', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = 12345 as any;
    updateWalletDto.challengeToken = 'valid-token';
    updateWalletDto.signature = 'valid-signature';
    updateWalletDto.recoveryToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
  });

  it('should fail validation if challengeToken is not a string', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = '0.0.4501344';
    updateWalletDto.challengeToken = 12345 as any;
    updateWalletDto.signature = 'valid-signature';
    updateWalletDto.recoveryToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('challengeToken');
  });

  it('should fail validation if signature is not a string', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = '0.0.4501344';
    updateWalletDto.challengeToken = 'valid-token';
    updateWalletDto.signature = 12345 as any;
    updateWalletDto.recoveryToken = 'eyJhbGciOiJIUzI1...A_678oMVg';

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signature');
  });

  it('should fail validation if recoveryToken is not a string', async () => {
    const updateWalletDto = new UpdateWallet();
    updateWalletDto.address = '0.0.4501344';
    updateWalletDto.challengeToken = 'valid-token';
    updateWalletDto.signature = 'valid-signature';
    updateWalletDto.recoveryToken = 12345 as any;

    const errors = await validate(updateWalletDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('recoveryToken');
  });
});
