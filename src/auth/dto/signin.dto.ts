import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { AuthenticatedUserDto } from './authenticated-user.dto';

export class SignIn {
  @ApiProperty({
    example: '0.0.4501344',
    description: "The user's wallet address with which they want to sign up.",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description: 'The JWT token that was sent from the backend after requesting the challenge.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  challengeToken: string;

  @ApiProperty({
    example:
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c',
    description: "The signature that was generated by the user's wallet during the request-challenge process.",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  signature: string;
}

export class SignInResponse {
  @ApiProperty({
    example: true,
    description: 'Whether the user was successfully signed in.',
  })
  success: boolean;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description: "User session token containing a user ID and the user's role.",
  })
  sessionToken: string;

  @ApiProperty({
    type: AuthenticatedUserDto,
    description: 'Information about the signed in user',
  })
  user: AuthenticatedUserDto;
}
