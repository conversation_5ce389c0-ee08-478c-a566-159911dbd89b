import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class VerifyOtp {
  @ApiProperty({
    example: '123456',
    description: 'The OTP sent to the user.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  otp: string;
}

export class VerifyOtpResponse {
  @ApiProperty({
    example: true,
    description: 'Whether the user was successfully signed in.',
  })
  @IsNotEmpty()
  success: boolean;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description: "User session token containing a user ID and the user's role.",
  })
  @IsNotEmpty()
  @IsString()
  sessionToken: string;
}
