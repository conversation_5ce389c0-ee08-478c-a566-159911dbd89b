import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class RequestChallenge {
  @ApiProperty({
    example: '0.0.4501344',
    description: "The user's wallet address with which they want to request the challenge message.",
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  address: string;
}

export class RequestChallengeResponse {
  @ApiProperty({
    example: true,
    description: 'Whether the request was successful.',
  })
  @IsNotEmpty()
  success: boolean;

  @ApiProperty({
    example: 'example.com wants you to sign in with your Hedera account: 0.0.4422816...',
    description:
      'The generated challenge. This is the message that the user needs to sign to prove ownership of the ' +
      'wallet address. It contains a human readable message similar to ERC-4361.',
  })
  @IsNotEmpty()
  challenge: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description:
      'JWT proving that the challenge was generated by the server. The token contains the challenge ' +
      'and the wallet address for which the challenge was generated.',
  })
  @IsNotEmpty()
  challengeToken: string;

  @ApiProperty({
    example: true,
    description: 'Whether a user account exists for the wallet address for which the challenge was requested.',
  })
  @IsNotEmpty()
  accountExists: boolean;

  @ApiProperty({
    example: true,
    description: 'Whether a user account exists for the wallet address for which the challenge was requested.',
  })
  @IsNotEmpty()
  accountDeleted: boolean;
}
