import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class VerifySignature {
  @ApiProperty({
    example: '0.0.4501344',
    description: "The user's wallet address with which they want to sign up.",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description: 'The JWT containing a challenge (SIWE message) generated by the backend.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  challengeToken: string;

  @ApiProperty({
    example:
      '0x2b4c2fdce01d9530caa7ff3beeb024dc4974669bd6d3362bc4aec9d68fca4f3544fc21eb161a822b70840bc9c36365e100d8e80bf1cb736022b7582392ce81061c',
    description: "The signature that was generated by the user's wallet during the request-challenge process.",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  signature: string;
}
