import { IsString, Matches, Length, IsEnum, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CountryCode, getCountries } from 'libphonenumber-js';

export class PhoneNumberDto {
  @ApiProperty({ description: 'Phone number (digits only)', example: '2512630796' })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{1,15}$/, { message: 'phoneNumber must contain only digits (1 to 15 digits)' })
  phoneNumber: string;

  @ApiProperty({
    description: 'Country code (ISO 3166-1 alpha-2)',
    example: 'US',
  })
  @IsString()
  @IsEnum(Object.values(getCountries()).filter((v) => typeof v === 'string'), {
    message: `countryCode must be one of the following values: ${getCountries().join(', ')}`,
  })
  countryCode: CountryCode;
}

export class VerifyPhoneNumberDto {
  @ApiProperty({ description: 'OTP code received via SMS', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6)
  @Matches(/^\d{6}$/, { message: 'OTP must be a 6-digit number' })
  otp: string;
}
