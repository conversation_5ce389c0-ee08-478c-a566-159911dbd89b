import { ApiProperty } from '@nestjs/swagger';
import { VerifySignature } from './verify-signature.dto';
import { IsNotEmpty, IsString } from 'class-validator';

export class UpdateWallet extends VerifySignature {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1...A_678oMVg',
    description: 'The JWT containing the email address of the user that requested account recovery.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  recoveryToken: string;
}
