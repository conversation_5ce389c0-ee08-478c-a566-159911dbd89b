import { ApiProperty } from '@nestjs/swagger';

/**
 * The DTO used for returning public user information.
 */
export class UserDto {
  @ApiProperty({
    example: '42',
    description: 'ID of the user.',
  })
  id: number;

  @ApiProperty({
    example: '<PERSON>',
    description: 'The display name of the user.',
  })
  displayName: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the user.',
  })
  email?: string;
}
