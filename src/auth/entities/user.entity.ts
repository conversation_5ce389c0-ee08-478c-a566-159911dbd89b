import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { BadRequestException } from '@nestjs/common';
import { UserNotificationPreferences } from '../../notifications/entities/user-notification-preferences.entity';

export enum UserRole {
  GRANT_PROGRAM_COORDINATOR = 'GRANT_PROGRAM_COORDINATOR',
  USER = 'USER',
}

@Entity()
export class User {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true })
  email: string;

  @Column({
    array: true,
    nullable: false,
    type: 'text',
  })
  addresses: string[];

  @Column()
  displayName: string;

  @Column({ default: false })
  emailVerified: boolean;

  @Column({ unique: true, nullable: true })
  phoneNumber: string;

  @Column({ default: false })
  isPhoneVerified: boolean;

  @Column({ type: 'bigint', nullable: true })
  phoneOtp: string;

  @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  phoneOtpExpiresAt: Date;

  @Column({ type: 'bigint', nullable: true })
  otp: string;

  @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  otpExpiresAt: Date;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @Column({ type: 'enum', enum: UserRole, default: UserRole.USER })
  role: UserRole;

  // soft deletes
  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  reactivatedAt?: Date;

  @Column({ type: 'jsonb', nullable: true, default: {} })
  linkedAccounts?: Record<string, string>;

  @OneToMany(() => UserNotificationPreferences, (preference) => preference.user, { cascade: ['insert', 'update'] })
  notificationPreferences: UserNotificationPreferences[];

  @BeforeUpdate()
  @BeforeInsert()
  ensureUniqueAddresses() {
    const uniqueAddresses = Array.from(new Set(this.addresses));
    if (uniqueAddresses.length !== this.addresses.length) {
      throw new BadRequestException('Duplicate addresses are not allowed');
    }
    this.addresses = uniqueAddresses;
  }
}
