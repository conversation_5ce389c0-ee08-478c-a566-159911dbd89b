import { Injectable, PipeTransform } from '@nestjs/common';

import { AuthService } from './auth.service';

@Injectable()
export class ParseTokenPipe implements PipeTransform {
  constructor(private authService: AuthService) {}

  transform(value: string | object | undefined) {
    if (!value) {
      return null;
    }

    if (typeof value === 'string') {
      const jwt = value.split(' ')[1];
      return this.authService.verifyAndExtractSignedInUser(jwt);
    }

    return value;
  }
}
