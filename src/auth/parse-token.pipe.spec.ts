import { Test, TestingModule } from '@nestjs/testing';

import { AuthService } from './auth.service';
import { ParseTokenPipe } from './parse-token.pipe';

describe('ParseTokenPipe', () => {
  let pipe: ParseTokenPipe;
  let authService: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ParseTokenPipe,
        {
          provide: AuthService,
          useValue: {
            verifyAndExtractSignedInUser: jest.fn(),
          },
        },
      ],
    }).compile();

    pipe = module.get<ParseTokenPipe>(ParseTokenPipe);
    authService = module.get<AuthService>(AuthService);
  });

  it('should return null when value is undefined', async () => {
    const result = await pipe.transform(undefined);
    expect(result).toBeNull();
  });

  it('should return the value when it is an object', async () => {
    const obj = { key: 'value' };
    const result = await pipe.transform(obj);
    expect(result).toBe(obj);
  });

  it('should extract and verify JWT when value is a string', async () => {
    const mockUser = { id: 1, username: 'testuser' };
    const token = 'Bearer mytoken123';

    jest.spyOn(authService, 'verifyAndExtractSignedInUser').mockResolvedValue(mockUser);

    const result = await pipe.transform(token);

    expect(authService.verifyAndExtractSignedInUser).toHaveBeenCalledWith('mytoken123');
    expect(result).toBe(mockUser);
  });
});
