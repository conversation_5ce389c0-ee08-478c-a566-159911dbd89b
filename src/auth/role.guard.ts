import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';

import { Reflector } from '@nestjs/core';
import { Roles } from './role.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    try {
      const roles = this.reflector.get(Roles, context.getHandler());
      if (!roles) {
        return true;
      }

      const request = context.switchToHttp().getRequest();
      const user = request['user'];

      return roles.includes(user.role);
    } catch {
      return false;
    }
  }
}
