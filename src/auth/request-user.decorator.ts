import { ExecutionContext, createParamDecorator } from '@nestjs/common';

import { JwtScope } from './auth.service';
import { ParseTokenPipe } from './parse-token.pipe';
import { UserRole } from './entities/user.entity';

export type RequestUserPayload = {
  id: number;
  role: UserRole;
  scope: JwtScope;
  emailVerified: boolean;
};

export const GetUserOrBearerToken = createParamDecorator((_: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return request.user ?? request.headers.authorization;
});

export const RequestUser = () => GetUserOrBearerToken(ParseTokenPipe);
