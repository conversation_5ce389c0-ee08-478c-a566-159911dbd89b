import { AuthService } from '../auth.service';
import { ConfigService } from '@nestjs/config';
import { GithubService } from './github/github.service';
import { GithubStrategy } from './github/github.strategy';
import { GoogleService } from './google/google.service';
import { GoogleStrategy } from './google/google.strategy';
import { JwtModule } from '@nestjs/jwt';
import { LinkedinService } from './linkedin/linkedin.service';
import { LinkedinStrategy } from './linkedin/linkedin.strategy';
import { MailModule } from '../../notifications/mail/mail.module';
import { Module } from '@nestjs/common';
import { SocialLoginService } from './social-login.service';
import { TwitterService } from './twitter/twitter.service';
import { TwitterStrategy } from './twitter/twitter.strategy';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRATION_TIME') },
      }),
    }),
    MailModule,
  ],
  providers: [
    GoogleService,
    GoogleStrategy,
    TwitterService,
    TwitterStrategy,
    LinkedinStrategy,
    LinkedinService,
    GithubService,
    GithubStrategy,
    SocialLoginService,
    AuthService,
  ],
  exports: [SocialLoginService],
})
export class SocialLoginModule {}
