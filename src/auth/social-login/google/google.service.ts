import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { GoogleStrategy } from './google.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';
import { SocialUserPayload } from '../social-login.service';

@Injectable()
export class GoogleService {
  constructor(private readonly googleStrategy: GoogleStrategy) {}

  async googleLogin(socialLoginDto: SocialLoginRequestDto): Promise<SocialUserPayload> {
    const { token } = socialLoginDto;
    const googleUser = await this.googleStrategy.verifyIdToken(token);

    if (!googleUser.email) {
      // Throw an error if email from Google is null or empty
      throw new HttpException(
        'Email address from Google is not available. Please ensure your email is public on your Google profile or use a different signup method.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      sub: googleUser.sub,
      email: googleUser.email,
      username: googleUser.name,
      linkedAccount: { googleId: googleUser.sub },
    };
  }
}
