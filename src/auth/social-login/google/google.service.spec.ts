import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus, UnauthorizedException } from '@nestjs/common';
import { GoogleService } from './google.service';
import { GoogleStrategy } from './google.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';
import { SocialUserPayload } from '../social-login.service';

const mockGoogleStrategy = () => ({
  verifyIdToken: jest.fn(),
});

describe('GoogleService', () => {
  let service: GoogleService;
  let googleStrategy: ReturnType<typeof mockGoogleStrategy>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GoogleService, { provide: GoogleStrategy, useFactory: mockGoogleStrategy }],
    }).compile();

    service = module.get<GoogleService>(GoogleService);
    googleStrategy = module.get(GoogleStrategy);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('googleLogin', () => {
    const socialLoginDto: SocialLoginRequestDto = {
      provider: 'google',
      token: 'test-google-token',
      expires: '2023-12-31T23:59:59.999Z',
      user: {
        name: 'Test User',
        address: 'test-address',
        signature: 'test-signature',
        challengeToken: 'test-challenge',
      } as any, // Using 'any' as user details are not directly validated in GoogleService
    };

    const mockSocialUserPayload = {
      sub: 'google-user-id',
      email: '<EMAIL>',
      username: 'TestUser',
      linkedAccount: { googleId: 'google-user-id' },
    };

    it('should successfully login and return SocialUserPayload with valid email', async () => {
      googleStrategy.verifyIdToken.mockResolvedValue({
        sub: 'google-user-id',
        email: '<EMAIL>',
        name: 'TestUser',
      });

      const result = await service.googleLogin(socialLoginDto);

      expect(googleStrategy.verifyIdToken).toHaveBeenCalledWith(socialLoginDto.token);
      expect(result).toEqual(mockSocialUserPayload);
    });

    it('should throw BadRequestException if email is missing from googleUser', async () => {
      const mockGoogleUserNoEmail = {
        sub: 'google-user-id',
        email: null, // Email is null, simulating missing email
        username: 'Test User',
        linkedAccount: { googleId: 'google-user-id' },
      } as SocialUserPayload;
      googleStrategy.verifyIdToken.mockResolvedValue(mockGoogleUserNoEmail);

      await expect(service.googleLogin(socialLoginDto)).rejects.toThrowError(HttpException);
      await expect(service.googleLogin(socialLoginDto)).rejects.toThrowError(
        'Email address from Google is not available. Please ensure your email is public on your Google profile or use a different signup method.',
      );
      await expect(service.googleLogin(socialLoginDto)).rejects.toHaveProperty('status', HttpStatus.BAD_REQUEST);
    });

    it('should throw error if verifyIdToken fails', async () => {
      const errorMessage = 'Google ID token verification failed';
      googleStrategy.verifyIdToken.mockRejectedValue(new Error(errorMessage)); // Mock verifyIdToken to reject

      await expect(service.googleLogin(socialLoginDto)).rejects.toThrowError(Error);
      await expect(service.googleLogin(socialLoginDto)).rejects.toThrowError(errorMessage);
      expect(googleStrategy.verifyIdToken).toHaveBeenCalledWith(socialLoginDto.token);
    });

    it('should throw UnauthorizedException if user data is missing', async () => {
      googleStrategy.verifyIdToken.mockRejectedValue(new UnauthorizedException('Invalid Google Id token'));

      await expect(service.googleLogin(socialLoginDto)).rejects.toThrowError(HttpException);
      await expect(service.googleLogin(socialLoginDto)).rejects.toThrowError('Invalid Google Id token');
      await expect(service.googleLogin(socialLoginDto)).rejects.toHaveProperty('status', HttpStatus.UNAUTHORIZED);
    });
  });
});
