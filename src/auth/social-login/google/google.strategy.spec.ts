import { ConfigService } from '@nestjs/config';
import { GoogleStrategy } from './google.strategy';
import { OAuth2Client } from 'google-auth-library';
import { UnauthorizedException } from '@nestjs/common';

jest.mock('google-auth-library', () => {
  const mockVerifyIdToken = jest.fn();
  const mockGetPayload = jest.fn();
  return {
    OAuth2Client: jest.fn(() => ({
      verifyIdToken: mockVerifyIdToken.mockImplementation(async () => ({
        getPayload: mockGetPayload,
      })),
    })),
    mockVerifyIdToken,
    mockGetPayload,
  };
});

describe('GoogleStrategy', () => {
  let googleStrategy: GoogleStrategy;
  let configService: ConfigService;
  const mockedOAuth2Client = OAuth2Client as unknown as jest.Mock;
  const { mockVerifyIdToken: mockedVerifyIdToken, mockGetPayload: mockedGetPayload } =
    jest.requireMock('google-auth-library');

  beforeEach(() => {
    configService = new ConfigService();
    googleStrategy = new GoogleStrategy(configService);
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should be defined', () => {
    expect(googleStrategy).toBeDefined();
  });

  describe('verifyIdToken', () => {
    it('should successfully verify ID token and return payload', async () => {
      const mockPayload = { sub: 'test-google-user-id', email: '<EMAIL>', name: 'Test User' };
      mockedGetPayload.mockReturnValue(mockPayload);

      const idToken = 'test-id-token';
      const result = await googleStrategy.verifyIdToken(idToken);

      expect(mockedVerifyIdToken).toHaveBeenCalledWith({
        idToken: expect.any(String),
        audience: undefined,
      });
      expect(mockedGetPayload).toHaveBeenCalled();
      expect(result).toEqual(mockPayload);
    });
    it('should throw UnauthorizedException if OAuth2Client.verifyIdToken throws an error', async () => {
      mockedVerifyIdToken.mockRejectedValue(new UnauthorizedException('Invalid Google Id token'));

      const idToken = 'invalid-id-token';
      await expect(googleStrategy.verifyIdToken(idToken)).rejects.toThrow(UnauthorizedException);
      await expect(googleStrategy.verifyIdToken(idToken)).rejects.toHaveProperty('message', 'Invalid Google Id token');
    });

    it('should throw UnauthorizedException if payload is null', async () => {
      mockedGetPayload.mockReturnValue(null); // Simulate no payload

      const idToken = 'test-id-token';
      await expect(googleStrategy.verifyIdToken(idToken)).rejects.toThrowError(UnauthorizedException);
      await expect(googleStrategy.verifyIdToken(idToken)).rejects.toHaveProperty('message', 'Invalid Google Id token');
      expect(mockedGetPayload).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException if payload is undefined', async () => {
      mockedGetPayload.mockReturnValue(undefined); // Simulate undefined payload

      const idToken = 'test-id-token';
      await expect(googleStrategy.verifyIdToken(idToken)).rejects.toThrowError(UnauthorizedException);
      await expect(googleStrategy.verifyIdToken(idToken)).rejects.toHaveProperty('message', 'Invalid Google Id token');
      expect(mockedGetPayload).toHaveBeenCalled();
    });

    it('should handle empty string as GOOGLE_CLIENT_ID from config', async () => {
      configService.get = jest.fn().mockReturnValue(''); // Mock ConfigService to return empty string for GOOGLE_CLIENT_ID
      const strategyWithEmptyConfig = new GoogleStrategy(configService);
      const mockPayload = { sub: 'test-google-user-id', email: '<EMAIL>', name: 'Test User' };
      mockedGetPayload.mockReturnValue(mockPayload);

      const idToken = 'test-id-token';
      await strategyWithEmptyConfig.verifyIdToken(idToken);

      expect(mockedOAuth2Client).toHaveBeenCalled();
      expect(mockedVerifyIdToken).toHaveBeenCalledWith({
        idToken: expect.any(String),
        audience: '', // Should be empty string now
      });
    });

    it('should handle undefined GOOGLE_CLIENT_ID from config', async () => {
      configService.get = jest.fn().mockReturnValue(undefined); // Mock ConfigService to return undefined for GOOGLE_CLIENT_ID
      const strategyWithUndefinedConfig = new GoogleStrategy(configService);
      const mockPayload = { sub: 'test-google-user-id', email: '<EMAIL>', name: 'Test User' };
      mockedGetPayload.mockReturnValue(mockPayload);

      const idToken = 'test-id-token';
      await strategyWithUndefinedConfig.verifyIdToken(idToken);

      expect(mockedOAuth2Client).toHaveBeenCalled();
      expect(mockedVerifyIdToken).toHaveBeenCalledWith({
        idToken: expect.any(String),
        audience: undefined, // Should be undefined
      });
    });
  });
});
