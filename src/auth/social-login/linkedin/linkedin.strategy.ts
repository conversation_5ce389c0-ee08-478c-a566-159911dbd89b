import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';

@Injectable()
export class LinkedinStrategy {
  private readonly logger = new Logger(LinkedinStrategy.name);

  constructor(private readonly configService: ConfigService) {}

  async verifyAccessToken(accessToken: string) {
    try {
      const response = await fetch(this.configService.get('LINKEDIN_API_URL'), {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        // Handle errors based on the HTTP status code
        if (response.status === 401) {
          throw new Error('Invalid or expired access token.');
        } else if (response.status === 403) {
          throw new Error('Forbidden - Token might not have the required scopes.');
        }

        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const userData = await response.json();
      return userData;
    } catch (error) {
      this.logger.error(error.message);
      throw new UnauthorizedException('Invalid Linkedin access token');
    }
  }
}
