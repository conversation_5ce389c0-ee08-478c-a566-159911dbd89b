import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus, UnauthorizedException } from '@nestjs/common';

import { LinkedinService } from './linkedin.service';
import { LinkedinStrategy } from './linkedin.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';
import { SocialUserPayload } from '../social-login.service';

const mockLinkedinStrategy = () => ({
  verifyAccessToken: jest.fn(),
});

describe('LinkedinService', () => {
  let service: LinkedinService;
  let linkedinStrategy: ReturnType<typeof mockLinkedinStrategy>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LinkedinService, { provide: LinkedinStrategy, useFactory: mockLinkedinStrategy }],
    }).compile();

    service = module.get<LinkedinService>(LinkedinService);
    linkedinStrategy = module.get(LinkedinStrategy);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('linkedinLogin', () => {
    const socialLoginDto: SocialLoginRequestDto = {
      provider: 'linkedin',
      token: 'test-linkedin-token',
      expires: '2023-12-31T23:59:59.999Z',
      user: {
        name: 'Test User',
        address: 'test-address',
        signature: 'test-signature',
        challengeToken: 'test-challenge',
      } as any,
    };
    const mockSocialUserPayload: SocialUserPayload = {
      sub: 'linkedin-user-id',
      email: '<EMAIL>',
      username: 'Test User',
      linkedAccount: { linkedinId: 'linkedin-user-id' },
    };

    it('should successfully login and return SocialUserPayload with valid email', async () => {
      linkedinStrategy.verifyAccessToken.mockResolvedValue({
        sub: 'linkedin-user-id',
        email: '<EMAIL>',
        name: 'Test User',
      });

      const result = await service.linkedinLogin(socialLoginDto);

      expect(linkedinStrategy.verifyAccessToken).toHaveBeenCalledWith(socialLoginDto.token);
      expect(result).toEqual(mockSocialUserPayload);
    });

    it('should throw BadRequestException if email is missing from linkedinUser', async () => {
      const mockLinkedinUserNoEmail = {
        sub: 'linkedin-user-id',
        email: null, // Email is null, simulating missing email
        username: 'Test User',
        linkedAccount: { linkedinId: 'linkedin-user-id' },
      } as SocialUserPayload;
      linkedinStrategy.verifyAccessToken.mockResolvedValue(mockLinkedinUserNoEmail);

      await expect(service.linkedinLogin(socialLoginDto)).rejects.toThrowError(HttpException);
      await expect(service.linkedinLogin(socialLoginDto)).rejects.toThrowError(
        'Email address from Linkedin is not available. Please ensure your email is public on your Linkedin profile or use a different signup method.',
      );
      await expect(service.linkedinLogin(socialLoginDto)).rejects.toHaveProperty('status', HttpStatus.BAD_REQUEST);
    });

    it('should throw error if verifyAccessToken fails', async () => {
      const errorMessage = 'Linkedin access token verification failed';
      linkedinStrategy.verifyAccessToken.mockRejectedValue(new Error(errorMessage)); // Mock verifyAccessToken to reject

      await expect(service.linkedinLogin(socialLoginDto)).rejects.toThrowError(Error);
      await expect(service.linkedinLogin(socialLoginDto)).rejects.toThrowError(errorMessage);
      expect(linkedinStrategy.verifyAccessToken).toHaveBeenCalledWith(socialLoginDto.token);
    });

    it('should throw UnauthorizedException if user data is missing', async () => {
      linkedinStrategy.verifyAccessToken.mockRejectedValue(new UnauthorizedException('Invalid Linkedin access token'));

      await expect(service.linkedinLogin(socialLoginDto)).rejects.toThrowError(HttpException);
      await expect(service.linkedinLogin(socialLoginDto)).rejects.toThrowError('Invalid Linkedin access token');
      await expect(service.linkedinLogin(socialLoginDto)).rejects.toHaveProperty('status', HttpStatus.UNAUTHORIZED);
    });
  });
});
