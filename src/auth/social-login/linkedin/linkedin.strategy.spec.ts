import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@nestjs/config';
import { LinkedinStrategy } from './linkedin.strategy';
import { UnauthorizedException } from '@nestjs/common';

describe('LinkedinStrategy', () => {
  let linkedinStrategy: LinkedinStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LinkedinStrategy, ConfigService],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn().mockReturnValue('https://api.linkedin.com/v2/userinfo'),
      })
      .compile();

    linkedinStrategy = module.get<LinkedinStrategy>(LinkedinStrategy);
    jest.spyOn(global, 'fetch').mockReset();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(linkedinStrategy).toBeDefined();
  });

  describe('verifyAccessToken', () => {
    const accessToken = 'test-access-token';

    it('should successfully verify access token and return user data', async () => {
      const mockUserData = {
        id: 'linkedin-user-id',
        localizedFirstName: 'Test',
        localizedLastName: 'User',
        emailAddress: '<EMAIL>',
      };
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockUserData,
      } as any);

      const userData = await linkedinStrategy.verifyAccessToken(accessToken);

      expect(global.fetch).toHaveBeenCalledWith('https://api.linkedin.com/v2/userinfo', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      expect(userData).toEqual(mockUserData);
    });

    it('should throw UnauthorizedException for 401 Unauthorized response', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 401,
      } as any);

      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(
        'Invalid Linkedin access token',
      );
    });

    it('should throw UnauthorizedException for 403 Forbidden response', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 403,
      } as any);

      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(
        'Invalid Linkedin access token',
      );
    });

    it('should throw UnauthorizedException for other non-OK HTTP responses (e.g., 500)', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 500,
      } as any);

      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(
        'Invalid Linkedin access token',
      );
    });

    it('should throw UnauthorizedException if fetch API call fails (network error)', async () => {
      jest.spyOn(global, 'fetch').mockRejectedValueOnce(new Error('Network error'));

      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(
        'Invalid Linkedin access token',
      );
    });

    it('should throw UnauthorizedException if response is ok but json parsing fails', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => {
          throw new Error('JSON parsing error');
        },
      } as any);

      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(linkedinStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(
        'Invalid Linkedin access token',
      );
    });
  });
});
