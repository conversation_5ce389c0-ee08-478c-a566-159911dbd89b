import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LinkedinStrategy } from './linkedin.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';

import { SocialUserPayload } from '../social-login.service';

@Injectable()
export class LinkedinService {
  constructor(private readonly linkedinStrategy: LinkedinStrategy) {}

  async linkedinLogin(socialLoginDto: SocialLoginRequestDto): Promise<SocialUserPayload> {
    const { token } = socialLoginDto;
    const linkedinUser = await this.linkedinStrategy.verifyAccessToken(token);

    if (!linkedinUser.email) {
      // Throw an error if email from Linkedin is null or empty
      throw new HttpException(
        'Email address from Linkedin is not available. Please ensure your email is public on your Linkedin profile or use a different signup method.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      sub: linkedinUser.sub,
      email: linkedinUser.email,
      username: linkedinUser.name,
      linkedAccount: { linkedinId: linkedinUser.sub },
    };
  }
}
