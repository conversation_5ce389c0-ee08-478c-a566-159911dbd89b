import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';

import { Octokit } from 'octokit';

@Injectable()
export class GithubStrategy {
  private readonly logger = new Logger(GithubStrategy.name);

  async verifyAccessToken(accessToken: string) {
    try {
      const octokit = new Octokit({ auth: accessToken, scopes: 'User' });
      const { data: githubUser } = await octokit.rest.users.getAuthenticated(); // Get authenticated user

      if (!githubUser) {
        throw new UnauthorizedException('Invalid Github access token: User data not found');
      }

      const response = await octokit.rest.users.listEmailsForAuthenticatedUser();
      const primaryEmail = response.data.find((email) => email.primary && email.verified)?.email;

      return {
        sub: githubUser.id.toString(),
        email: primaryEmail ?? null,
        username: githubUser.login,
        linkedAccount: { githubId: githubUser.id.toString() },
      };
    } catch (error) {
      this.logger.error(error.message);
      throw new UnauthorizedException('Invalid Github access token');
    }
  }
}
