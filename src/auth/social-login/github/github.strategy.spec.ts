jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

import { HttpException, UnauthorizedException } from '@nestjs/common';

import { GithubStrategy } from './github.strategy';
import { Octokit } from 'octokit';

describe('GithubStrategy', () => {
  let githubStrategy: GithubStrategy;
  let mockOctokit: jest.Mocked<Octokit>;

  beforeEach(() => {
    githubStrategy = new GithubStrategy();
    const mockRest = {
      users: {
        getAuthenticated: jest.fn(),
        listEmailsForAuthenticatedUser: jest.fn(),
        verifyAccessToken: jest.fn(),
      },
    };
    mockOctokit = {
      rest: mockRest,
    } as any as jest.Mocked<Octokit>;
    (Octokit as unknown as jest.Mock).mockClear();
    (Octokit as unknown as jest.Mock).mockReturnValue(mockOctokit);
  });

  it('should be defined', () => {
    expect(githubStrategy).toBeDefined();
  });

  describe('verifyAccessToken', () => {
    it('should return SocialUserPayload on successful verification with primary verified email', async () => {
      const mockAccessToken = 'test_access_token';
      const mockGithubUser = {
        id: 12345,
        login: 'testuser',
      };
      const mockEmails = {
        data: [
          { email: '<EMAIL>', primary: false, verified: true },
          { email: '<EMAIL>', primary: true, verified: true },
          { email: '<EMAIL>', primary: true, verified: false },
        ],
      };

      jest.spyOn(mockOctokit.rest.users, 'getAuthenticated').mockResolvedValue({ data: mockGithubUser } as any);
      jest
        .spyOn(mockOctokit.rest.users, 'listEmailsForAuthenticatedUser')
        .mockResolvedValue({ data: mockEmails.data } as any);

      const result = await githubStrategy.verifyAccessToken(mockAccessToken);

      expect(Octokit).toHaveBeenCalledWith({ auth: mockAccessToken, scopes: 'User' });
      expect(mockOctokit.rest.users.getAuthenticated).toHaveBeenCalled();
      expect(mockOctokit.rest.users.listEmailsForAuthenticatedUser).toHaveBeenCalled();
      expect(result).toEqual({
        sub: mockGithubUser.id.toString(),
        email: '<EMAIL>',
        username: mockGithubUser.login,
        linkedAccount: { githubId: mockGithubUser.id.toString() },
      });
    });

    it('should return SocialUserPayload on successful verification even if primary email is not first in list', async () => {
      const mockAccessToken = 'test_access_token';
      const mockGithubUser = {
        id: 12345,
        login: 'testuser',
      };
      const mockEmails = {
        data: [
          { email: '<EMAIL>', primary: false, verified: true },
          { email: '<EMAIL>', primary: true, verified: true },
        ],
      };

      jest.spyOn(mockOctokit.rest.users, 'getAuthenticated').mockResolvedValue({ data: mockGithubUser } as any);
      jest
        .spyOn(mockOctokit.rest.users, 'listEmailsForAuthenticatedUser')
        .mockResolvedValue({ data: mockEmails.data } as any);

      const result = await githubStrategy.verifyAccessToken(mockAccessToken);

      expect(result).toEqual({
        sub: mockGithubUser.id.toString(),
        email: '<EMAIL>',
        username: mockGithubUser.login,
        linkedAccount: { githubId: mockGithubUser.id.toString() },
      });
    });

    it('should return SocialUserPayload on successful verification even if there is only one email and it is primary and verified', async () => {
      const mockAccessToken = 'test_access_token';
      const mockGithubUser = {
        id: 12345,
        login: 'testuser',
      };
      const mockEmails = {
        data: [{ email: '<EMAIL>', primary: true, verified: true }],
      };

      jest.spyOn(mockOctokit.rest.users, 'getAuthenticated').mockResolvedValue({ data: mockGithubUser } as any);
      jest
        .spyOn(mockOctokit.rest.users, 'listEmailsForAuthenticatedUser')
        .mockResolvedValue({ data: mockEmails.data } as any);

      const result = await githubStrategy.verifyAccessToken(mockAccessToken);

      expect(result).toEqual({
        sub: mockGithubUser.id.toString(),
        email: '<EMAIL>',
        username: mockGithubUser.login,
        linkedAccount: { githubId: mockGithubUser.id.toString() },
      });
    });

    it('should throw UnauthorizedException if Github user data is not found', async () => {
      const mockAccessToken = 'invalid_access_token';

      jest.spyOn(mockOctokit.rest.users, 'getAuthenticated').mockResolvedValueOnce(null as any); // Simulate user not found
      jest.spyOn(mockOctokit.rest.users, 'listEmailsForAuthenticatedUser').mockImplementation(() => {
        throw new Error('This function should not be called');
      });
      jest
        .spyOn(githubStrategy, 'verifyAccessToken')
        .mockRejectedValue(new UnauthorizedException('Invalid Linkedin access token'));

      await expect(githubStrategy.verifyAccessToken(mockAccessToken)).rejects.toThrowError(HttpException);
      await expect(githubStrategy.verifyAccessToken(mockAccessToken)).rejects.toThrowError(
        'Invalid Linkedin access token',
      );
    });

    it('should throw UnauthorizedException if no primary verified email is found', async () => {
      const mockAccessToken = 'test_access_token';
      const mockGithubUser = {
        id: 12345,
        login: 'testuser',
      };
      const mockEmails = {
        data: [
          { email: '<EMAIL>', primary: false, verified: true },
          { email: '<EMAIL>', primary: true, verified: false },
        ],
      };

      jest.spyOn(mockOctokit.rest.users, 'getAuthenticated').mockResolvedValue({ data: mockGithubUser } as any);
      jest
        .spyOn(mockOctokit.rest.users, 'listEmailsForAuthenticatedUser')
        .mockResolvedValue({ data: mockEmails.data } as any);

      const resultPromise = githubStrategy.verifyAccessToken(mockAccessToken);

      await expect(resultPromise).resolves.toEqual({
        sub: mockGithubUser.id.toString(),
        email: null, // Expect null email when no primary verified email is found
        username: mockGithubUser.login,
        linkedAccount: { githubId: mockGithubUser.id.toString() },
      });
    });

    it('should return SocialUserPayload with null email if no emails are returned', async () => {
      const mockAccessToken = 'test_access_token';
      const mockGithubUser = {
        id: 12345,
        login: 'testuser',
      };
      const mockEmails = {
        data: [],
      };

      jest.spyOn(mockOctokit.rest.users, 'getAuthenticated').mockResolvedValue({ data: mockGithubUser } as any);
      jest
        .spyOn(mockOctokit.rest.users, 'listEmailsForAuthenticatedUser')
        .mockResolvedValue({ data: mockEmails.data } as any);

      const result = await githubStrategy.verifyAccessToken(mockAccessToken);

      expect(result).toEqual({
        sub: mockGithubUser.id.toString(),
        email: null,
        username: mockGithubUser.login,
        linkedAccount: { githubId: mockGithubUser.id.toString() },
      });
    });
  });
});
