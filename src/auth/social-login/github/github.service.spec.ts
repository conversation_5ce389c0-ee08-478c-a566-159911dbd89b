jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

import { HttpException, HttpStatus, UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { GithubService } from './github.service';
import { GithubStrategy } from './github.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';
import { SocialUserPayload } from '../social-login.service';

const mockGithubStrategy = () => ({
  verifyAccessToken: jest.fn(),
});

describe('GithubService', () => {
  let service: GithubService;
  let githubStrategy: ReturnType<typeof mockGithubStrategy>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GithubService, { provide: GithubStrategy, useFactory: mockGithubStrategy }],
    }).compile();

    service = module.get<GithubService>(GithubService);
    githubStrategy = module.get(GithubStrategy);
  });

  describe('githubLogin', () => {
    const socialLoginDto: SocialLoginRequestDto = {
      provider: 'github',
      token: 'test-github-token',
      expires: '2023-12-31T23:59:59.999Z',
      user: {
        name: 'Test User',
        address: 'test-address',
        signature: 'test-signature',
        challengeToken: 'test-challenge',
      } as any,
    };
    const mockSocialUserPayload: SocialUserPayload = {
      sub: 'github-user-id',
      email: '<EMAIL>',
      username: 'testuser',
      linkedAccount: { githubId: 'github-user-id' },
    };

    it('should successfully login and return SocialUserPayload with valid email', async () => {
      githubStrategy.verifyAccessToken.mockResolvedValue(mockSocialUserPayload);

      const result = await service.githubLogin(socialLoginDto);

      expect(githubStrategy.verifyAccessToken).toHaveBeenCalledWith(socialLoginDto.token);
      expect(result).toEqual(mockSocialUserPayload);
    });

    it('should throw BadRequestException if email is missing from githubUser', async () => {
      const mockGithubUserNoEmail = {
        sub: 'github-user-id',
        email: null, // Email is null, simulating missing email
        username: 'testuser',
        linkedAccount: { githubId: 'github-user-id' },
      } as SocialUserPayload;
      githubStrategy.verifyAccessToken.mockResolvedValue(mockGithubUserNoEmail);

      await expect(service.githubLogin(socialLoginDto)).rejects.toThrowError(HttpException);
      await expect(service.githubLogin(socialLoginDto)).rejects.toThrowError(
        'Email address from GitHub is not available. Please ensure your email is public on your GitHub profile or use a different signup method.',
      );
      await expect(service.githubLogin(socialLoginDto)).rejects.toHaveProperty('status', HttpStatus.BAD_REQUEST);
    });

    it('should throw error if verifyAccessToken fails', async () => {
      const errorMessage = 'GitHub token verification failed';
      githubStrategy.verifyAccessToken.mockRejectedValue(new Error(errorMessage)); // Mock verifyAccessToken to reject

      await expect(service.githubLogin(socialLoginDto)).rejects.toThrowError(Error);
      await expect(service.githubLogin(socialLoginDto)).rejects.toThrowError(errorMessage);
      expect(githubStrategy.verifyAccessToken).toHaveBeenCalledWith(socialLoginDto.token);
    });

    it('should throw UnauthorizedException if token is invalid', async () => {
      githubStrategy.verifyAccessToken.mockRejectedValue(new UnauthorizedException('Invalid token'));

      await expect(service.githubLogin(socialLoginDto)).rejects.toThrowError(UnauthorizedException);
      await expect(service.githubLogin(socialLoginDto)).rejects.toThrowError('Invalid token');
    });
  });
});
