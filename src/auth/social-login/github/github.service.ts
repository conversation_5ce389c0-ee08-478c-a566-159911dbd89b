import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { GithubStrategy } from './github.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';

import { SocialUserPayload } from '../social-login.service';

@Injectable()
export class GithubService {
  constructor(private readonly githubStrategy: GithubStrategy) {}

  async githubLogin(socialLoginDto: SocialLoginRequestDto): Promise<SocialUserPayload> {
    const { token } = socialLoginDto;
    const githubUser = await this.githubStrategy.verifyAccessToken(token);

    if (!githubUser.email) {
      // Throw an error if email from GitHub is null or empty
      throw new HttpException(
        'Email address from GitHub is not available. Please ensure your email is public on your GitHub profile or use a different signup method.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return githubUser;
  }
}
