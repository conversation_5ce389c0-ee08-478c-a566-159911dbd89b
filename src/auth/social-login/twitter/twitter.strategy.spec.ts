import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@nestjs/config';
import { TwitterStrategy } from './twitter.strategy';
import { UnauthorizedException } from '@nestjs/common';

describe('TwitterStrategy', () => {
  let twitterStrategy: TwitterStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TwitterStrategy, ConfigService],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn().mockReturnValue('https://api.x.com/2/users/me'),
      })
      .compile();

    twitterStrategy = module.get<TwitterStrategy>(TwitterStrategy);
    jest.spyOn(global, 'fetch').mockReset();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(twitterStrategy).toBeDefined();
  });

  describe('verifyAccessToken', () => {
    const accessToken = 'test-access-token';

    it('should successfully verify access token and return user data', async () => {
      const mockUserData = {
        data: {
          id: 'twitter-user-id',
          name: 'Test User',
          username: 'testuser',
        },
      };
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockUserData,
      } as any);

      const userData = await twitterStrategy.verifyAccessToken(accessToken);

      expect(global.fetch).toHaveBeenCalledWith('https://api.x.com/2/users/me', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      expect(userData).toEqual(mockUserData.data);
    });

    it('should throw UnauthorizedException for 401 Unauthorized response', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 401,
      } as any);

      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError('Invalid Twitter access token');
    });

    it('should throw UnauthorizedException for 403 Forbidden response', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 403,
      } as any);

      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError('Invalid Twitter access token');
    });

    it('should throw UnauthorizedException for other non-OK HTTP responses (e.g., 500)', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 500,
      } as any);

      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError('Invalid Twitter access token');
    });

    it('should throw UnauthorizedException if fetch API call fails (network error)', async () => {
      jest.spyOn(global, 'fetch').mockRejectedValueOnce(new Error('Network error'));

      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError('Invalid Twitter access token');
    });

    it('should throw UnauthorizedException if response is ok but json parsing fails', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => {
          throw new Error('JSON parsing error');
        },
      } as any);

      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError('Invalid Twitter access token');
    });

    it('should throw UnauthorizedException even if response is ok and json is valid but data is missing', async () => {
      const mockResponseWithoutData = { not_data: 'something else' }; // Simulate response without 'data' property
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockResponseWithoutData,
      } as any);

      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError(UnauthorizedException);
      await expect(twitterStrategy.verifyAccessToken(accessToken)).rejects.toThrowError('Invalid Twitter access token');
    });
  });
});
