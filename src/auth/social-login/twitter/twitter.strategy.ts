import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';

@Injectable()
export class TwitterStrategy {
  private readonly logger = new Logger(TwitterStrategy.name);

  constructor(private readonly configService: ConfigService) {}

  async verifyAccessToken(accessToken: string) {
    try {
      const response = await fetch(this.configService.get('TWITTER_API_URL'), {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        // Handle errors based on the HTTP status code
        if (response.status === 401) {
          throw new Error('Invalid or expired access token.');
        } else if (response.status === 403) {
          throw new Error('Forbidden - Token might not have the required scopes.');
        } else {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      }

      const userData = await response.json();

      if (!userData || !userData.data) {
        throw new UnauthorizedException('Invalid Twitter access token');
      }

      return userData.data; // Return the user data object
    } catch (error) {
      this.logger.error(`Invalid Twitter access token: ${error.message}`);
      throw new UnauthorizedException('Invalid Twitter access token');
    }
  }
}
