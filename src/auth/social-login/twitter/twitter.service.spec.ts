import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';

import { TwitterService } from './twitter.service';
import { TwitterStrategy } from './twitter.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';
import { SocialUserPayload } from '../social-login.service';

const mockTwitterStrategy = () => ({
  verifyAccessToken: jest.fn(),
});

describe('TwitterService', () => {
  let service: TwitterService;
  let twitterStrategy: ReturnType<typeof mockTwitterStrategy>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TwitterService, { provide: TwitterStrategy, useFactory: mockTwitterStrategy }],
    }).compile();

    service = module.get<TwitterService>(TwitterService);
    twitterStrategy = module.get(TwitterStrategy);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('twitterLogin', () => {
    const socialLoginDto: SocialLoginRequestDto = {
      provider: 'twitter',
      token: 'test-twitter-token',
      expires: '2023-12-31T23:59:59.999Z',
      user: {
        name: 'Test User',
        address: 'test-address',
        signature: 'test-signature',
        challengeToken: 'test-challenge',
      } as any,
    };
    const mockSocialUserPayload: SocialUserPayload = {
      sub: 'twitter-user-id',
      email: '<EMAIL>',
      username: 'Test User',
      linkedAccount: { twitterId: 'twitter-user-id' },
    };

    it('should successfully login and return SocialUserPayload with valid email', async () => {
      twitterStrategy.verifyAccessToken.mockResolvedValue({
        sub: 'twitter-user-id',
        email: '<EMAIL>',
        name: 'Test User',
      });

      const result = await service.twitterLogin(socialLoginDto);

      expect(twitterStrategy.verifyAccessToken).toHaveBeenCalledWith(socialLoginDto.token);
      expect(result).toEqual(mockSocialUserPayload);
    });

    it('should throw BadRequestException if email is missing from twitterUser', async () => {
      const mockTwitterUserNoEmail = {
        sub: 'twitter-user-id',
        email: null, // Email is null, simulating missing email
        username: 'Test User',
        linkedAccount: { twitterId: 'twitter-user-id' },
      } as SocialUserPayload;
      twitterStrategy.verifyAccessToken.mockResolvedValue(mockTwitterUserNoEmail);

      await expect(service.twitterLogin(socialLoginDto)).rejects.toThrowError(HttpException);
      await expect(service.twitterLogin(socialLoginDto)).rejects.toThrowError(
        'Email address from Twitter is not available. Please ensure your email is public on your Twitter profile or use a different signup method.',
      );
      await expect(service.twitterLogin(socialLoginDto)).rejects.toHaveProperty('status', HttpStatus.BAD_REQUEST);
    });

    it('should throw error if verifyAccessToken fails', async () => {
      const errorMessage = 'Twitter access token verification failed';
      twitterStrategy.verifyAccessToken.mockRejectedValue(new Error(errorMessage)); // Mock verifyAccessToken to reject

      await expect(service.twitterLogin(socialLoginDto)).rejects.toThrowError(Error);
      await expect(service.twitterLogin(socialLoginDto)).rejects.toThrowError(errorMessage);
      expect(twitterStrategy.verifyAccessToken).toHaveBeenCalledWith(socialLoginDto.token);
    });
  });
});
