import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { TwitterStrategy } from './twitter.strategy';
import { SocialLoginRequestDto } from '../../dto/social-auth.dto';

import { SocialUserPayload } from '../social-login.service';

@Injectable()
export class TwitterService {
  constructor(private readonly twitterStrategy: TwitterStrategy) {}

  async twitterLogin(socialLoginDto: SocialLoginRequestDto): Promise<SocialUserPayload> {
    const { token } = socialLoginDto;
    const twitterUser = await this.twitterStrategy.verifyAccessToken(token);

    if (!twitterUser.email) {
      // Throw an error if email from Twitter is null or empty
      throw new HttpException(
        'Email address from Twitter is not available. Please ensure your email is public on your Twitter profile or use a different signup method.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      sub: twitterUser.sub,
      email: twitterUser.email,
      username: twitterUser.name,
      linkedAccount: { twitterId: twitterUser.sub },
    };
  }
}
