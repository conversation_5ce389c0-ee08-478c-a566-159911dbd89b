import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import { PhoneNumberService } from './phone-number.service';
import { SnsModule } from '../../notifications/channels/sns/sns.module';

@Module({
  imports: [TypeOrmModule.forFeature([User]), SnsModule],
  providers: [PhoneNumberService],
  exports: [PhoneNumberService],
})
export class PhoneVerificationModule {}
