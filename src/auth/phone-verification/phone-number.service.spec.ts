jest.mock('libphonenumber-js', () => {
  return {
    __esModule: true,
    ...jest.requireActual('libphonenumber-js'),
  };
});

import * as libphonenumber from 'libphonenumber-js'; // Import the module to mock

import {
  BadRequestException,
  ConflictException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { AuthService } from '../auth.service';
import { EntityManager } from 'typeorm';
import { PhoneNumberService } from './phone-number.service';
import { SnsService } from '../../notifications/channels/sns/sns.service';
import { User } from '../entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

const mockUserRepository = () => ({
  findOne: jest.fn(),
  save: jest.fn(),
  createQueryBuilder: jest.fn().mockReturnValue({
    where: jest.fn().mockReturnThis(),
    withDeleted: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
  }),
  manager: {
    transaction: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  },
});

const mockSnsService = () => ({
  publishSMS: jest.fn(),
});

const mockAuthService = () => ({
  validateUser: jest.fn(),
});

describe('PhoneNumberService', () => {
  let phoneNumberService: PhoneNumberService;
  let userRepository: ReturnType<typeof mockUserRepository>;
  let snsService: ReturnType<typeof mockSnsService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PhoneNumberService,
        { provide: getRepositoryToken(User), useFactory: mockUserRepository },
        { provide: SnsService, useFactory: mockSnsService },
        { provide: AuthService, useFactory: mockAuthService },
      ],
    }).compile();

    phoneNumberService = module.get<PhoneNumberService>(PhoneNumberService);
    userRepository = module.get(getRepositoryToken(User));
    snsService = module.get<SnsService>(SnsService) as any;

    // Mock transaction manager's transaction method
    userRepository.manager.transaction.mockImplementation(async (transactionCode) => {
      return await transactionCode(userRepository as unknown as EntityManager);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('addPhoneNumber', () => {
    const userId = 1;
    const phoneNumber = '**********';
    const countryCode = 'US';
    const parsedPhoneNumber = '+1**********';
    const otp = '123456';

    it('should successfully add phone number and send OTP', async () => {
      const mockUser: User = { id: userId, username: 'testuser' } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUser),
      } as any);
      jest.spyOn(phoneNumberService as any, 'generateOTP').mockReturnValue(otp);

      snsService.publishSMS.mockResolvedValue(undefined);
      jest.spyOn(libphonenumber, 'isValidNumber').mockReturnValue(true);
      const expectedMessage = `THA \nUse the code ${otp} to verify your phone number`;

      const result = await phoneNumberService.addPhoneNumber(userId, phoneNumber, countryCode);

      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
      expect(userRepository.createQueryBuilder().where).toHaveBeenCalledWith('user.id = :userId', { userId });
      expect(userRepository.createQueryBuilder().withDeleted).toHaveBeenCalled();
      expect(userRepository.createQueryBuilder().getOne).toHaveBeenCalled();
      expect(userRepository.manager.transaction).toHaveBeenCalled();

      expect(phoneNumberService['generateOTP']).toHaveBeenCalled();
      expect(snsService.publishSMS).toHaveBeenCalledWith(parsedPhoneNumber, expectedMessage);
      expect(result).toEqual(
        expect.objectContaining({
          success: true,
          message: 'Phone number added and OTP sent',
        }),
      );
    });

    it('should throw NotFoundException if user is not found', async () => {
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(undefined),
      } as any);

      await expect(phoneNumberService.addPhoneNumber(userId, phoneNumber, countryCode)).rejects.toThrowError(
        NotFoundException,
      );
    });

    it('should throw BadRequestException if user is deleted', async () => {
      const mockDeletedUser: User = { id: userId, username: 'testuser', deletedAt: new Date() } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockDeletedUser),
      } as any);

      await expect(phoneNumberService.addPhoneNumber(userId, phoneNumber, countryCode)).rejects.toThrowError(
        BadRequestException,
      );
      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
    });

    it('should throw ConflictException if phone number is already associated with another account.', async () => {
      jest
        .spyOn(phoneNumberService, 'addPhoneNumber')
        .mockRejectedValue(new ConflictException('This phone number is already associated with another account.'));

      await expect(phoneNumberService.addPhoneNumber(userId, phoneNumber, countryCode)).rejects.toThrowError(
        ConflictException,
      );
      await expect(phoneNumberService.addPhoneNumber(userId, phoneNumber, countryCode)).rejects.toMatchObject({
        message: 'This phone number is already associated with another account.',
      });
    });

    it('should throw InternalServerErrorException if SNS service fails', async () => {
      const mockUser: User = { id: userId, username: 'testuser' } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUser),
      } as any);
      jest.spyOn(phoneNumberService as any, 'generateOTP').mockReturnValue(otp);
      jest.spyOn(snsService, 'publishSMS').mockRejectedValue(new Error('SNS failed'));

      await expect(phoneNumberService.addPhoneNumber(userId, phoneNumber, countryCode)).rejects.toThrowError(
        InternalServerErrorException,
      );
      expect(snsService.publishSMS).toHaveBeenCalled();
    });
  });

  describe('updatePhoneNumber', () => {
    it('should call addPhoneNumber with the same parameters', async () => {
      const userId = 1;
      const phoneNumber = '********10';
      const countryCode = 'CA';
      jest.spyOn(phoneNumberService, 'addPhoneNumber').mockResolvedValue({} as any);

      await phoneNumberService.updatePhoneNumber(userId, phoneNumber, countryCode);

      expect(phoneNumberService.addPhoneNumber).toHaveBeenCalledWith(userId, phoneNumber, countryCode);
    });
  });

  describe('deletePhoneNumber', () => {
    const userId = 1;

    it('should successfully delete phone number', async () => {
      const mockUser: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: true,
        phoneOtp: '123456',
        phoneOtpExpiresAt: new Date(),
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUser),
      } as any);

      const result = await phoneNumberService.deletePhoneNumber(userId);

      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
      expect(userRepository.createQueryBuilder().where).toHaveBeenCalledWith('user.id = :userId', { userId });
      expect(userRepository.createQueryBuilder().withDeleted).toHaveBeenCalled();
      expect(userRepository.createQueryBuilder().getOne).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          phoneNumber: null,
          isPhoneVerified: false,
          phoneOtp: null,
          phoneOtpExpiresAt: null,
        }),
      );
      expect(result).toEqual(
        expect.objectContaining({
          message: 'Phone number deleted successfully.',
          success: true,
        }),
      );
    });

    it('should throw NotFoundException if user is not found', async () => {
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(undefined),
      } as any);

      await expect(phoneNumberService.deletePhoneNumber(userId)).rejects.toThrowError(NotFoundException);
    });

    it('should throw BadRequestException if user is deleted', async () => {
      const mockDeletedUser: User = { id: userId, username: 'testuser', deletedAt: new Date() } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockDeletedUser),
      } as any);

      await expect(phoneNumberService.deletePhoneNumber(userId)).rejects.toThrowError(BadRequestException);
      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
    });
  });

  describe('verifyPhoneNumber', () => {
    const userId = 1;
    const otp = '123456';

    it('should successfully verify phone number', async () => {
      const mockUser: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: false,
        phoneOtp: otp,
        phoneOtpExpiresAt: new Date(Date.now() + 600000), // OTP expires in 10 mins
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUser),
      } as any);
      const result = await phoneNumberService.verifyPhoneNumber(userId, otp);

      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
      expect(userRepository.createQueryBuilder().where).toHaveBeenCalledWith('user.id = :userId', { userId });
      expect(userRepository.createQueryBuilder().withDeleted).toHaveBeenCalled();
      expect(userRepository.createQueryBuilder().getOne).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          isPhoneVerified: true,
          phoneOtp: null,
          phoneOtpExpiresAt: null,
        }),
      );
      expect(result).toEqual(
        expect.objectContaining({
          message: 'Phone number verified successfully.',
          success: true,
        }),
      );
    });

    it('should throw NotFoundException if user is not found', async () => {
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(undefined),
      } as any);

      await expect(phoneNumberService.verifyPhoneNumber(userId, otp)).rejects.toThrowError(NotFoundException);
    });

    it('should throw BadRequestException if user is deleted', async () => {
      const mockDeletedUser: User = { id: userId, username: 'testuser', deletedAt: new Date() } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockDeletedUser),
      } as any);

      await expect(phoneNumberService.verifyPhoneNumber(userId, otp)).rejects.toThrowError(BadRequestException);
      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
    });

    it('should throw BadRequestException if phone number is not found', async () => {
      const mockUserWithoutPhone: User = { id: userId, username: 'testuser', isPhoneVerified: false } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUserWithoutPhone),
      } as any);

      await expect(phoneNumberService.verifyPhoneNumber(userId, otp)).rejects.toThrowError(BadRequestException);
    });

    it('should throw BadRequestException if phone number is already verified', async () => {
      const mockVerifiedUser: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: true,
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockVerifiedUser),
      } as any);

      await expect(phoneNumberService.verifyPhoneNumber(userId, otp)).rejects.toThrowError(BadRequestException);
    });

    it('should throw BadRequestException for invalid or expired OTP', async () => {
      const mockUserWithExpiredOTP: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: false,
        phoneOtp: 'wrongOTP',
        phoneOtpExpiresAt: new Date(Date.now() - 60000), // OTP expired 1 min ago
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUserWithExpiredOTP),
      } as any);

      await expect(phoneNumberService.verifyPhoneNumber(userId, otp)).rejects.toThrowError(BadRequestException);
    });

    it('should throw BadRequestException for invalid OTP', async () => {
      const mockUserWithWrongOTP: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: false,
        phoneOtp: 'wrongOTP',
        phoneOtpExpiresAt: new Date(Date.now() + 600000), // OTP expires in 10 mins
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUserWithWrongOTP),
      } as any);

      await expect(phoneNumberService.verifyPhoneNumber(userId, otp)).rejects.toThrowError(BadRequestException);
    });
  });

  describe('resendVerificationCode', () => {
    const userId = 1;
    const parsedPhoneNumber = '+1**********';
    const otp = '654321';

    it('should successfully resend verification code', async () => {
      const mockUser: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: false,
        phoneOtp: 'oldOTP',
        phoneOtpExpiresAt: new Date(Date.now() - 60000), // OTP expired 1 min ago
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUser),
      } as any);
      jest.spyOn(phoneNumberService as any, 'generateOTP').mockReturnValue(otp);
      jest.spyOn(snsService, 'publishSMS').mockResolvedValue(undefined);

      const result = await phoneNumberService.resendVerificationCode(userId);

      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
      expect(userRepository.createQueryBuilder().where).toHaveBeenCalledWith('user.id = :userId', { userId });
      expect(userRepository.createQueryBuilder().withDeleted).toHaveBeenCalled();
      expect(userRepository.createQueryBuilder().getOne).toHaveBeenCalled();
      expect(phoneNumberService['generateOTP']).toHaveBeenCalled();
      expect(snsService.publishSMS).toHaveBeenCalledWith(
        parsedPhoneNumber,
        `THA \nUse the code ${otp} to verify your phone number`,
      );
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          phoneOtp: otp,
          phoneOtpExpiresAt: expect.any(Date),
        }),
      );
      expect(result).toEqual(
        expect.objectContaining({
          message: 'New OTP sent successfully.',
          success: true,
        }),
      );
    });

    it('should throw NotFoundException if user is not found', async () => {
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(undefined),
      } as any);

      await expect(phoneNumberService.resendVerificationCode(userId)).rejects.toThrowError(NotFoundException);
    });

    it('should throw BadRequestException if user is deleted', async () => {
      const mockDeletedUser: User = { id: userId, username: 'testuser', deletedAt: new Date() } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockDeletedUser),
      } as any);

      await expect(phoneNumberService.resendVerificationCode(userId)).rejects.toThrowError(BadRequestException);
      expect(userRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
    });

    it('should throw BadRequestException if phone number is not found', async () => {
      const mockUserWithoutPhone: User = { id: userId, username: 'testuser', isPhoneVerified: false } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUserWithoutPhone),
      } as any);

      await expect(phoneNumberService.resendVerificationCode(userId)).rejects.toThrowError(BadRequestException);
    });

    it('should throw BadRequestException if phone number is already verified', async () => {
      const mockVerifiedUser: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: true,
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockVerifiedUser),
      } as any);

      await expect(phoneNumberService.resendVerificationCode(userId)).rejects.toThrowError(BadRequestException);
    });

    it('should throw BadRequestException if verification code is still valid', async () => {
      const mockUserWithValidOTP: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: false,
        phoneOtp: 'validOTP',
        phoneOtpExpiresAt: new Date(Date.now() + 600000), // OTP expires in 10 mins
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUserWithValidOTP),
      } as any);

      await expect(phoneNumberService.resendVerificationCode(userId)).rejects.toThrowError(BadRequestException);
    });

    it('should throw InternalServerErrorException if SNS service fails on resend', async () => {
      const mockUser: User = {
        id: userId,
        username: 'testuser',
        phoneNumber: '+1**********',
        isPhoneVerified: false,
        phoneOtp: 'oldOTP',
        phoneOtpExpiresAt: new Date(Date.now() - 60000), // OTP expired 1 min ago
      } as any;
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        withDeleted: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockUser),
      } as any);
      jest.spyOn(phoneNumberService as any, 'generateOTP').mockReturnValue(otp);
      jest.spyOn(snsService, 'publishSMS').mockRejectedValue(new Error('SNS failed'));

      await expect(phoneNumberService.resendVerificationCode(userId)).rejects.toThrowError(
        InternalServerErrorException,
      );
      expect(snsService.publishSMS).toHaveBeenCalled();
    });
  });
});
