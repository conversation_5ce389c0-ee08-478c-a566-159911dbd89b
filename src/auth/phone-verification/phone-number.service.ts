import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { parsePhoneNumberWithError, isValidNumber, CountryCode } from 'libphonenumber-js';
import * as crypto from 'crypto';
import { User } from '../entities/user.entity';
import { SnsService } from '../../notifications/channels/sns/sns.service';

@Injectable()
export class PhoneNumberService {
  private readonly logger = new Logger(PhoneNumberService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly snsService: SnsService,
  ) {}

  async addPhoneNumber(userId: number, phoneNumber: string, countryCode?: CountryCode) {
    return this.userRepository.manager.transaction(async (transactionalEntityManager: EntityManager) => {
      if (!isValidNumber(phoneNumber, countryCode)) {
        throw new BadRequestException('Invalid phone number format.');
      }
      const parsedPhoneNumber = parsePhoneNumberWithError(phoneNumber, countryCode).number;
      const user = await this.validateUser(userId, parsedPhoneNumber);

      if (user.phoneNumber === parsedPhoneNumber) {
        this.validateOtpWaitPeriod(user);
      }

      if (user.phoneNumber === parsedPhoneNumber && user.isPhoneVerified) {
        throw new BadRequestException('This phone number is already added and verified.');
      }

      const otp = this.generateOTP();
      const otpExpiresAt = new Date();
      // OTP expires in 10 minutes
      otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + 10);

      user.phoneNumber = parsedPhoneNumber;
      user.isPhoneVerified = false;
      user.phoneOtp = otp;
      user.phoneOtpExpiresAt = otpExpiresAt;

      await transactionalEntityManager.save(user);
      await this.sendOTP(user.phoneNumber, otp);

      return { success: true, message: 'Phone number added and OTP sent' };
    });
  }

  async updatePhoneNumber(userId: number, phoneNumber: string, countryCode?: CountryCode) {
    return this.addPhoneNumber(userId, phoneNumber, countryCode);
  }

  async deletePhoneNumber(userId: number) {
    return this.userRepository.manager.transaction(async (transactionalEntityManager: EntityManager) => {
      const user = await this.validateUser(userId);

      user.phoneNumber = null;
      user.isPhoneVerified = false;
      user.phoneOtp = null;
      user.phoneOtpExpiresAt = null;
      await transactionalEntityManager.save(user);
      return {
        success: true,
        message: 'Phone number deleted successfully.',
      };
    });
  }

  async verifyPhoneNumber(userId: number, otp: string) {
    return this.userRepository.manager.transaction(async (transactionalEntityManager: EntityManager) => {
      const user = await this.validateUser(userId);

      if (!user.phoneNumber) {
        throw new BadRequestException('Phone number not found. Please add phone number first.');
      }

      if (user.isPhoneVerified) {
        throw new BadRequestException('Phone number already verified.');
      }

      if (!user.phoneOtp || !user.phoneOtpExpiresAt || user.phoneOtpExpiresAt < new Date()) {
        throw new BadRequestException('Invalid or expired OTP. Please request a new OTP.');
      }

      if (user.phoneOtp !== otp) {
        throw new BadRequestException('Invalid OTP.');
      }

      user.isPhoneVerified = true;
      user.phoneOtp = null;
      user.phoneOtpExpiresAt = null;

      await transactionalEntityManager.save(user);
      return { success: true, message: 'Phone number verified successfully.' };
    });
  }

  async resendVerificationCode(userId: number) {
    return this.userRepository.manager.transaction(async (transactionalEntityManager: EntityManager) => {
      const user = await this.validateUser(userId);

      if (!user.phoneNumber) {
        throw new BadRequestException('Phone number not found. Please add phone number first.');
      }

      if (user.isPhoneVerified) {
        throw new BadRequestException('Phone number already verified.');
      }

      this.validateOtpWaitPeriod(user);

      const otp = this.generateOTP();
      const otpExpiresAt = new Date();
      otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + 10);

      user.phoneOtp = otp;
      user.phoneOtpExpiresAt = otpExpiresAt;

      await transactionalEntityManager.save(user);
      await this.sendOTP(user.phoneNumber, otp);

      return { success: true, message: 'New OTP sent successfully.' };
    });
  }

  private validateOtpWaitPeriod(user: User): void {
    const now = new Date();
    if (user.phoneOtp && user.phoneOtpExpiresAt && user.phoneOtpExpiresAt > now) {
      const timeRemaining = Math.ceil((user.phoneOtpExpiresAt.getTime() - now.getTime()) / 60000);
      throw new BadRequestException(
        `An OTP for this number is still valid. Please use the existing code or wait ${timeRemaining} more minute(s) before requesting a new one.`,
      );
    }
  }

  private async validateUser(userId: number, phoneNumber?: string): Promise<User | undefined> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .where('user.id = :userId', { userId })
      .withDeleted()
      .getOne();

    if (!user) {
      this.logger.warn(`User validation failed: User ID ${userId} not found.`);
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    if (user.deletedAt) {
      this.logger.warn(`User validation failed: User ID ${userId} is deleted.`);
      throw new BadRequestException('Account is deleted and cannot be modified. Please recover your account first.');
    }

    if (phoneNumber) {
      const existingUserWithPhone = await this.userRepository.findOne({
        where: { phoneNumber },
      });

      if (existingUserWithPhone && existingUserWithPhone.id !== userId) {
        // Check if phone exists for *another* user
        this.logger.warn(
          `Phone number validation failed: Phone ${phoneNumber} already used by user ID ${existingUserWithPhone.id}.`,
        );
        throw new ConflictException('This phone number is already associated with another account.');
      }
    }

    return user;
  }

  private generateOTP(): string {
    const buffer = crypto.randomBytes(3);
    const otp = parseInt(buffer.toString('hex'), 16).toString().slice(0, 6);
    return otp.padStart(6, '0');
  }

  private async sendOTP(phoneNumber: string, otp: string): Promise<void> {
    const messageBody = `Use the code ${otp} to verify your phone number`;
    const message = `THA \n${messageBody}`;

    try {
      await this.snsService.publishSMS(phoneNumber, message);
    } catch (error) {
      this.logger.warn(`sending OTP failed: ${error.message}`);
      throw new InternalServerErrorException('Failed to send OTP. Please try again later.');
    }
  }
}
