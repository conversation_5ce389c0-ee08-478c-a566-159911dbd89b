import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Query,
  Req,
  Request,
  Headers,
  Put,
  Delete,
  Patch,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  VerifySignature,
  RequestChallenge,
  VerifyOtp,
  SignIn,
  SignUp,
  RecoverAccount,
  UpdateWallet,
  SignInResponse,
  RequestChallengeResponse,
  UserExists,
  FetchUserResponse,
  VerifyOtpResponse,
  RecoverDeletedAccount,
} from './dto';
import { AuthGuard } from './auth.guard';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConflictResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UpdateDisplayNameDto } from './dto/update-display-name.dto';
import { SocialAuthResponse, SocialLoginRequestDto } from './dto/social-auth.dto';
import { SocialLoginService } from './social-login/social-login.service';
import { PhoneNumberService } from './phone-verification/phone-number.service';
import { PhoneNumberDto, VerifyPhoneNumberDto } from './dto/phone-verification.dto';
import { RequestUser, RequestUserPayload } from './request-user.decorator';
import { SuccessResponse } from '../common/dto/success-response.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly socialLoginService: SocialLoginService,
    private readonly phoneNumberService: PhoneNumberService,
  ) {}

  @Get('/request-challenge')
  @ApiOperation({ summary: 'Request a challenge to sign.' })
  @ApiResponse({
    status: 200,
    description: 'The challenge was successfully requested.',
    type: RequestChallengeResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid wallet address.',
  })
  requestChallenge(@Query() requestChallenge: RequestChallenge, @Headers('Referer') referer: string) {
    return this.authService.requestChallenge(requestChallenge, referer);
  }

  @Post('/sign-in')
  @ApiOperation({ summary: 'Sign in the user.' })
  @ApiResponse({
    status: 201,
    description: 'The user was successfully signed in.',
    type: SignInResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'Address in the request does not match the address in the challenge.',
  })
  @ApiResponse({
    status: 401,
    description: 'Please provide a valid challenge and signature.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  signIn(@Body() signIn: SignIn): Promise<SignInResponse> {
    return this.authService.signIn(signIn);
  }

  @Post('/sign-up')
  @ApiOperation({ summary: 'Sign up the user.' })
  @ApiResponse({
    status: 201,
    description: 'The user was created successfully.',
    type: SuccessResponse,
    example: {
      success: true,
      message: 'User created successfully.',
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Address in the request does not match the address in the challenge.',
  })
  @ApiResponse({
    status: 401,
    description: 'Please provide a valid email address.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  signUp(@Body() signUp: SignUp) {
    return this.authService.signUp(signUp);
  }

  @Post('/recover-account')
  @ApiOperation({ summary: 'Initiate user account recovery.' })
  @ApiResponse({
    status: 201,
    description: 'A recovery email was successfully sent to the user.',
    example: {
      success: true,
      message: 'Recovery email sent to the user.',
    },
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  recoverAccount(@Body() recoverAccount: RecoverAccount) {
    return this.authService.recoverAccount(recoverAccount);
  }

  @Post('/update-wallet')
  @ApiOperation({ summary: 'Update the user wallet.' })
  @ApiResponse({
    status: 201,
    description: "The user's wallet was successfully updated.",
    example: {
      success: true,
      message: 'Wallet address updated successfully.',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid JWT Token.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  updateWalletAddress(@Body() updateWallet: UpdateWallet) {
    return this.authService.updateWalletAddress(updateWallet);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('/send-otp')
  @ApiOperation({
    summary: "Send a one-time password for email verification to the user's email.",
  })
  @ApiResponse({
    status: 201,
    description: 'The one-time password was successfully sent.',
    example: {
      success: true,
      message: 'OTP sent to the user.',
    },
    type: SuccessResponse,
  })
  @ApiResponse({
    status: 400,
    description: "If the user's email address is already verified.",
  })
  @ApiResponse({
    status: 401,
    description: 'If the user is not signed in.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'If the server failed to send the OTP email.',
  })
  sendOTP(@RequestUser() requestUser: RequestUserPayload) {
    return this.authService.sendOTP(requestUser);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('/verify-otp')
  @ApiOperation({
    summary: 'Verify the one-time password sent to the user.',
  })
  @ApiResponse({
    status: 201,
    description: 'Email verification was successful. Response contains a new session token.',
    type: VerifyOtpResponse,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid OTP.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  verifyOTP(@Body() verifyOtp: VerifyOtp, @RequestUser() requestUser: RequestUserPayload): Promise<VerifyOtpResponse> {
    return this.authService.verifyOTP(verifyOtp, requestUser);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('/user')
  @ApiOperation({ summary: 'Get the user data.' })
  @ApiResponse({
    status: 200,
    description: 'The user data was successfully retrieved.',
    type: FetchUserResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  getUser(@RequestUser() requestUser: RequestUserPayload): Promise<FetchUserResponse> {
    return this.authService.userDetails(requestUser);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('/user/exists')
  @ApiOperation({ summary: 'Check if the user with given email address exists.' })
  @ApiResponse({
    status: 200,
    description: 'The request was successful.',
    example: {
      success: true,
      exists: true,
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  checkUserExists(@Query() userExists: UserExists) {
    return this.authService.userExists(userExists);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('/add-wallet')
  @ApiOperation({ summary: "Add a new wallet to the user's account" })
  @ApiResponse({
    status: 200,
    description: 'The wallet was successfully added to the account.',
    example: {
      success: true,
      message: 'Wallet successfully added to the account.',
    },
  })
  addWalletToAccount(@Body() verifySignature: VerifySignature, @RequestUser() requestUser: RequestUserPayload) {
    return this.authService.addWalletToAccount(verifySignature, requestUser);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Update the display name of the user.' })
  @ApiResponse({
    status: 200,
    description: 'Display name updated successfully.',
    example: {
      success: true,
      message: 'Display name updated successfully.',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid signature.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  @Put('update-display-name')
  async updateDisplayName(
    @Body() updateDisplayNameDto: UpdateDisplayNameDto,
    @RequestUser() requestUser: RequestUserPayload,
  ) {
    return this.authService.updateDisplayName(updateDisplayNameDto, requestUser);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete('delete-account')
  @ApiOperation({ summary: 'Delete the user account.' })
  @ApiResponse({
    status: 200,
    description: 'Account deleted successfully.',
    example: {
      success: true,
      message: 'Account deleted successfully.',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid signature.',
  })
  @ApiResponse({
    status: 403,
    description: 'Users with the role GRANT_PROGRAM_COORDINATOR cannot delete their accounts.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
  })
  async deleteAccount(@RequestUser() requestUser: RequestUserPayload) {
    return this.authService.deleteAccount(requestUser);
  }

  @Post('recover-deleted-account')
  @ApiOperation({ summary: 'Recover a soft-deleted user account' })
  @ApiBody({ type: RecoverDeletedAccount, description: 'User recovery' })
  @ApiResponse({ status: 200, description: 'Account recovered successfully.', type: SuccessResponse })
  @ApiResponse({ status: 404, description: 'No deleted account found for given wallet address.' })
  @ApiResponse({ status: 400, description: 'Invalid token scope or address mismatch.' })
  @ApiResponse({ status: 401, description: 'Unauthorized challenge or invalid signature.' })
  @ApiResponse({ status: 500, description: 'Failed to recover account.' })
  recoverDeletedAccount(@Body() account: RecoverDeletedAccount) {
    return this.authService.recoverDeletedAccount(account);
  }

  @Post('social-login')
  @ApiBody({ type: SocialLoginRequestDto, description: 'Social Login Request' })
  @ApiOperation({ summary: 'Login with social provider' })
  @ApiResponse({
    status: 200,
    description: 'User logged in successfully.',
    type: SocialAuthResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'Address in the request does not match the address in the challenge.',
  })
  @ApiResponse({
    status: 401,
    description: 'Please provide a valid challenge and signature.',
  })
  async socialLogin(@Body() socialLoginDto: SocialLoginRequestDto): Promise<SocialAuthResponse> {
    return this.socialLoginService.socialLogin(socialLoginDto);
  }

  @Post('phone-number')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Add phone number for authenticated user' })
  @ApiResponse({
    status: 201,
    description: 'Phone number added and OTP sent',
    content: {
      'application/json': {
        example: { success: true, message: 'Phone number added and OTP sent' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid phone number format or user input, Account is deleted and cannot be modified.',
  })
  @ApiConflictResponse({ description: 'This phone number is already associated with another account.' })
  @ApiNotFoundResponse({ description: 'User not found' })
  async addPhoneNumber(@Req() req: Request, @Body() phoneNumberDto: PhoneNumberDto) {
    const userId = req['user'].id;
    return this.phoneNumberService.addPhoneNumber(userId, phoneNumberDto.phoneNumber, phoneNumberDto.countryCode);
  }

  @Patch('phone-number')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Update phone number for authenticated user' })
  @ApiOkResponse({
    description: 'Phone number updated and OTP sent',
    content: {
      'application/json': {
        example: { success: true, message: 'Phone number added and OTP sent' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid phone number format or user input, Account is deleted and cannot be modified.',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  async updatePhoneNumber(@Req() req: Request, @Body() phoneNumberDto: PhoneNumberDto) {
    const userId = req['user'].id;
    return this.phoneNumberService.updatePhoneNumber(userId, phoneNumberDto.phoneNumber, phoneNumberDto.countryCode);
  }

  @Delete('phone-number')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Delete phone number for authenticated user' })
  @ApiOkResponse({
    description: 'Phone number deleted successfully.',
    content: {
      'application/json': {
        example: { success: true, message: 'Phone number deleted successfully.' },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Account is deleted and cannot be modified.' })
  @ApiNotFoundResponse({ description: 'User not found' })
  async deletePhoneNumber(@Req() req: Request) {
    const userId = req['user'].id;
    return this.phoneNumberService.deletePhoneNumber(userId);
  }

  @Post('phone-number/verify')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Verify phone number using OTP for authenticated user' })
  @ApiOkResponse({
    description: 'Phone number verified successfully.',
    content: {
      'application/json': {
        example: { success: true, message: 'Phone number verified successfully.' },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  async verifyPhoneNumber(@Req() req: Request, @Body() verifyPhoneNumberDto: VerifyPhoneNumberDto) {
    const userId = req['user'].id;
    return this.phoneNumberService.verifyPhoneNumber(userId, verifyPhoneNumberDto.otp);
  }

  @Get('phone-number/resend')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Resend verification code (OTP) for authenticated user' })
  @ApiOkResponse({
    description: 'New OTP sent successfully.',
    content: {
      'application/json': {
        example: { success: true, message: 'New OTP sent successfully.' },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'User or phone number not found' })
  async resendVerificationCode(@Req() req: Request) {
    const userId = req['user'].id;
    return this.phoneNumberService.resendVerificationCode(userId);
  }
}
