import { Auth<PERSON>ontroller } from './auth.controller';
import { AuthService } from './auth.service';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MailModule } from '../notifications/mail/mail.module';
import { Module } from '@nestjs/common';
import { OptionalAuthGuard } from './optional-auth.guard';
import { PhoneVerificationModule } from './phone-verification/phone-verification.module';
import { SocialLoginModule } from './social-login/social-login.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';

@Module({
  imports: [
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRATION_TIME') },
      }),
    }),
    TypeOrmModule.forFeature([User]),
    MailModule,
    SocialLoginModule,
    PhoneVerificationModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, OptionalAuthGuard],
  exports: [AuthService, JwtModule, OptionalAuthGuard],
})
export class AuthModule {}
