type MessageChunkInfo = {
  initial_transaction_id: string;
  nonce: number;
  number: number;
  total: number;
  scheduled: boolean;
};

export type Message = {
  chunk_info: MessageChunkInfo;
  consensus_timestamp: number;
  message: string;
  payer_account_id: string;
  running_hash: string;
  running_hash_version: number;
  sequence_number: number;
  topic_id: string;
};

export type HederaTopicMessagesResponse = {
  messages: Message[];
  links: {
    next: null;
  };
};
