import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

import { TransactionId } from '@hashgraph/sdk';

@ValidatorConstraint({ name: 'isValidTransactionId', async: false })
export class IsValidTransactionId implements ValidatorConstraintInterface {
  validate(value: any) {
    if (typeof value !== 'string' || value.trim().length === 0) {
      return false;
    }

    try {
      TransactionId.fromString(value);
      return true;
    } catch {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments): string {
    return `Property '${args.property}' must be a valid Hedera transaction ID string (e.g., '0.0.123@0000000000.0000000000').`;
  }
}
