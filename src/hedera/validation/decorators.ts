import { ValidationOptions, registerDecorator } from 'class-validator';

import { IsValidTransactionId } from './is-valid-transaction-id.validator';

export function IsHederaTxId(validationOptions?: ValidationOptions): PropertyDecorator {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidTransactionId,
    });
  };
}
