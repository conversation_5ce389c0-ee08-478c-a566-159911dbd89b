import {
  AccountId,
  Client,
  PublicKey,
  TopicCreateTransaction,
  TopicId,
  TopicMessageSubmitTransaction,
  Transaction,
  TransactionId,
} from '@hashgraph/sdk';
import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { HederaTopicMessagesResponse, Message } from './hedera.types';
import { catchError, firstValueFrom, map, throwError } from 'rxjs';

import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { KeyManagementService } from '../key-management/key-management.service';

@Injectable()
export class HederaService {
  private readonly logger = new Logger(HederaService.name);
  private client: Client;
  private operatorPublicKey: PublicKey;
  private hederaTopicsAPI: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly kmsService: KeyManagementService,
  ) {
    const operatorAccountId = this.configService.getOrThrow<string>('HEDERA_ACCOUNT_ID');
    const hederaNetwork = this.configService.get<string>('HEDERA_NETWORK', 'testnet');

    this.hederaTopicsAPI = `${this.configService.getOrThrow<string>('MIRROR_NODE_URL')}/api/v1/topics`;

    this.operatorPublicKey = PublicKey.fromString(
      this.configService.getOrThrow<string>('HEDERA_OPERATOR_PUBLIC_KEY_DER'),
    );

    this.client = Client.forName(hederaNetwork).setOperatorWith(
      operatorAccountId,
      this.operatorPublicKey,
      this.kmsService.signMessage.bind(this.kmsService),
    );
  }

  async createTopic(): Promise<TopicId> {
    try {
      const transaction = new TopicCreateTransaction({
        adminKey: this.operatorPublicKey,
        submitKey: this.operatorPublicKey,
      });

      const txResponse = await transaction.execute(this.client);

      const receipt = await txResponse.getReceipt(this.client);
      const { topicId } = receipt;

      this.logger.log(`Topic created successfully with ID: ${topicId}`);
      return topicId;
    } catch (error) {
      this.logger.error('Error creating topic:', error);
      throw new HttpException(`Failed to create topic: ${error.message}`, HttpStatus.FAILED_DEPENDENCY);
    }
  }

  async createTopicWithRetry(): Promise<TopicId> {
    const maxTopicCreationRetries = this.configService.get<number>('HEDERA_MAX_TOPIC_CREATION_RETRIES', 3);
    const topicCreationRetryDelayMs = this.configService.get<number>('HEDERA_TOPIC_CREATION_RETRY_DELAY_MS', 3000);

    for (let retryAttempt = 0; retryAttempt < maxTopicCreationRetries; retryAttempt++) {
      try {
        return await this.createTopic();
      } catch {
        this.logger.log(`Retrying vote topic creation in ${topicCreationRetryDelayMs / 1000} seconds...`);
        await new Promise((resolve) => setTimeout(resolve, topicCreationRetryDelayMs));
      }
    }

    throw new HttpException(
      'Failed to create Hedera vote topic after multiple retries. Vote preparation failed.',
      HttpStatus.FAILED_DEPENDENCY,
    );
  }

  async submitMessage(topicId: string, message: string): Promise<void> {
    try {
      const transaction = new TopicMessageSubmitTransaction();
      transaction.setTopicId(TopicId.fromString(topicId));
      transaction.setMessage(message);

      const frozenTransaction = transaction.freezeWith(this.client);
      const signedTx = await frozenTransaction.signWithOperator(this.client);

      const txResponse = await signedTx.execute(this.client);
      const receipt = await txResponse.getReceipt(this.client);

      this.logger.log(`Message submitted to topic ${topicId}. Status: ${receipt.status}`);
    } catch (error) {
      this.logger.error(`Error submitting message to topic ${topicId}:`, error);
      throw new HttpException(
        `Failed to submit message to topic ${topicId}: ${error.message}`,
        HttpStatus.FAILED_DEPENDENCY,
      );
    }
  }

  async getMessagesFromTopic(topicId: string): Promise<Message[]> {
    this.logger.debug(`Fetching messages for topic: ${topicId}`);
    const hederaTopicUrl = `${this.hederaTopicsAPI}/${topicId}/messages`;

    const messages = await firstValueFrom(
      this.httpService.get<HederaTopicMessagesResponse>(hederaTopicUrl).pipe(
        catchError((error) => {
          this.logger.error('Error fetching messages from topic:', topicId, error);
          return throwError(
            () =>
              new HttpException(
                `Failed to fetch messages from topic ${topicId}: ${error.message}`,
                HttpStatus.FAILED_DEPENDENCY,
              ),
          );
        }),
        map((response) => response.data.messages),
      ),
    );

    return messages.map(({ message, ...rest }: Message) => {
      return {
        message: Buffer.from(message, 'base64').toString(),
        ...rest,
      };
    });
  }

  async prepareSubmitMessageTransaction<T extends object>(payload: T, topicId: string, payerAccountId?: string) {
    const transaction = new TopicMessageSubmitTransaction().setTopicId(TopicId.fromString(topicId));
    const payloadString = JSON.stringify(payload);
    transaction.setMessage(payloadString);

    if (payerAccountId) {
      transaction.setTransactionId(TransactionId.generate(AccountId.fromString(payerAccountId)));
    }

    const nodes = this.configService.getOrThrow<string>('HEDERA_NODES').split(',');
    const randomNode = nodes[Math.floor(Math.random() * nodes.length)];

    transaction.setNodeAccountIds([AccountId.fromString(randomNode)]);

    const frozenTransaction = transaction.freezeWith(this.client);
    const signTx = await frozenTransaction.signWithOperator(this.client);

    return Buffer.from(signTx.toBytes()).toString('base64');
  }

  async executeSignedTransaction<T extends Transaction>(transaction: T) {
    if (!this.operatorPublicKey.verifyTransaction(transaction)) {
      throw new BadRequestException('Invalid transaction signature');
    }

    const response = await transaction.execute(this.client);

    const receipt = await response.getReceipt(this.client);
    const record = await response.getRecord(this.client);

    return { receipt, record };
  }
}
