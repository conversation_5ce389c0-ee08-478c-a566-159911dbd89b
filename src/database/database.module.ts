import { DataSource } from 'typeorm';
import { Global, Logger, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NumericColumnTransformer } from './column-transformers';

@Global() // makes the module available globally for other modules once imported in the app module
@Module({
  imports: [],
  providers: [
    {
      provide: DataSource,
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const logger = new Logger('DatabaseModule');
        try {
          const dataSource = new DataSource({
            type: 'postgres',
            host: configService.get('POSTGRES_HOST'),
            port: configService.get('POSTGRES_PORT'),
            username: configService.get('POSTGRES_USER'),
            password: configService.get('POSTGRES_PASSWORD'),
            database: configService.get('POSTGRES_DB'),
            ssl: !configService.get('POSTGRES_CA_CERT')
              ? false
              : {
                  rejectUnauthorized: false,
                  ca: configService.get('POSTGRES_CA_CERT').toString(),
                },
            // this will automatically load all entity file in the src folder
            entities: [`${__dirname}/../**/**.entity{.ts,.js}`],
            synchronize: configService.get('POSTGRES_SYNC') == 'true',
            migrations: [`${__dirname}/migrations/**/*{.ts,.js}`],
            migrationsTableName: 'typeorm_migrations',
            migrationsRun: configService.get('POSTGRES_MIGRATIONS_RUN') == 'true',
          });
          await dataSource.initialize();
          logger.log('Database connected successfully');
          return dataSource;
        } catch (error) {
          logger.error('Error connecting to database');
          throw error;
        }
      },
    },
    NumericColumnTransformer,
  ],
  exports: [DataSource, NumericColumnTransformer],
})
export class DatabaseModule {}
