import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1726061896788 implements MigrationInterface {
  name = 'Migration1726061896788';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."user_role_enum" AS ENUM('GRANT_PROGRAM_COORDINATOR', 'USER')`);
    await queryRunner.query(
      `CREATE TABLE "user" ("id" SERIAL NOT NULL, "email" character varying NOT NULL, "addresses" text array NOT NULL, "displayName" character varying NOT NULL, "emailVerified" boolean NOT NULL DEFAULT false, "otp" bigint, "otpExpiresAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "role" "public"."user_role_enum" NOT NULL DEFAULT 'USER', CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."grant_program_status_enum" AS ENUM('OPEN', 'CLOSED')`);
    await queryRunner.query(
      `CREATE TABLE "grant_program" ("id" SERIAL NOT NULL, "grantProgramSlug" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "scope" character varying NOT NULL, "budget" numeric(12,0) NOT NULL, "grantorPublicProfileName" character varying NOT NULL, "grantorLogoURL" character varying NOT NULL, "grantorPublicProfileSlug" character varying NOT NULL, "status" "public"."grant_program_status_enum" NOT NULL DEFAULT 'OPEN', "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "grantProgramCoordinatorId" integer, CONSTRAINT "UQ_42a28b38bec04a6fd275522eed0" UNIQUE ("grantProgramSlug"), CONSTRAINT "PK_a35fc0314e171da8f544af331ba" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "grant_application_member" ("id" SERIAL NOT NULL, "isCreator" boolean NOT NULL, "userId" integer, "grantApplicationId" integer, CONSTRAINT "PK_88f41e2e4a893e2fe8b7faf5fc5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."grant_application_status_enum" AS ENUM('OPEN', 'WITHDRAWN', 'REJECTED', 'APPROVED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "grant_application" ("id" SERIAL NOT NULL, "title" character varying NOT NULL, "description" character varying NOT NULL, "companyName" character varying NOT NULL, "companyCountry" character varying NOT NULL, "companyWebpage" character varying NOT NULL, "industry" character varying NOT NULL, "contactFullName" character varying NOT NULL, "contactEmail" character varying NOT NULL, "contactPhoneNumber" character varying NOT NULL, "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "status" "public"."grant_application_status_enum" NOT NULL DEFAULT 'OPEN', "statusChangedOn" TIMESTAMP NOT NULL DEFAULT now(), "grantCallId" integer, "assigneeId" integer, CONSTRAINT "PK_0c45acfa71f48353eefd64ea6b5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "grant_application_stage_log" ("id" SERIAL NOT NULL, "startedOn" TIMESTAMP NOT NULL DEFAULT now(), "finishedOn" TIMESTAMP, "grantApplicationId" integer, "stageId" integer, CONSTRAINT "PK_32955a462406f286c6fad930bf0" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "grant_application_stage" ("id" SERIAL NOT NULL, "title" character varying NOT NULL, "subtitle" character varying NOT NULL, "description" character varying NOT NULL, "expectedDuration" integer NOT NULL, "position" integer NOT NULL, "actionDescription" character varying, "actionButtonText" character varying, "actionLink" character varying, "grantCallId" integer, CONSTRAINT "PK_5dd02b91702505f1e1ee91100fc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "grant_call" ("id" SERIAL NOT NULL, "grantCallSlug" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "businessCategory" character varying NOT NULL, "targetedIndustries" text NOT NULL, "startDate" TIMESTAMP WITH TIME ZONE NOT NULL, "endDate" TIMESTAMP WITH TIME ZONE, "isClosed" boolean NOT NULL DEFAULT false, "minGrantSize" numeric(12,0) NOT NULL, "maxGrantSize" numeric(12,0) NOT NULL, "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "grantProgramId" integer, CONSTRAINT "UQ_370dde099c3c0c99078379a2ac9" UNIQUE ("grantCallSlug"), CONSTRAINT "PK_bd962d7fe2f1723f921c44a8882" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "grant_call_member" ("id" SERIAL NOT NULL, "isCoordinator" boolean NOT NULL, "userId" integer, "grantCallId" integer, CONSTRAINT "PK_e62782fa4919b47d212bdb9790f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_program" ADD CONSTRAINT "FK_285ce4002bf23dac14a5d9f7c98" FOREIGN KEY ("grantProgramCoordinatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application_member" ADD CONSTRAINT "FK_8c5ac3494dcf67db381d357d0c2" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application_member" ADD CONSTRAINT "FK_d924032111b73cc7efe5023cac2" FOREIGN KEY ("grantApplicationId") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_c72adc8231b7d3e4bc235dab915" FOREIGN KEY ("grantCallId") REFERENCES "grant_call"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_d3546a9ea3c960fc99b7296d754" FOREIGN KEY ("assigneeId") REFERENCES "grant_call_member"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application_stage_log" ADD CONSTRAINT "FK_273f35204327236d84d0fbff63e" FOREIGN KEY ("grantApplicationId") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application_stage_log" ADD CONSTRAINT "FK_2997a003800c7d671e6a6dab4f3" FOREIGN KEY ("stageId") REFERENCES "grant_application_stage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application_stage" ADD CONSTRAINT "FK_e2a128648f1b4cc74ab9d585c29" FOREIGN KEY ("grantCallId") REFERENCES "grant_call"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "FK_4db3b62454d196f965fbadc743e" FOREIGN KEY ("grantProgramId") REFERENCES "grant_program"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call_member" ADD CONSTRAINT "FK_97835811bfec2ba830e92124fcd" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call_member" ADD CONSTRAINT "FK_979c5832ec2f61a8e59e130afbe" FOREIGN KEY ("grantCallId") REFERENCES "grant_call"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_call_member" DROP CONSTRAINT "FK_979c5832ec2f61a8e59e130afbe"`);
    await queryRunner.query(`ALTER TABLE "grant_call_member" DROP CONSTRAINT "FK_97835811bfec2ba830e92124fcd"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "FK_4db3b62454d196f965fbadc743e"`);
    await queryRunner.query(`ALTER TABLE "grant_application_stage" DROP CONSTRAINT "FK_e2a128648f1b4cc74ab9d585c29"`);
    await queryRunner.query(
      `ALTER TABLE "grant_application_stage_log" DROP CONSTRAINT "FK_2997a003800c7d671e6a6dab4f3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application_stage_log" DROP CONSTRAINT "FK_273f35204327236d84d0fbff63e"`,
    );
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "FK_d3546a9ea3c960fc99b7296d754"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "FK_c72adc8231b7d3e4bc235dab915"`);
    await queryRunner.query(`ALTER TABLE "grant_application_member" DROP CONSTRAINT "FK_d924032111b73cc7efe5023cac2"`);
    await queryRunner.query(`ALTER TABLE "grant_application_member" DROP CONSTRAINT "FK_8c5ac3494dcf67db381d357d0c2"`);
    await queryRunner.query(`ALTER TABLE "grant_program" DROP CONSTRAINT "FK_285ce4002bf23dac14a5d9f7c98"`);
    await queryRunner.query(`DROP TABLE "grant_call_member"`);
    await queryRunner.query(`DROP TABLE "grant_call"`);
    await queryRunner.query(`DROP TABLE "grant_application_stage"`);
    await queryRunner.query(`DROP TABLE "grant_application_stage_log"`);
    await queryRunner.query(`DROP TABLE "grant_application"`);
    await queryRunner.query(`DROP TYPE "public"."grant_application_status_enum"`);
    await queryRunner.query(`DROP TABLE "grant_application_member"`);
    await queryRunner.query(`DROP TABLE "grant_program"`);
    await queryRunner.query(`DROP TYPE "public"."grant_program_status_enum"`);
    await queryRunner.query(`DROP TABLE "user"`);
    await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
  }
}
