import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747227602847 implements MigrationInterface {
  name = 'Migration1747227602847';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."grant_program_status_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."grant_program_status_enum" AS ENUM('OPEN', 'CLOSED')`);
    await queryRunner.query(
      `ALTER TABLE "grant_program" ADD "status" "public"."grant_program_status_enum" NOT NULL DEFAULT 'OPEN'`,
    );
  }
}
