import { MigrationInterface, QueryRunner } from 'typeorm';

import { StageCode } from '../../workflow/enums/stage-code.enum';
import { WorkflowStepDefinition } from '../../workflow/entities/workflow-step-definition.entity';

export class Migration1746710319087 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.startTransaction();
    try {
      const stepRepo = queryRunner.manager.getRepository(WorkflowStepDefinition);

      stepRepo.update(
        {
          code: StageCode.GC_CLOSED,
        },
        {
          isTerminal: false,
        },
      );

      await queryRunner.query(`TRUNCATE grant_call CASCADE`);

      await queryRunner.commitTransaction();
      console.log('Workflow data seeding finished successfully.');
    } catch (err) {
      console.error('Error seeding workflow data:', err);

      await queryRunner.rollbackTransaction();
      throw err;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.startTransaction();

    try {
      const stepRepo = queryRunner.manager.getRepository(WorkflowStepDefinition);

      stepRepo.update(
        {
          code: StageCode.GC_CLOSED,
        },
        {
          isTerminal: true,
        },
      );

      await queryRunner.commitTransaction();

      console.log('Workflow data seeding reverted successfully.');
    } catch (err) {
      console.error('Error reverting workflow data seeding:', err);

      await queryRunner.rollbackTransaction();
      throw err;
    }
  }
}
