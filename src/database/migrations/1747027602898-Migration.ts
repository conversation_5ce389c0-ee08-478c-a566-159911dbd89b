import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747027602898 implements MigrationInterface {
  name = 'Migration1747027602898';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application_stage" DROP CONSTRAINT "FK_e2a128648f1b4cc74ab9d585c29"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "FK_d3546a9ea3c960fc99b7296d754"`);
    await queryRunner.query(`ALTER TABLE "workflow_state" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."workflow_state_status_enum"`);
    await queryRunner.query(`ALTER TABLE "grant_application_stage" DROP COLUMN "grantCallId"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "assigneeId"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "targetedIndustries"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "startDate"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "endDate"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "minGrantSize"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "maxGrantSize"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "isClosed"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum" RENAME TO "workflow_step_definitions_code_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION', 'GA_APPROVED', 'GA_REJECTED', 'GA_WITHDRAWN')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."workflow_step_definitions_code_enum" USING "code"::"text"::"public"."workflow_step_definitions_code_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum_old"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code") `,
    );
    await queryRunner.query(`DROP TABLE IF EXISTS "grant_call_member"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum_old" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."workflow_step_definitions_code_enum_old" USING "code"::"text"::"public"."workflow_step_definitions_code_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum_old" RENAME TO "workflow_step_definitions_code_enum"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("code", "workflowTemplateId") `,
    );
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "isClosed" boolean DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "maxGrantSize" numeric(12,0)`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "minGrantSize" numeric(12,0)`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "endDate" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "startDate" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "targetedIndustries" text`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "assigneeId" integer`);
    await queryRunner.query(`ALTER TABLE "grant_application_stage" ADD "grantCallId" integer`);
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_state_status_enum" AS ENUM('IN_PROGRESS', 'READY_FOR_NEXT_STEP', 'ACTION_REQUIRED', 'APPROVED', 'REJECTED', 'WITHDRAWN')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_state" ADD "status" "public"."workflow_state_status_enum" NOT NULL DEFAULT 'IN_PROGRESS'`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_d3546a9ea3c960fc99b7296d754" FOREIGN KEY ("assigneeId") REFERENCES "grant_call_member"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application_stage" ADD CONSTRAINT "FK_e2a128648f1b4cc74ab9d585c29" FOREIGN KEY ("grantCallId") REFERENCES "grant_call"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    await queryRunner.query(`
        ALTER TABLE "grant_call_member" 
        ADD CONSTRAINT "FK_grant_call_member_grant_call" 
        FOREIGN KEY ("grant_call_id") REFERENCES "grant_call"("id") 
        ON DELETE NO ACTION ON UPDATE NO ACTION 
        DEFERRABLE INITIALLY DEFERRED;
    `);
  }
}
