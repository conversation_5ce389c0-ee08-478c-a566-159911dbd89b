import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747903583056 implements MigrationInterface {
  name = 'Migration1747903583056';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "companyWebpage"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "categories"`);
    await queryRunner.query(
      `CREATE TYPE "public"."GrantCategory" AS ENUM('On-chain finance', 'Consumer / Loyalty', 'Sustainability', 'Digital Identity', 'Traceability', 'Telco', 'Other')`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD "categories" "public"."GrantCategory" DEFAULT 'Other' NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "categories"`);
    await queryRunner.query(`DROP TYPE "public"."GrantCategory"`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "categories" text`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "companyWebpage" character varying NOT NULL`);
  }
}
