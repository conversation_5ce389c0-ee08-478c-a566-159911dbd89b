import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747725371847 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename the existing enum type
    await queryRunner.query(`
            ALTER TYPE "public"."workflow_step_definitions_code_enum"
            RENAME TO "workflow_step_definitions_code_enum_old"
        `);

    // 2) Read all of the old labels
    const oldLabels: Array<{ label: string }> = await queryRunner.query(`
            SELECT enumlabel AS label
            FROM pg_enum
            WHERE enumtypid = (
                SELECT oid
                FROM pg_type
                WHERE typname = 'workflow_step_definitions_code_enum_old'
            )
            ORDER BY enumsortorder
        `);

    // 3) Assemble a list of labels, adding GA_OPEN if missing
    const labels = oldLabels.map((r) => r.label);
    if (!labels.includes('GA_OPEN')) {
      labels.push('GA_OPEN');
    }
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 4) Create a brand-new enum type with every label
    await queryRunner.query(`
            CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM(${labelsSql})
        `);

    // 5) Rebind the column to the new type (via text cast)
    await queryRunner.query(`
            ALTER TABLE "workflow_step_definitions"
            ALTER COLUMN "code"
            TYPE "public"."workflow_step_definitions_code_enum"
            USING ("code"::text::public.workflow_step_definitions_code_enum)
        `);

    // 6) Drop the old enum type
    await queryRunner.query(`
            DROP TYPE "public"."workflow_step_definitions_code_enum_old"
        `);

    // 7) Seed the new workflow step
    //   a) find the APPLICATION template
    const res: Array<{ id: number }> = await queryRunner.query(`
            SELECT id
            FROM workflow_templates
            WHERE "entityType" = 'APPLICATION'
            LIMIT 1
        `);
    if (!res.length) {
      throw new Error('APPLICATION workflow template not found');
    }
    const templateId = res[0].id;

    //   b) insert the new step
    await queryRunner.query(
      `
                INSERT INTO workflow_step_definitions
                    ("workflowTemplateId", name, code, "sequenceNumber", "transitionType", "isTerminal")
                VALUES
                    ($1, 'Open a Grant Application', 'GA_OPEN', 1, 'MANUAL', false)
            `,
      [templateId],
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 1) remove the seeded step
    await queryRunner.query(`
            DELETE FROM workflow_step_definitions
            WHERE code = 'GA_OPEN'
        `);

    // 2) Rename the current enum so we can rebuild the original
    await queryRunner.query(`
            ALTER TYPE "public"."workflow_step_definitions_code_enum"
            RENAME TO "workflow_step_definitions_code_enum_old"
        `);

    // 3) Pull back all labels from that old‐named type
    const oldLabels: Array<{ label: string }> = await queryRunner.query(`
            SELECT enumlabel AS label
            FROM pg_enum
            WHERE enumtypid = (
                SELECT oid
                FROM pg_type
                WHERE typname = 'workflow_step_definitions_code_enum_old'
            )
            ORDER BY enumsortorder
        `);

    // 4) Drop GA_OPEN from the list
    const labels = oldLabels.map((r) => r.label).filter((l) => l !== 'GA_OPEN');
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 5) Recreate the original enum with only the remaining labels
    await queryRunner.query(`
            CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM(${labelsSql})
        `);

    // 6) Point the column back at the recreated enum
    await queryRunner.query(`
            ALTER TABLE "workflow_step_definitions"
            ALTER COLUMN "code"
            TYPE "public"."workflow_step_definitions_code_enum"
            USING ("code"::text::public.workflow_step_definitions_code_enum)
        `);

    // 7) Finally, drop the intermediate old type
    await queryRunner.query(`
            DROP TYPE "public"."workflow_step_definitions_code_enum_old"
        `);
  }
}
