import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1744823496159 implements MigrationInterface {
  name = 'Migration1744823496159';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "grant_call_stage_settings" ("id" SERIAL NOT NULL, "grantCallId" integer NOT NULL, "workflowStepDefinitionId" integer NOT NULL, "startDate" TIMESTAMP WITH TIME ZONE, "endDate" TIMESTAMP WITH TIME ZONE, "durationSeconds" integer, "stageUrl" text, CONSTRAINT "PK_f430a1089210ec878bba4acfb33" PRIMARY KEY ("id")); COMMENT ON COLUMN "grant_call_stage_settings"."stageUrl" IS 'Optional URL relevant to this specific stage (e.g., form, scheduling)'`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_9ff7f77323ab307aead4dd4303" ON "grant_call_stage_settings" ("grantCallId", "workflowStepDefinitionId") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."grant_distribution_rules_type_enum" AS ENUM('PERCENTAGE', 'FIXED_AMOUNT')`,
    );
    await queryRunner.query(
      `CREATE TABLE "grant_distribution_rules" ("id" SERIAL NOT NULL, "grantCallId" integer NOT NULL, "rank" integer NOT NULL, "type" "public"."grant_distribution_rules_type_enum" NOT NULL, "value" numeric(18,2) NOT NULL, CONSTRAINT "PK_452297c9866c3181b00bfa8d188" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_23e9c68bf1a9e39907f448e15c" ON "grant_distribution_rules" ("grantCallId", "rank") `,
    );
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "categories" text`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "totalGrantAmount" numeric(18,2)`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "startDate" DROP NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."startDate" IS 'DEPRECATED: Use stageSettings'`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."endDate" IS 'DEPRECATED: Use stageSettings'`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "minGrantSize" DROP NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."minGrantSize" IS 'DEPRECATED'`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "maxGrantSize" DROP NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."maxGrantSize" IS 'DEPRECATED'`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "targetedIndustries" DROP NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."targetedIndustries" IS 'DEPRECATED: Use categories'`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "isClosed" DROP NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."isClosed" IS 'DEPRECATED'`);
    await queryRunner.query(
      `ALTER TABLE "grant_call_stage_settings" ADD CONSTRAINT "FK_c73d5fac149089b6121c768977f" FOREIGN KEY ("grantCallId") REFERENCES "grant_call"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call_stage_settings" ADD CONSTRAINT "FK_e7435ad5fc4637ce3f36797879d" FOREIGN KEY ("workflowStepDefinitionId") REFERENCES "workflow_step_definitions"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_distribution_rules" ADD CONSTRAINT "FK_62ee03c8127fa443484d465f8dc" FOREIGN KEY ("grantCallId") REFERENCES "grant_call"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_distribution_rules" DROP CONSTRAINT "FK_62ee03c8127fa443484d465f8dc"`);
    await queryRunner.query(`ALTER TABLE "grant_call_stage_settings" DROP CONSTRAINT "FK_e7435ad5fc4637ce3f36797879d"`);
    await queryRunner.query(`ALTER TABLE "grant_call_stage_settings" DROP CONSTRAINT "FK_c73d5fac149089b6121c768977f"`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."isClosed" IS NULL`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "isClosed" SET NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."targetedIndustries" IS NULL`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "targetedIndustries" SET NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."maxGrantSize" IS NULL`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "maxGrantSize" SET NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."minGrantSize" IS NULL`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "minGrantSize" SET NOT NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."endDate" IS NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "grant_call"."startDate" IS NULL`);
    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "startDate" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "totalGrantAmount"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "categories"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_23e9c68bf1a9e39907f448e15c"`);
    await queryRunner.query(`DROP TABLE "grant_distribution_rules"`);
    await queryRunner.query(`DROP TYPE "public"."grant_distribution_rules_type_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9ff7f77323ab307aead4dd4303"`);
    await queryRunner.query(`DROP TABLE "grant_call_stage_settings"`);
  }
}
