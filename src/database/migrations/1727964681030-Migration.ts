import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1727964681030 implements MigrationInterface {
  name = 'Migration1727964681030';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" DROP COLUMN "grantorPublicProfileSlug"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" ADD "grantorPublicProfileSlug" character varying NOT NULL`);
  }
}
