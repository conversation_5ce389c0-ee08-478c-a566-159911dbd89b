import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1744271085356 implements MigrationInterface {
  name = 'Migration1744271085356';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

    await queryRunner.query(
      `CREATE TYPE "public"."workflow_templates_entitytype_enum" AS ENUM('PROGRAM', 'CALL', 'APPLICATION')`,
    );
    await queryRunner.query(
      `CREATE TABLE "workflow_templates" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(150) NOT NULL, "entityType" "public"."workflow_templates_entitytype_enum" NOT NULL, CONSTRAINT "UQ_69be586c943a0ed2c1f96936ca9" UNIQUE ("name"), CONSTRAINT "PK_de336a1fce23ad3261d49423eae" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_69be586c943a0ed2c1f96936ca" ON "workflow_templates" ("name") `);
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SUBMITTED', 'GA_SCREENING', 'GA_REJECTED_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION', 'GA_APPROVED', 'GA_REJECTED_FINAL', 'GA_WITHDRAWN')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_transitiontype_enum" AS ENUM('MANUAL', 'AUTOMATIC')`,
    );
    await queryRunner.query(
      `CREATE TABLE "workflow_step_definitions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "workflowTemplateId" uuid NOT NULL, "name" character varying(100) NOT NULL, "code" "public"."workflow_step_definitions_code_enum" NOT NULL, "sequenceNumber" integer NOT NULL, "transitionType" "public"."workflow_step_definitions_transitiontype_enum" NOT NULL DEFAULT 'MANUAL', "description" text, "isTerminal" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_6e137e79633dea0ec215ef10c81" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_01b8474eb36b386452ed8d7b9e" ON "workflow_step_definitions" ("workflowTemplateId", "sequenceNumber") `,
    );
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "workflowTemplateId" uuid`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "currentStepDefinitionId" uuid`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "currentStepTransitionedAt" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "currentStepEndsAt" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ADD CONSTRAINT "FK_d73770f05bd6e45f68ac566ea9e" FOREIGN KEY ("workflowTemplateId") REFERENCES "workflow_templates"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "FK_8ce4a99b04496ffc09e4bcb0b99" FOREIGN KEY ("workflowTemplateId") REFERENCES "workflow_templates"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "FK_33c12099a7d5edf2fb6009d2a18" FOREIGN KEY ("currentStepDefinitionId") REFERENCES "workflow_step_definitions"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "FK_33c12099a7d5edf2fb6009d2a18"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "FK_8ce4a99b04496ffc09e4bcb0b99"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP CONSTRAINT "FK_d73770f05bd6e45f68ac566ea9e"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "currentStepEndsAt"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "currentStepTransitionedAt"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "currentStepDefinitionId"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "workflowTemplateId"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_01b8474eb36b386452ed8d7b9e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(`DROP TABLE "workflow_step_definitions"`);
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_transitiontype_enum"`);
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_69be586c943a0ed2c1f96936ca"`);
    await queryRunner.query(`DROP TABLE "workflow_templates"`);
    await queryRunner.query(`DROP TYPE "public"."workflow_templates_entitytype_enum"`);
    await queryRunner.query(`DROP EXTENSION IF EXISTS "uuid-ossp"`);
  }
}
