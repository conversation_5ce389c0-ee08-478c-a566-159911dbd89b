import { MigrationInterface, QueryRunner, Repository } from 'typeorm';

import { StageCode } from '../../workflow/enums/stage-code.enum';
import { StageTransitionType } from '../../workflow/enums/stage-transition-type.enum';
import { WorkflowEntityType } from '../../workflow/enums/workflow-entity-type.enum';
import { WorkflowStepDefinition } from '../../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../../workflow/entities/workflow-template.entity';

interface WorkflowStepSeedData {
  name: string;
  code: StageCode;
  sequenceNumber: number;
  transitionType: StageTransitionType;
  isTerminal: boolean;
  description?: string;
}

const grantProgramWorkflowSteps: WorkflowStepSeedData[] = [
  {
    name: 'Open',
    code: StageCode.GP_OPEN,
    sequenceNumber: 10,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Finalized',
    code: StageCode.GP_FINALIZED,
    sequenceNumber: 100,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: true,
  },
];

const grantCallWorkflowSteps: WorkflowStepSeedData[] = [
  {
    name: 'Closed',
    code: StageCode.GC_CLOSED,
    sequenceNumber: 10,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: true,
  },
  {
    name: 'Open for Applications',
    code: StageCode.GC_OPEN_FOR_APPLICATIONS,
    sequenceNumber: 20,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Screening',
    code: StageCode.GC_SCREENING,
    sequenceNumber: 30,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Community Voting',
    code: StageCode.GC_COMMUNITY_VOTING,
    sequenceNumber: 40,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Onboarding',
    code: StageCode.GC_ONBOARDING,
    sequenceNumber: 50,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Final Community Voting',
    code: StageCode.GC_FINAL_COMMUNITY_VOTING,
    sequenceNumber: 60,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Finalized',
    code: StageCode.GC_FINALIZED,
    sequenceNumber: 100,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: true,
  },
];

const grantApplicationWorkflowSteps: WorkflowStepSeedData[] = [
  {
    name: 'Screening',
    code: StageCode.GA_SCREENING,
    sequenceNumber: 10,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Qualification',
    code: StageCode.GA_QUALIFICATION,
    sequenceNumber: 20,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Interview',
    code: StageCode.GA_INTERVIEW,
    sequenceNumber: 30,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Due Diligence',
    code: StageCode.GA_DUE_DILIGENCE,
    sequenceNumber: 40,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Town Hall',
    code: StageCode.GA_TOWN_HALL,
    sequenceNumber: 50,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Final Qualification',
    code: StageCode.GA_FINAL_QUALIFICATION,
    sequenceNumber: 60,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
];

const grantProgramTemplateName = 'Default Grant Program Workflow';
const grantCallTemplateName = 'Default Grant Call Workflow';
const grantApplicationTemplateName = 'Default Grant Application Workflow';

export class SeedWorkflowData1744358909175 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.startTransaction();
    try {
      const templateRepo = queryRunner.manager.getRepository(WorkflowTemplate);
      const stepRepo = queryRunner.manager.getRepository(WorkflowStepDefinition);

      await this.seedWorkflow(
        templateRepo,
        stepRepo,
        grantProgramTemplateName,
        WorkflowEntityType.PROGRAM,
        grantProgramWorkflowSteps,
      );

      await this.seedWorkflow(
        templateRepo,
        stepRepo,
        grantCallTemplateName,
        WorkflowEntityType.CALL,
        grantCallWorkflowSteps,
      );

      await this.seedWorkflow(
        templateRepo,
        stepRepo,
        grantApplicationTemplateName,
        WorkflowEntityType.APPLICATION,
        grantApplicationWorkflowSteps,
      );

      await queryRunner.commitTransaction();
      console.log('Workflow data seeding finished successfully.');
    } catch (err) {
      console.error('Error seeding workflow data:', err);

      await queryRunner.rollbackTransaction();
      throw err;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.startTransaction();

    try {
      const templateRepo = queryRunner.manager.getRepository(WorkflowTemplate);
      const stepRepo = queryRunner.manager.getRepository(WorkflowStepDefinition);

      const templateNamesToRemove = [grantProgramTemplateName, grantCallTemplateName, grantApplicationTemplateName];

      const templates = await templateRepo
        .createQueryBuilder('template')
        .where('template.name IN (:...names)', { names: templateNamesToRemove })
        .getMany();

      if (templates.length > 0) {
        const templateIdsToRemove = templates.map((t) => t.id);

        await stepRepo
          .createQueryBuilder()
          .delete()
          .where('workflowTemplateId IN (:...ids)', {
            ids: templateIdsToRemove,
          })
          .execute();

        await templateRepo
          .createQueryBuilder()
          .delete()
          .where('id IN (:...ids)', { ids: templateIdsToRemove })
          .execute();
      } else {
        console.log(' -> No workflow templates matching names to remove were found.');
      }

      await queryRunner.commitTransaction();

      console.log('Workflow data seeding reverted successfully.');
    } catch (err) {
      console.error('Error reverting workflow data seeding:', err);

      await queryRunner.rollbackTransaction();
      throw err;
    }
  }

  private async seedWorkflow(
    templateRepo: Repository<WorkflowTemplate>,
    stepRepo: Repository<WorkflowStepDefinition>,
    templateName: string,
    entityType: WorkflowEntityType,
    stepsData: WorkflowStepSeedData[],
  ): Promise<void> {
    let template = await templateRepo.findOneBy({ name: templateName });

    if (!template) {
      template = templateRepo.create({
        name: templateName,
        entityType: entityType,
      });
      template = await templateRepo.save(template);

      const stepsToCreate = stepsData.map((stepData) =>
        stepRepo.create({
          ...stepData,
          workflowTemplateId: template.id,
          workflowTemplate: template,
        }),
      );

      await stepRepo.save(stepsToCreate);
      console.log(`   -> Added ${stepsToCreate.length} steps for ${templateName}.`);
    } else {
      console.log(` -> WorkflowTemplate "${templateName}" already exists. Skipping creation.`);
    }
  }
}
