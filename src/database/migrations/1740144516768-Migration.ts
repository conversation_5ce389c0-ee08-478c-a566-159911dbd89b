import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1740144516768 implements MigrationInterface {
  name = 'Migration1740144516768';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "phoneNumber" character varying`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "UQ_f2578043e491921209f5dadd080" UNIQUE ("phoneNumber")`,
    );
    await queryRunner.query(`ALTER TABLE "user" ADD "isPhoneVerified" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "user" ADD "phoneOtp" bigint`);
    await queryRunner.query(`ALTER TABLE "user" ADD "phoneOtpExpiresAt" TIMESTAMP WITH TIME ZONE DEFAULT now()`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "phoneOtpExpiresAt"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "phoneOtp"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isPhoneVerified"`);
    await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "UQ_f2578043e491921209f5dadd080"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "phoneNumber"`);
  }
}
