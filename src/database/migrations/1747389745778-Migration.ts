import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747389745778 implements MigrationInterface {
  name = 'Migration1747389745778';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "industry"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."grant_application_status_enum"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "statusChangedOn"`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "categories" text`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "createdById" integer`);

    await queryRunner.query(`
      UPDATE "grant_application" "ga"
      SET "createdById" = (
          SELECT "gam"."userId"
          FROM "grant_application_member" "gam"
          WHERE "gam"."grantApplicationId" = "ga"."id" AND "gam"."isCreator" = TRUE
          LIMIT 1
      )
      WHERE "ga"."createdById" IS NULL;
    `);

    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "createdById" SET NOT NULL`);

    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_17a0cb26cc9be00d6000f9c7b18" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );

    await queryRunner.query(`DROP TABLE IF EXISTS "grant_application_stage_log"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "grant_application_member"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "grant_application_stage"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE "grant_application_stage" (
            "id" SERIAL NOT NULL,
            "title" character varying NOT NULL,
            "subtitle" character varying NOT NULL,
            "description" character varying NOT NULL,
            "expectedDuration" integer NOT NULL,
            "position" integer NOT NULL,
            "actionDescription" character varying,
            "actionButtonText" character varying,
            "actionLink" character varying,
            CONSTRAINT "PK_some_unique_name_for_gas_pk" PRIMARY KEY ("id") -- Use actual PK constraint name if known
        )
    `);

    await queryRunner.query(`
        CREATE TABLE "grant_application_member" (
            "id" SERIAL NOT NULL,
            "isCreator" boolean NOT NULL,
            "userId" integer,
            "grantApplicationId" integer,
            CONSTRAINT "PK_some_unique_name_for_gam_pk" PRIMARY KEY ("id")
        )
    `);
    await queryRunner.query(`
        ALTER TABLE "grant_application_member"
        ADD CONSTRAINT "FK_gam_userId_to_user" FOREIGN KEY ("userId")
        REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
        ALTER TABLE "grant_application_member"
        ADD CONSTRAINT "FK_gam_grantApplicationId_to_ga" FOREIGN KEY ("grantApplicationId")
        REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
        ALTER TABLE "grant_application_stage_log"
        ADD CONSTRAINT "FK_gasl_grantApplicationId_to_ga" FOREIGN KEY ("grantApplicationId")
        REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
        ALTER TABLE "grant_application_stage_log"
        ADD CONSTRAINT "FK_gasl_stageId_to_gas" FOREIGN KEY ("stageId")
        REFERENCES "grant_application_stage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "FK_17a0cb26cc9be00d6000f9c7b18"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "updatedAt"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "createdById"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "categories"`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "statusChangedOn" TIMESTAMP NOT NULL DEFAULT now()`);

    await queryRunner.query(`
      DO $$
      BEGIN
          IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'grant_application_status_enum' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
              CREATE TYPE "public"."grant_application_status_enum" AS ENUM('OPEN', 'WITHDRAWN', 'REJECTED', 'APPROVED');
          END IF;
      END $$;
    `);

    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD "status" "public"."grant_application_status_enum" NOT NULL DEFAULT 'OPEN'`,
    );
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "industry" character varying NOT NULL`);
  }
}
