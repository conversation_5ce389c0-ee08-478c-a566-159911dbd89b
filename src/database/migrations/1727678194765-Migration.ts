import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1727678194765 implements MigrationInterface {
  name = 'Migration1727678194765';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" ADD "grantorDescription" character varying`);
    await queryRunner.query(
      `UPDATE "grant_program" SET "grantorDescription" = "grantorPublicProfileName" WHERE "grantorDescription" IS NULL`,
    );
    await queryRunner.query(`ALTER TABLE "grant_program" ALTER COLUMN "grantorDescription" SET NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" DROP COLUMN "grantorDescription"`);
  }
}
