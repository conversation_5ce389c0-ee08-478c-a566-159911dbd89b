import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1744215136748 implements MigrationInterface {
  name = 'Migration1744215136748';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "FK_27be2cab62274f6876ad6a31641"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "FK_0b4cd4043d6b126d8feaff3ce39"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_304dfe0177e97c7482c785f442"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "user_id"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "application_id"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "voteType"`);
    await queryRunner.query(`DROP TYPE "public"."votes_votetype_enum"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "transactionHash"`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "inFavorVotes" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "againstVotes" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "walletsInFavor" character varying array NOT NULL`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "walletsAgainst" character varying array NOT NULL`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "grantApplicationId" integer`);
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "UQ_77a56e5a66022b5b611e614f17c" UNIQUE ("grantApplicationId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "FK_77a56e5a66022b5b611e614f17c" FOREIGN KEY ("grantApplicationId") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "FK_77a56e5a66022b5b611e614f17c"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "UQ_77a56e5a66022b5b611e614f17c"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "grantApplicationId"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "walletsAgainst"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "walletsInFavor"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "againstVotes"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "inFavorVotes"`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "transactionHash" character varying(255)`);
    await queryRunner.query(`CREATE TYPE "public"."votes_votetype_enum" AS ENUM('in_favor', 'against')`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "voteType" "public"."votes_votetype_enum"`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "application_id" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "user_id" integer NOT NULL`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_304dfe0177e97c7482c785f442" ON "votes" ("user_id", "application_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "FK_0b4cd4043d6b126d8feaff3ce39" FOREIGN KEY ("application_id") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "FK_27be2cab62274f6876ad6a31641" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
