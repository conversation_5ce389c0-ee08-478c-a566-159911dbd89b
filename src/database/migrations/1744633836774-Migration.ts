import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1744633836774 implements MigrationInterface {
  name = 'Migration1744633836774';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "votes" ALTER COLUMN "inFavorVotes" SET DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "votes" ALTER COLUMN "againstVotes" SET DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "votes" ALTER COLUMN "againstVotes" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "votes" ALTER COLUMN "inFavorVotes" DROP DEFAULT`);
  }
}
