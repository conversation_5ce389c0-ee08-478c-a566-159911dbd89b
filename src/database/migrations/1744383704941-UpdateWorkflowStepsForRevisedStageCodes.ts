import { MigrationInterface, QueryRunner } from 'typeorm';

import { StageCode } from '../../workflow/enums/stage-code.enum';
import { StageTransitionType } from '../../workflow/enums/stage-transition-type.enum';
import { WorkflowStepDefinition } from '../../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../../workflow/entities/workflow-template.entity';

const APPLICATION_TEMPLATE_NAME = 'Default Grant Application Workflow';

const OLD_GA_SUBMITTED_DETAILS = {
  name: 'Submitted',
  code: 'GA_SUBMITTED',
  sequenceNumber: 10,
  transitionType: StageTransitionType.AUTOMATIC,
  isTerminal: false,
  description: 'Initial submission state.',
};
const OLD_GA_REJECTED_SCREENING_DETAILS = {
  name: 'Rejected (Screening)',
  code: 'GA_REJECTED_SCREENING',
  sequenceNumber: 25,
  transitionType: StageTransitionType.MANUAL,
  isTerminal: true,
};

const OLD_GA_REJECTED_FINAL_DETAILS = {
  name: 'Rejected (Final)',
  code: 'GA_REJECTED_FINAL',
  sequenceNumber: 110,
  transitionType: StageTransitionType.MANUAL,
  isTerminal: true,
};

export class UpdateWorkflowStepsForRevisedStageCodes1744383704941 implements MigrationInterface {
  name = 'UpdateWorkflowStepsForRevisedStageCodes1744383704941';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "workflow_step_definitions" SET "transitionType" = 'MANUAL' WHERE "transitionType" = 'AUTOMATIC'`,
    );

    await queryRunner.query(`DROP INDEX IF EXISTS "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE TEXT`);

    await queryRunner.query(
      `UPDATE "workflow_step_definitions" SET "code" = 'GA_REJECTED', "name" = 'Rejected' WHERE "code" = 'GA_REJECTED_FINAL'`,
    );

    await queryRunner.query(
      `DELETE FROM "workflow_step_definitions" WHERE "code" = 'GA_REJECTED_SCREENING' RETURNING id`,
    );

    await queryRunner.query(`DELETE FROM "workflow_step_definitions" WHERE "code" = 'GA_SUBMITTED' RETURNING id`);

    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum" RENAME TO "workflow_step_definitions_code_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION', 'GA_APPROVED', 'GA_REJECTED', 'GA_WITHDRAWN')`,
    );

    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."workflow_step_definitions_code_enum" USING "code"::"public"."workflow_step_definitions_code_enum"`,
    );

    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum_old"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const templateRepo = queryRunner.manager.getRepository(WorkflowTemplate);
    const stepRepo = queryRunner.manager.getRepository(WorkflowStepDefinition);

    const appTemplate = await templateRepo.findOneBy({
      name: APPLICATION_TEMPLATE_NAME,
    });

    await queryRunner.query(`DROP INDEX IF EXISTS "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);

    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum_old" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SUBMITTED', 'GA_SCREENING', 'GA_REJECTED_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION', 'GA_APPROVED', 'GA_REJECTED_FINAL', 'GA_WITHDRAWN')`,
    );

    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE TEXT`);

    await queryRunner.query(
      `UPDATE "workflow_step_definitions" SET "code" = $1, "name" = $2 WHERE "code" = 'GA_REJECTED'`,
      [OLD_GA_REJECTED_FINAL_DETAILS.code, OLD_GA_REJECTED_FINAL_DETAILS.name],
    );

    await stepRepo.save(
      [
        stepRepo.create({
          workflowTemplateId: appTemplate.id,
          workflowTemplate: appTemplate,
          name: OLD_GA_REJECTED_SCREENING_DETAILS.name,
          code: OLD_GA_REJECTED_SCREENING_DETAILS.code as StageCode,
          sequenceNumber: OLD_GA_REJECTED_SCREENING_DETAILS.sequenceNumber,
          transitionType: OLD_GA_REJECTED_SCREENING_DETAILS.transitionType,
          isTerminal: OLD_GA_REJECTED_SCREENING_DETAILS.isTerminal,
        }),
        stepRepo.create({
          workflowTemplateId: appTemplate.id,
          workflowTemplate: appTemplate,
          name: OLD_GA_SUBMITTED_DETAILS.name,
          code: OLD_GA_SUBMITTED_DETAILS.code as StageCode,
          sequenceNumber: OLD_GA_SUBMITTED_DETAILS.sequenceNumber,
          transitionType: OLD_GA_SUBMITTED_DETAILS.transitionType,
          isTerminal: OLD_GA_SUBMITTED_DETAILS.isTerminal,
          description: OLD_GA_SUBMITTED_DETAILS.description,
        }),
      ],
      { transaction: false },
    );

    const codesToRevertToAuto = [
      'GC_CLOSED',
      'GC_OPEN_FOR_APPLICATIONS',
      'GC_COMMUNITY_VOTING',
      'GC_FINAL_COMMUNITY_VOTING',
      'GA_SUBMITTED',
      'GA_SCREENING',
      'GA_QUALIFICATION',
    ];
    await queryRunner.query(
      `UPDATE "workflow_step_definitions" SET "transitionType" = 'AUTOMATIC' WHERE "code" = ANY($1::text[])`,
      [codesToRevertToAuto],
    );
    const codesToRevertToManual = [
      'GP_OPEN',
      'GP_FINALIZED',
      'GC_SCREENING',
      'GC_ONBOARDING',
      'GC_FINALIZED',
      'GA_REJECTED_SCREENING',
      'GA_INTERVIEW',
      'GA_DUE_DILIGENCE',
      'GA_TOWN_HALL',
      'GA_FINAL_QUALIFICATION',
      'GA_APPROVED',
      'GA_REJECTED_FINAL',
      'GA_WITHDRAWN',
    ];
    await queryRunner.query(
      `UPDATE "workflow_step_definitions" SET "transitionType" = 'MANUAL' WHERE "code" = ANY($1::text[])`,
      [codesToRevertToManual],
    );

    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."workflow_step_definitions_code_enum_old" USING "code"::"public"."workflow_step_definitions_code_enum_old"`,
    );

    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum"`);

    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum_old" RENAME TO "workflow_step_definitions_code_enum"`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("code", "workflowTemplateId")`,
    );
  }
}
