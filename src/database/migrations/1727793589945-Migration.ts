import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1727793589945 implements MigrationInterface {
  name = 'Migration1727793589945';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" ADD "grantorWebsite" character varying`);
    await queryRunner.query(`UPDATE "grant_program" SET "grantorWebsite" = 'https://hashgraph.swiss'`);
    await queryRunner.query(`ALTER TABLE "grant_program" ALTER COLUMN "grantorWebsite" SET NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" DROP COLUMN "grantorWebsite"`);
  }
}
