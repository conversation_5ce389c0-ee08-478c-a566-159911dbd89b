import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1742931680299 implements MigrationInterface {
  name = 'Migration1742931680299';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."votes_votetype_enum" AS ENUM('in_favor', 'against')`);

    await queryRunner.query(
      `CREATE TABLE "votes" ("id" SERIAL NOT NULL, "user_id" integer NOT NULL, "application_id" integer NOT NULL, "voteType" "public"."votes_votetype_enum", "transactionHash" character varying(255), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_f3d9fd4a0af865152c3f59db8ff" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_304dfe0177e97c7482c785f442" ON "votes" ("user_id", "application_id") `,
    );
    await queryRunner.query(`CREATE TYPE "public"."vote_history_votetype_enum" AS ENUM('in_favor', 'against')`);
    await queryRunner.query(
      `CREATE TYPE "public"."vote_history_eventtype_enum" AS ENUM('vote_cast', 'vote_modified', 'vote_removed')`,
    );
    await queryRunner.query(`CREATE TYPE "public"."vote_history_previousvotetype_enum" AS ENUM('in_favor', 'against')`);
    await queryRunner.query(
      `CREATE TABLE "vote_history" ("id" SERIAL NOT NULL, "vote_id" integer, "user_id" integer NOT NULL, "application_id" integer NOT NULL, "voteType" "public"."vote_history_votetype_enum", "eventType" "public"."vote_history_eventtype_enum" NOT NULL, "transactionHash" character varying(255) NOT NULL, "eventTimestamp" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "previousVoteType" "public"."vote_history_previousvotetype_enum", "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_e17854d4623d6d69edbe4b25a96" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "FK_27be2cab62274f6876ad6a31641" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "FK_0b4cd4043d6b126d8feaff3ce39" FOREIGN KEY ("application_id") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vote_history" ADD CONSTRAINT "FK_7a8abce5761d340916217c7849e" FOREIGN KEY ("vote_id") REFERENCES "votes"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vote_history" ADD CONSTRAINT "FK_ef764ea4efedb0f59eab2199f25" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vote_history" ADD CONSTRAINT "FK_f90fa188c087aee0cb8756791c0" FOREIGN KEY ("application_id") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vote_history" DROP CONSTRAINT "FK_f90fa188c087aee0cb8756791c0"`);
    await queryRunner.query(`ALTER TABLE "vote_history" DROP CONSTRAINT "FK_ef764ea4efedb0f59eab2199f25"`);
    await queryRunner.query(`ALTER TABLE "vote_history" DROP CONSTRAINT "FK_7a8abce5761d340916217c7849e"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "FK_0b4cd4043d6b126d8feaff3ce39"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "FK_27be2cab62274f6876ad6a31641"`);
    await queryRunner.query(`DROP TABLE "vote_history"`);
    await queryRunner.query(`DROP TYPE "public"."vote_history_previousvotetype_enum"`);
    await queryRunner.query(`DROP TYPE "public"."vote_history_eventtype_enum"`);
    await queryRunner.query(`DROP TYPE "public"."vote_history_votetype_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_304dfe0177e97c7482c785f442"`);
    await queryRunner.query(`DROP TABLE "votes"`);
    await queryRunner.query(`DROP TYPE "public"."votes_votetype_enum"`);
  }
}
