import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1739371877731 implements MigrationInterface {
  name = 'Migration1739371877731';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_notification_preferences_notificationtype_enum" AS ENUM('application-approved', 'application-moved', 'application-rejected', 'grant application assignee-changed', 'grant-call-invitation', 'new-grant-application')`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_notification_preferences" ("id" SERIAL NOT NULL, "notificationType" "public"."user_notification_preferences_notificationtype_enum" NOT NULL, "enabled" boolean NOT NULL DEFAULT true, "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "userId" integer, CONSTRAINT "PK_2b30dfc697b16f75a55be54d464" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "FK_fc1bb12707451f64b0ebb377fa9" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" DROP CONSTRAINT "FK_fc1bb12707451f64b0ebb377fa9"`,
    );
    await queryRunner.query(`DROP TABLE "user_notification_preferences"`);
    await queryRunner.query(`DROP TYPE "public"."user_notification_preferences_notificationtype_enum"`);
  }
}
