import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1739529825621 implements MigrationInterface {
  name = 'Migration1739529825621';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" DROP CONSTRAINT "FK_fc1bb12707451f64b0ebb377fa9"`,
    );
    await queryRunner.query(`ALTER TABLE "user_notification_preferences" ALTER COLUMN "userId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_notification_preferences_notificationtype_enum" RENAME TO "user_notification_preferences_notificationtype_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_notification_preferences_notificationtype_enum" AS ENUM('application-approved', 'application-moved', 'application-rejected', 'grant-application-assignee-changed', 'grant-call-invitation', 'new-grant-application')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" ALTER COLUMN "notificationType" TYPE "public"."user_notification_preferences_notificationtype_enum" USING "notificationType"::"text"::"public"."user_notification_preferences_notificationtype_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_notification_preferences_notificationtype_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "FK_fc1bb12707451f64b0ebb377fa9" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" DROP CONSTRAINT "FK_fc1bb12707451f64b0ebb377fa9"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_notification_preferences_notificationtype_enum_old" AS ENUM('application-approved', 'application-moved', 'application-rejected', 'grant application assignee-changed', 'grant-call-invitation', 'new-grant-application')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" ALTER COLUMN "notificationType" TYPE "public"."user_notification_preferences_notificationtype_enum_old" USING "notificationType"::"text"::"public"."user_notification_preferences_notificationtype_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_notification_preferences_notificationtype_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_notification_preferences_notificationtype_enum_old" RENAME TO "user_notification_preferences_notificationtype_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "user_notification_preferences" ALTER COLUMN "userId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "FK_fc1bb12707451f64b0ebb377fa9" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
