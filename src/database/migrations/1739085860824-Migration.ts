import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1739085860824 implements MigrationInterface {
  name = 'Migration1739085860824';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "linkedAccounts" jsonb DEFAULT '{}'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "linkedAccounts"`);
  }
}
