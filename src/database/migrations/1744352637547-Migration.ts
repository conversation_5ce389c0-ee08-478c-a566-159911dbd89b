import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1744352637547 implements MigrationInterface {
  name = 'Migration1744352637547';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "FK_33c12099a7d5edf2fb6009d2a18"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "FK_8ce4a99b04496ffc09e4bcb0b99"`);
    await queryRunner.query(
      `CREATE TABLE "workflow_state" ("id" SERIAL NOT NULL, "workflowTemplateId" integer, "currentStepDefinitionId" integer, "currentStepTransitionedAt" TIMESTAMP WITH TIME ZONE, "currentStepEndsAt" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_78ea932fd6e23282848898c5710" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "workflowTemplateId"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "currentStepDefinitionId"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "currentStepTransitionedAt"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "currentStepEndsAt"`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "workflowStateId" integer`);
    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "UQ_6276b335096d3987251b408d400" UNIQUE ("workflowStateId")`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP CONSTRAINT "FK_d73770f05bd6e45f68ac566ea9e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_01b8474eb36b386452ed8d7b9e"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP CONSTRAINT "PK_6e137e79633dea0ec215ef10c81"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP COLUMN "id"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ADD "id" SERIAL NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ADD CONSTRAINT "PK_6e137e79633dea0ec215ef10c81" PRIMARY KEY ("id")`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP COLUMN "workflowTemplateId"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ADD "workflowTemplateId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "workflow_templates" DROP CONSTRAINT "PK_de336a1fce23ad3261d49423eae"`);
    await queryRunner.query(`ALTER TABLE "workflow_templates" DROP COLUMN "id"`);
    await queryRunner.query(`ALTER TABLE "workflow_templates" ADD "id" SERIAL NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "workflow_templates" ADD CONSTRAINT "PK_de336a1fce23ad3261d49423eae" PRIMARY KEY ("id")`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_01b8474eb36b386452ed8d7b9e" ON "workflow_step_definitions" ("workflowTemplateId", "sequenceNumber") `,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ADD CONSTRAINT "FK_d73770f05bd6e45f68ac566ea9e" FOREIGN KEY ("workflowTemplateId") REFERENCES "workflow_templates"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_state" ADD CONSTRAINT "FK_503944262afd4ec636932b5bff6" FOREIGN KEY ("workflowTemplateId") REFERENCES "workflow_templates"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_state" ADD CONSTRAINT "FK_8a45466b004b56ed7843caa35c3" FOREIGN KEY ("currentStepDefinitionId") REFERENCES "workflow_step_definitions"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "FK_6276b335096d3987251b408d400" FOREIGN KEY ("workflowStateId") REFERENCES "workflow_state"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "FK_6276b335096d3987251b408d400"`);
    await queryRunner.query(`ALTER TABLE "workflow_state" DROP CONSTRAINT "FK_8a45466b004b56ed7843caa35c3"`);
    await queryRunner.query(`ALTER TABLE "workflow_state" DROP CONSTRAINT "FK_503944262afd4ec636932b5bff6"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP CONSTRAINT "FK_d73770f05bd6e45f68ac566ea9e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_01b8474eb36b386452ed8d7b9e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(`ALTER TABLE "workflow_templates" DROP CONSTRAINT "PK_de336a1fce23ad3261d49423eae"`);
    await queryRunner.query(`ALTER TABLE "workflow_templates" DROP COLUMN "id"`);
    await queryRunner.query(`ALTER TABLE "workflow_templates" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`);
    await queryRunner.query(
      `ALTER TABLE "workflow_templates" ADD CONSTRAINT "PK_de336a1fce23ad3261d49423eae" PRIMARY KEY ("id")`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP COLUMN "workflowTemplateId"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ADD "workflowTemplateId" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP CONSTRAINT "PK_6e137e79633dea0ec215ef10c81"`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" DROP COLUMN "id"`);
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ADD CONSTRAINT "PK_6e137e79633dea0ec215ef10c81" PRIMARY KEY ("id")`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_01b8474eb36b386452ed8d7b9e" ON "workflow_step_definitions" ("workflowTemplateId", "sequenceNumber") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code") `,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ADD CONSTRAINT "FK_d73770f05bd6e45f68ac566ea9e" FOREIGN KEY ("workflowTemplateId") REFERENCES "workflow_templates"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "UQ_6276b335096d3987251b408d400"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "workflowStateId"`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "currentStepEndsAt" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "currentStepTransitionedAt" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "currentStepDefinitionId" uuid`);
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "workflowTemplateId" uuid`);
    await queryRunner.query(`DROP TABLE "workflow_state"`);
    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "FK_8ce4a99b04496ffc09e4bcb0b99" FOREIGN KEY ("workflowTemplateId") REFERENCES "workflow_templates"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "FK_33c12099a7d5edf2fb6009d2a18" FOREIGN KEY ("currentStepDefinitionId") REFERENCES "workflow_step_definitions"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }
}
