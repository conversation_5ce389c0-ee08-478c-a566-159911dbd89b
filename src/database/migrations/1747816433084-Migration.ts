import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747816433084 implements MigrationInterface {
  name = 'Migration1747816433084';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_templates_entitytype_enum" RENAME TO "workflow_templates_entitytype_enum_old"`,
    );
    await queryRunner.query(`CREATE TYPE "public"."WorkflowEntityType" AS ENUM('PROGRAM', 'CALL', 'APPLICATION')`);
    await queryRunner.query(
      `ALTER TABLE "workflow_templates" ALTER COLUMN "entityType" TYPE "public"."WorkflowEntityType" USING "entityType"::"text"::"public"."WorkflowEntityType"`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_templates_entitytype_enum_old"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum" RENAME TO "workflow_step_definitions_code_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."StageCode" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."StageCode" USING "code"::"text"::"public"."StageCode"`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_transitiontype_enum" RENAME TO "workflow_step_definitions_transitiontype_enum_old"`,
    );
    await queryRunner.query(`CREATE TYPE "public"."StageTransitionType" AS ENUM('MANUAL', 'AUTOMATIC')`);
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" TYPE "public"."StageTransitionType" USING "transitionType"::"text"::"public"."StageTransitionType"`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" SET DEFAULT 'MANUAL'`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_transitiontype_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."grant_distribution_rules_type_enum" RENAME TO "grant_distribution_rules_type_enum_old"`,
    );
    await queryRunner.query(`CREATE TYPE "public"."DistributionType" AS ENUM('PERCENTAGE', 'FIXED_AMOUNT')`);
    await queryRunner.query(
      `ALTER TABLE "grant_distribution_rules" ALTER COLUMN "type" TYPE "public"."DistributionType" USING "type"::"text"::"public"."DistributionType"`,
    );
    await queryRunner.query(`DROP TYPE "public"."grant_distribution_rules_type_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_state_status_enum" RENAME TO "workflow_state_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."WorkflowStatus" AS ENUM('IN_PROGRESS', 'READY_FOR_NEXT_STEP', 'ACTION_REQUIRED', 'APPROVED', 'REJECTED', 'WITHDRAWN')`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_state" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "workflow_state" ALTER COLUMN "status" TYPE "public"."WorkflowStatus" USING "status"::"text"::"public"."WorkflowStatus"`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_state" ALTER COLUMN "status" SET DEFAULT 'IN_PROGRESS'`);
    await queryRunner.query(`DROP TYPE "public"."workflow_state_status_enum_old"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5"`);

    const grantAppStatusEnumExistsResult = await queryRunner.query(
      `SELECT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'grant_application_status_enum' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public'))`,
    );
    const grantAppStatusEnumExists = grantAppStatusEnumExistsResult[0].exists;

    if (grantAppStatusEnumExists) {
      await queryRunner.query(
        `ALTER TYPE "public"."grant_application_status_enum" RENAME TO "grant_application_status_enum_old"`,
      );
      await queryRunner.query(
        `CREATE TYPE "public"."ApplicationStatus" AS ENUM('OPEN', 'WITHDRAWN', 'REJECTED', 'APPROVED')`,
      );
      await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "status" DROP DEFAULT`);
      await queryRunner.query(
        `ALTER TABLE "grant_application" ALTER COLUMN "status" TYPE "public"."ApplicationStatus" USING "status"::"text"::"public"."ApplicationStatus"`,
      );
      await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "status" SET DEFAULT 'OPEN'`);

      await queryRunner.query(`DROP TYPE "public"."grant_application_status_enum_old"`);
    }

    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "actionTopicId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "workflowStateId" SET NOT NULL`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code") `,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5" FOREIGN KEY ("workflowStateId") REFERENCES "workflow_state"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "workflowStateId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "actionTopicId" DROP NOT NULL`);

    const grantAppStatusEnumExistsResult = await queryRunner.query(
      `SELECT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'grant_application_status_enum_old' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public'))`,
    );

    const grantAppStatusEnumExists = grantAppStatusEnumExistsResult[0].exists;

    if (grantAppStatusEnumExists) {
      await queryRunner.query(
        `CREATE TYPE "public"."grant_application_status_enum_old" AS ENUM('OPEN', 'WITHDRAWN', 'REJECTED', 'APPROVED')`,
      );
      await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "status" DROP DEFAULT`);
      await queryRunner.query(
        `ALTER TABLE "grant_application" ALTER COLUMN "status" TYPE "public"."grant_application_status_enum_old" USING "status"::"text"::"public"."grant_application_status_enum_old"`,
      );
      await queryRunner.query(`ALTER TABLE "grant_application" ALTER COLUMN "status" SET DEFAULT 'OPEN'`);
      await queryRunner.query(`DROP TYPE "public"."ApplicationStatus"`);
      await queryRunner.query(
        `ALTER TYPE "public"."grant_application_status_enum_old" RENAME TO "grant_application_status_enum"`,
      );
    }

    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5" FOREIGN KEY ("workflowStateId") REFERENCES "workflow_state"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_state_status_enum_old" AS ENUM('IN_PROGRESS', 'READY_FOR_NEXT_STEP', 'ACTION_REQUIRED', 'APPROVED', 'REJECTED', 'WITHDRAWN')`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_state" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "workflow_state" ALTER COLUMN "status" TYPE "public"."workflow_state_status_enum_old" USING "status"::"text"::"public"."workflow_state_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_state" ALTER COLUMN "status" SET DEFAULT 'IN_PROGRESS'`);
    await queryRunner.query(`DROP TYPE "public"."WorkflowStatus"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_state_status_enum_old" RENAME TO "workflow_state_status_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."grant_distribution_rules_type_enum_old" AS ENUM('PERCENTAGE', 'FIXED_AMOUNT')`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_distribution_rules" ALTER COLUMN "type" TYPE "public"."grant_distribution_rules_type_enum_old" USING "type"::"text"::"public"."grant_distribution_rules_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."DistributionType"`);
    await queryRunner.query(
      `ALTER TYPE "public"."grant_distribution_rules_type_enum_old" RENAME TO "grant_distribution_rules_type_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_transitiontype_enum_old" AS ENUM('MANUAL', 'AUTOMATIC')`,
    );
    await queryRunner.query(`ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" TYPE "public"."workflow_step_definitions_transitiontype_enum_old" USING "transitionType"::"text"::"public"."workflow_step_definitions_transitiontype_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "transitionType" SET DEFAULT 'MANUAL'`,
    );
    await queryRunner.query(`DROP TYPE "public"."StageTransitionType"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_transitiontype_enum_old" RENAME TO "workflow_step_definitions_transitiontype_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum_old" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."workflow_step_definitions_code_enum_old" USING "code"::"text"::"public"."workflow_step_definitions_code_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."StageCode"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum_old" RENAME TO "workflow_step_definitions_code_enum"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("code", "workflowTemplateId") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_templates_entitytype_enum_old" AS ENUM('PROGRAM', 'CALL', 'APPLICATION')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_templates" ALTER COLUMN "entityType" TYPE "public"."workflow_templates_entitytype_enum_old" USING "entityType"::"text"::"public"."workflow_templates_entitytype_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."WorkflowEntityType"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_templates_entitytype_enum_old" RENAME TO "workflow_templates_entitytype_enum"`,
    );
  }
}
