import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747141605770 implements MigrationInterface {
  name = 'Migration1747141605770';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_state_status_enum" AS ENUM('IN_PROGRESS', 'READY_FOR_NEXT_STEP', 'ACTION_REQUIRED', 'APPROVED', 'REJECTED', 'WITHDRAWN')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_state" ADD "status" "public"."workflow_state_status_enum" NOT NULL DEFAULT 'IN_PROGRESS'`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum" RENAME TO "workflow_step_definitions_code_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."workflow_step_definitions_code_enum" USING "code"::"text"::"public"."workflow_step_definitions_code_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum_old"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("workflowTemplateId", "code") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_e6f73eaa19ceafbff74c0cf469"`);
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_step_definitions_code_enum_old" AS ENUM('GP_OPEN', 'GP_FINALIZED', 'GC_CLOSED', 'GC_OPEN_FOR_APPLICATIONS', 'GC_SCREENING', 'GC_COMMUNITY_VOTING', 'GC_ONBOARDING', 'GC_FINAL_COMMUNITY_VOTING', 'GC_FINALIZED', 'GA_SCREENING', 'GA_QUALIFICATION', 'GA_INTERVIEW', 'GA_DUE_DILIGENCE', 'GA_TOWN_HALL', 'GA_FINAL_QUALIFICATION', 'GA_APPROVED', 'GA_REJECTED', 'GA_WITHDRAWN')`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_step_definitions" ALTER COLUMN "code" TYPE "public"."workflow_step_definitions_code_enum_old" USING "code"::"text"::"public"."workflow_step_definitions_code_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."workflow_step_definitions_code_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."workflow_step_definitions_code_enum_old" RENAME TO "workflow_step_definitions_code_enum"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e6f73eaa19ceafbff74c0cf469" ON "workflow_step_definitions" ("code", "workflowTemplateId") `,
    );
    await queryRunner.query(`ALTER TABLE "workflow_state" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."workflow_state_status_enum"`);
  }
}
