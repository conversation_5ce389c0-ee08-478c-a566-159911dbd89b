import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1744368350605 implements MigrationInterface {
  name = 'Migration1744368350605';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "workflowStateId" integer`);
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "UQ_eef23d9c2f14c87587b3f5f20f5" UNIQUE ("workflowStateId")`,
    );
    await queryRunner.query(`ALTER TABLE "grant_program" ADD "workflowStateId" integer`);
    await queryRunner.query(
      `ALTER TABLE "grant_program" ADD CONSTRAINT "UQ_466a92e5e68d626363dd12089f4" UNIQUE ("workflowStateId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_application" ADD CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5" FOREIGN KEY ("workflowStateId") REFERENCES "workflow_state"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "grant_program" ADD CONSTRAINT "FK_466a92e5e68d626363dd12089f4" FOREIGN KEY ("workflowStateId") REFERENCES "workflow_state"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_program" DROP CONSTRAINT "FK_466a92e5e68d626363dd12089f4"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "FK_eef23d9c2f14c87587b3f5f20f5"`);
    await queryRunner.query(`ALTER TABLE "grant_program" DROP CONSTRAINT "UQ_466a92e5e68d626363dd12089f4"`);
    await queryRunner.query(`ALTER TABLE "grant_program" DROP COLUMN "workflowStateId"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP CONSTRAINT "UQ_eef23d9c2f14c87587b3f5f20f5"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "workflowStateId"`);
  }
}
