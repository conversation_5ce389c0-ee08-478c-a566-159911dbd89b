import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1744913628656 implements MigrationInterface {
  name = 'Migration1744913628656';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_call" ADD "createdById" integer`);
    const grantCalls = await queryRunner.query(`SELECT * FROM "grant_call"`);

    if (grantCalls && grantCalls.length > 0) {
      let defaultCreatorUserId = await queryRunner.query(`SELECT "id" FROM "user" ORDER BY "id" ASC LIMIT 1`);
      if (defaultCreatorUserId && defaultCreatorUserId.length > 0 && defaultCreatorUserId[0].id) {
        defaultCreatorUserId = defaultCreatorUserId[0].id;
      } else {
        throw new Error(
          `Migration failed: Could not find any user in the "user" table to use as a default creator ID for existing grant_call rows.`,
        );
      }

      await queryRunner.query(`UPDATE "grant_call" SET "createdById" = $1 WHERE "createdById" IS NULL`, [
        defaultCreatorUserId,
      ]);
    }

    await queryRunner.query(`ALTER TABLE "grant_call" ALTER COLUMN "createdById" SET NOT NULL`);

    await queryRunner.query(
      `ALTER TABLE "grant_call" ADD CONSTRAINT "FK_d48c3a6767d347a997b6bd17ee2" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_call" DROP CONSTRAINT "FK_d48c3a6767d347a997b6bd17ee2"`);
    await queryRunner.query(`ALTER TABLE "grant_call" DROP COLUMN "createdById"`);
  }
}
