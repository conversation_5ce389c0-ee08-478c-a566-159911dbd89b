import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1747746869118 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1) Remove the seeded GA_OPEN step
    await queryRunner.query(`
      DELETE FROM workflow_step_definitions
      WHERE code = 'GA_OPEN'
    `);

    // 2) Rename the current enum so we can rebuild it
    await queryRunner.query(`
      ALTER TYPE "public"."workflow_step_definitions_code_enum"
      RENAME TO "workflow_step_definitions_code_enum_old"
    `);

    // 3) Pull back all labels from that old‐named type
    const oldLabels: Array<{ label: string }> = await queryRunner.query(`
      SELECT enumlabel AS label
      FROM pg_enum
      WHERE enumtypid = (
        SELECT oid
        FROM pg_type
        WHERE typname = 'workflow_step_definitions_code_enum_old'
      )
      ORDER BY enumsortorder
    `);

    // 4) Drop GA_OPEN from the list
    const labels = oldLabels.map((r) => r.label).filter((l) => l !== 'GA_OPEN');
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 5) Recreate the enum without GA_OPEN
    await queryRunner.query(`
      CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM(${labelsSql})
    `);

    // 6) Point the column back at the recreated enum
    await queryRunner.query(`
      ALTER TABLE "workflow_step_definitions"
      ALTER COLUMN "code"
      TYPE "public"."workflow_step_definitions_code_enum"
      USING ("code"::text::public.workflow_step_definitions_code_enum)
    `);

    // 7) Drop the intermediate old type
    await queryRunner.query(`
      DROP TYPE "public"."workflow_step_definitions_code_enum_old"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename current enum to old so we can add GA_OPEN back
    await queryRunner.query(`
      ALTER TYPE "public"."workflow_step_definitions_code_enum"
      RENAME TO "workflow_step_definitions_code_enum_old"
    `);

    // 2) Read all the existing labels
    const oldLabels: Array<{ label: string }> = await queryRunner.query(`
      SELECT enumlabel AS label
      FROM pg_enum
      WHERE enumtypid = (
        SELECT oid
        FROM pg_type
        WHERE typname = 'workflow_step_definitions_code_enum_old'
      )
      ORDER BY enumsortorder
    `);

    // 3) Assemble a list of labels, adding GA_OPEN if missing
    const labels = oldLabels.map((r) => r.label);
    if (!labels.includes('GA_OPEN')) {
      labels.push('GA_OPEN');
    }
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 4) Recreate the enum with GA_OPEN
    await queryRunner.query(`
      CREATE TYPE "public"."workflow_step_definitions_code_enum" AS ENUM(${labelsSql})
    `);

    // 5) Rebind the column to the new type
    await queryRunner.query(`
      ALTER TABLE "workflow_step_definitions"
      ALTER COLUMN "code"
      TYPE "public"."workflow_step_definitions_code_enum"
      USING ("code"::text::public.workflow_step_definitions_code_enum)
    `);

    // 6) Drop the old enum type
    await queryRunner.query(`
      DROP TYPE "public"."workflow_step_definitions_code_enum_old"
    `);

    // 7) Re‐seed the 'Open a Grant Application' step
    const res: Array<{ id: number }> = await queryRunner.query(`
      SELECT id
      FROM workflow_templates
      WHERE "entityType" = 'APPLICATION'
      LIMIT 1
    `);
    if (!res.length) {
      throw new Error('APPLICATION workflow template not found');
    }
    const templateId = res[0].id;

    await queryRunner.query(
      `
      INSERT INTO workflow_step_definitions
        ("workflowTemplateId", name, code, "sequenceNumber", "transitionType", "isTerminal")
      VALUES
        ($1, 'Open a Grant Application', 'GA_OPEN', 1, 'MANUAL', false)
    `,
      [templateId],
    );
  }
}
