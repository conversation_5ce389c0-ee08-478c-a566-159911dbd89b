import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1742336563984 implements MigrationInterface {
  name = 'Migration1742336563984';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "hederaTopicId"`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "actionTopicId" character varying(255)`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "votingTopicId" character varying(255)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "votingTopicId"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "actionTopicId"`);
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "hederaTopicId" character varying(255)`);
  }
}
