import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1741247001256 implements MigrationInterface {
  name = 'Migration1741247001256';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "hederaTopicId" character varying(255)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "hederaTopicId"`);
  }
}
