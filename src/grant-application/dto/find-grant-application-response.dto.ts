import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { GrantApplicationBaseDto } from './grant-application-base.dto';
import { UserDto } from '../../auth/dto/user.dto';
import { WorkflowStatus } from '../../workflow/enums/workflow-status.enum';
import { StageDefinitionResponseDto } from 'src/workflow/dto/stage-definition.response.dto';

export class FindGrantApplicationResponseDto extends GrantApplicationBaseDto {
  @ApiProperty({
    example: 101,
    description: 'Unique ID of the Grant Application.',
  })
  id: number;

  @ApiProperty({
    description: 'Unique slug identifying the Grant Call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  grantCallSlug: string;

  @ApiProperty({
    description: 'Unique slug identifying the Grant Program.',
    example: 'grant-program-to-the-moon-hdoija',
  })
  grantProgramSlug: string;

  @ApiProperty({
    description: 'The current workflow status of the application (e.g., IN_PROGRESS, APPROVED).',
    enumName: 'WorkflowStatus',
    enum: WorkflowStatus,
    example: WorkflowStatus.IN_PROGRESS,
  })
  status: WorkflowStatus;

  @ApiProperty({
    type: UserDto,
    description: 'User who initially created the application',
  })
  creator: UserDto;

  @ApiProperty({
    type: StageDefinitionResponseDto,
    description: 'Current stage of the application',
  })
  currentStage: StageDefinitionResponseDto;

  @ApiProperty({
    example: '0.0.102736',
    description: 'Created action topic id.',
  })
  actionTopicId: string;

  @ApiPropertyOptional({
    example: '0.0.102736',
    nullable: true,
    description: 'Created vote topic id',
  })
  votingTopicId?: string | null;

  @ApiProperty({
    description: 'Timestamp when the application was created.',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Timestamp when the application was last updated.',
  })
  updatedAt: string;
}
