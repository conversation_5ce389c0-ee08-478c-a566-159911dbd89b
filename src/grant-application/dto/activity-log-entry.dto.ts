import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ActivityLogEntryDto {
  @ApiProperty({
    example: '2024-03-15T10:30:00.000Z',
    description: 'Timestamp of the activity.',
  })
  timestamp: string;

  @ApiProperty({
    example: 'Coordinator Name',
    description: 'User that performed the action.',
  })
  who: string;

  @ApiPropertyOptional({
    example: 'Screening',
    description: 'The stage the application was in *before* this activity/transition.',
  })
  fromStage?: string | null;

  @ApiProperty({
    example: 'Interview',
    description: 'The stage the application is in *after* this activity/transition (or current if no transition).',
  })
  toStage: string;

  @ApiProperty({
    example: 'Applicant meets initial criteria.',
    description: 'Reason associated with the activity.',
  })
  reason: string;
}
