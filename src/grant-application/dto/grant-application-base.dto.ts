import { ArrayNotEmpty, IsArray, IsEmail, IsEnum, IsNotEmpty, IsString, MaxLength } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { GrantCategory } from '../../grant-call/enums/grant-category.enum';

export class GrantApplicationBaseDto {
  @ApiProperty({
    description: 'The title of the grant application',
    example: 'Grant Application 1',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(120)
  title: string;

  @ApiProperty({
    description: 'The description of the grant application',
    example: 'This is a grant application',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Target Grant Categories',
    enum: GrantCategory,
    isArray: true,
    required: true,
    example: [GrantCategory.SUSTAINABILITY],
  })
  @IsArray()
  @IsEnum(GrantCategory, { each: true })
  @ArrayNotEmpty({ message: 'At least one category required.' })
  categories: GrantCategory[];

  @ApiProperty({
    description: 'The name of the applying company',
    example: 'Example Company',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({
    description: 'The location/country of the company',
    example: 'Finland',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  companyCountry: string;

  @ApiProperty({
    description: 'Full name of the contact person of the grant application',
    example: 'John Doe',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  contactFullName: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the contact person of the grant application',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  contactEmail: string;

  @ApiProperty({
    description: 'Phone number of the contact person of the grant application',
    example: '+*********** 789',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  contactPhoneNumber: string;
}
