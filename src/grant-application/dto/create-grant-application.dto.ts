import { IsNotEmpty, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { GrantApplicationBaseDto } from './grant-application-base.dto';

export class CreateGrantApplicationDto extends GrantApplicationBaseDto {
  @ApiProperty({
    description: 'The slug of the grant call this grant application should belong to.',
    example: 'my-awesome-grant-call-hdiow',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  grantCallSlug: string;
}
