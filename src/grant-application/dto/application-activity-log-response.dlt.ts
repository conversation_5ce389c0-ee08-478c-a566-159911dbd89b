import { ApiProperty } from '@nestjs/swagger';
import { StageActivityGroupDto } from './stage-activity-group.dto';

export class ApplicationActivityLogResponseDto {
  @ApiProperty({ example: 101 })
  applicationId: number;

  @ApiProperty({
    type: [StageActivityGroupDto],
    description: 'Chronologically ordered activities grouped by the stage they occurred in or transitioned from.',
  })
  activityLog: StageActivityGroupDto[];
}
