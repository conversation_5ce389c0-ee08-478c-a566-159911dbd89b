import { Body, Controller, Get, HttpStatus, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import {
  CreateGrantApplicationDto,
  GrantApplicationDetailResponseDto,
  GetGrantApplicationsQueryDto,
  FindGrantApplicationResponseDto,
} from './dto/index';
import { GrantApplicationService } from './grant-application.service';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { StageWithCountResponseDto } from '../workflow/dto/stage-with-count.response.dto';
import { RolesGuard } from '../auth/role.guard';
import { Roles } from '../auth/role.decorator';
import { UpdateStateStatusDto } from '../workflow/dto/update-state-status.dto';
import { UpdateStateStatusResponseDto } from '../workflow/dto/update-state-status-response.dto';
import { TransitionApplicationStageDto } from './dto/transition-application-stage.dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { RequestUser, RequestUserPayload } from '../auth/request-user.decorator';
import { ApplicationActivityLogResponseDto } from './dto/application-activity-log-response.dlt';
import { OptionalAuthGuard } from '../auth/optional-auth.guard';

@ApiTags('Grant Application')
@Controller('grant-application')
export class GrantApplicationController {
  constructor(private readonly service: GrantApplicationService) {}

  @ApiOperation({ summary: 'Get the defined workflow stage definitions for Grant Applications' })
  @ApiResponse({ status: 200, description: 'List of stage definitions.', type: [StageDefinitionResponseDto] })
  @Get('/stages')
  async getApplicationStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return this.service.getApplicationStageDefinitions();
  }

  @ApiOperation({ summary: 'Get Grant Application workflow stages with counts of applications in each stage.' })
  @ApiResponse({ status: 200, type: [StageWithCountResponseDto] })
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('/stage-counts')
  getGlobalGrantCallStageCounts(): Promise<StageWithCountResponseDto[]> {
    return this.service.getApplicationStagesWithCounts();
  }

  @ApiOperation({ summary: 'Create a new grant application' })
  @ApiResponse({
    status: 201,
    type: GrantApplicationDetailResponseDto,
    description: 'The application has been successfully created.',
  })
  @ApiResponse({ status: 404, description: 'When the given grant call is not found.' })
  @ApiResponse({ status: 400, description: 'When the grant call is not open for applications.' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post()
  create(
    @Body() createGrantApplication: CreateGrantApplicationDto,
    @RequestUser() user: RequestUserPayload,
  ): Promise<GrantApplicationDetailResponseDto> {
    return this.service.create(createGrantApplication, user.id);
  }

  @ApiOperation({ summary: 'Fetch grant applications.' })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of grant applications.',
    type: [FindGrantApplicationResponseDto],
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'If no valid session token is present.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'In case of invalid query params.' })
  @ApiBearerAuth()
  @UseGuards(OptionalAuthGuard)
  @Get()
  getGrantApplications(
    @Query()
    filterOptions: GetGrantApplicationsQueryDto,
    @RequestUser() user: RequestUserPayload,
  ): Promise<FindGrantApplicationResponseDto[]> {
    return this.service.findGrantApplications(filterOptions, user?.id);
  }

  @ApiOperation({ summary: 'Retrieve the grant application using the ID.' })
  @ApiResponse({
    status: 200,
    description: 'The grant application of the grant call has been retrieved successfully.',
    type: FindGrantApplicationResponseDto,
  })
  @ApiResponse({ status: 404, description: 'The grant application does not exist.' })
  @ApiResponse({ status: 403, description: 'If the authenticated user does not have the right permissions.' })
  @ApiParam({ name: 'id', description: 'ID of the grant application.', example: '1' })
  @Get(':id')
  getGrantApplication(@Param('id') id: number): Promise<FindGrantApplicationResponseDto> {
    return this.service.findGrantApplication(id);
  }

  @ApiOperation({ summary: 'Get topic messages for a grant application' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved topic messages.',
    type: ApplicationActivityLogResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Grant application not found.' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User is not a member of the grant call, grant application, or grant program coordinator to access topic messages.',
  })
  @Get(':id/feedback')
  async getApplicationActivityLog(@Param('id') applicationId: number): Promise<ApplicationActivityLogResponseDto> {
    return this.service.getApplicationActivityLog(applicationId);
  }

  @ApiOperation({ summary: "Update Grant Application's main status" })
  @ApiBody({ type: UpdateStateStatusDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application status updated successfully.',
    type: UpdateStateStatusResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission for this status update.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Application not found.' })
  @Patch(':appId/status')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  async updateApplicationStatus(
    @Param('appId') appId: number,
    @Body() body: UpdateStateStatusDto,
    @RequestUser() user: RequestUserPayload,
  ): Promise<UpdateStateStatusResponseDto> {
    return this.service.updateApplicationStatusForCoordinator(appId, body.status, body.reason, user.id);
  }

  @Patch(':appId/withdraw')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiBody({ type: TransitionApplicationStageDto })
  async withdrawApplication(
    @Param('appId') appId: number,
    @Body() body: TransitionApplicationStageDto,
    @RequestUser() user: RequestUserPayload,
  ): Promise<UpdateStateStatusResponseDto> {
    return this.service.withdrawApplication(appId, body.reason, user.id);
  }

  @ApiOperation({ summary: 'Advance Grant Application to the next workflow stage' })
  @ApiBody({ type: TransitionApplicationStageDto, description: 'Requires reason for transition' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application successfully transitioned to the next stage.',
    type: SingleTransitionResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid reason or Application not in a transitionable state.',
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Application not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Transition failed due to workflow rules.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(['GRANT_PROGRAM_COORDINATOR'])
  @Post(':appId/transition')
  async transitionGrantApplication(
    @Param('appId') appId: number,
    @Body() { reason }: TransitionApplicationStageDto,
    @RequestUser() user: RequestUserPayload,
  ): Promise<SingleTransitionResponseDto> {
    return this.service.transitionSingleApplicationStage(appId, reason, user.id);
  }
}
