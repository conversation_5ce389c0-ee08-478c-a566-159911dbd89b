import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { User } from '../auth/entities/user.entity';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { MailService } from '../notifications/mail/mail.service';
import { sanitizeDto } from '../utils/sanitize.util';
import {
  CreateGrantApplicationDto,
  ApplicationActivityLogResponseDto,
  ActivityLogEntryDto,
  StageActivityGroupDto,
  GetGrantApplicationsQueryDto,
  FindGrantApplicationResponseDto,
} from './dto';
import { GrantApplication } from './entities/grant-application.entity';
import { ConfigService } from '@nestjs/config';
import { HederaService } from '../hedera/hedera.service';
import { WorkflowService } from '../workflow/workflow.service';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { StageWithCountResponseDto } from '../workflow/dto/stage-with-count.response.dto';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { GrantApplicationStageCode, StageCode } from '../workflow/enums/stage-code.enum';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { formatStageCode } from '../utils/formatting.util';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { UpdateStateStatusResponseDto } from '../workflow/dto/update-state-status-response.dto';
import { BalanceAlertService } from '../balance-alerting/balance-alert.service';

@Injectable()
export class GrantApplicationService {
  private readonly logger = new Logger(GrantApplicationService.name);
  private readonly maxHederaMessagePayloadLength: number;

  constructor(
    @InjectRepository(GrantApplication)
    private readonly applicationRepo: Repository<GrantApplication>,
    @InjectRepository(User)
    private readonly authRepository: Repository<User>,
    private readonly mailService: MailService,
    private readonly configService: ConfigService,
    private readonly hederaService: HederaService,
    private readonly workflowService: WorkflowService,
    private readonly workflowTransitionService: WorkflowTransitionService,
    private readonly balanceAlertService: BalanceAlertService,
    private readonly dataSource: DataSource,
  ) {
    this.maxHederaMessagePayloadLength = this.configService.get<number>('HEDERA_MESSAGE_PAYLOAD_LIMIT', 1000);
  }

  async create(
    createApplicationDto: CreateGrantApplicationDto,
    requestingUserId: number,
  ): Promise<{ grantApplicationId: number }> {
    const sanitizedDto = sanitizeDto(createApplicationDto);

    return this.applicationRepo.manager.transaction(async (transactionalEntityManager) => {
      const grantCallRepo = transactionalEntityManager.getRepository(GrantCall);
      const grantApplicationRepo = transactionalEntityManager.getRepository(GrantApplication);
      const stepDefRepo = transactionalEntityManager.getRepository(WorkflowStepDefinition);
      const wfTemplateRepo = transactionalEntityManager.getRepository(WorkflowTemplate);
      const wfStateRepo = transactionalEntityManager.getRepository(WorkflowState);

      const grantCall = await grantCallRepo.findOne({
        where: { grantCallSlug: sanitizedDto.grantCallSlug },
        relations: { workflowState: { currentStepDefinition: true } },
      });

      if (!grantCall) {
        throw new NotFoundException(`Grant Call with slug "${sanitizedDto.grantCallSlug}" not found.`);
      }

      if (grantCall.workflowState?.currentStepDefinition?.code !== StageCode.GC_OPEN_FOR_APPLICATIONS) {
        throw new BadRequestException(
          `Cannot create application: Grant Call "${grantCall.name}" is not open for applications.`,
        );
      }

      const workflowTemplate = await wfTemplateRepo.findOneBy({ entityType: WorkflowEntityType.APPLICATION });
      const defaultStepDef = await stepDefRepo.findOneBy({ code: StageCode.GA_SCREENING });

      const initialWorkflowState = wfStateRepo.create({
        workflowTemplate: workflowTemplate,
        currentStepDefinition: defaultStepDef,
        currentStepDefinitionId: defaultStepDef.id,
        currentStepTransitionedAt: new Date(),
        currentStepEndsAt: null,
      });

      await wfStateRepo.save(initialWorkflowState);

      let actionTopicId: string;

      try {
        const topic = await this.hederaService.createTopicWithRetry();

        actionTopicId = topic.toString();
      } catch (hederaError) {
        this.logger.error(`Failed to create Hedera topic for new application: ${hederaError.message}`, hederaError);

        throw new InternalServerErrorException('Failed to create necessary DLT topic.');
      }

      const {
        title,
        description,
        companyName,
        companyCountry,
        contactFullName,
        contactEmail,
        contactPhoneNumber,
        categories,
      } = sanitizedDto;

      let application = grantApplicationRepo.create({
        title,
        description,
        companyName,
        companyCountry,
        categories,
        contactFullName,
        contactEmail,
        contactPhoneNumber,
        grantCall,
        workflowState: initialWorkflowState,
        actionTopicId,
        createdById: requestingUserId,
      });

      application = await grantApplicationRepo.save(application);

      await this.anchorTransitionEvent(
        application,
        'Application Created and Submitted',
        defaultStepDef.code,
        requestingUserId,
        null,
      );

      return {
        grantApplicationId: application.id,
      };
    });
  }

  async transitionSingleApplicationStage(
    applicationId: number,
    reason: string,
    userId: number,
  ): Promise<SingleTransitionResponseDto> {
    const application = await this.applicationRepo.findOne({
      where: { id: applicationId },
      relations: {
        grantCall: {
          grantProgram: true,
        },
        workflowState: { currentStepDefinition: true },
      },
    });

    if (!application) {
      throw new NotFoundException('Grant application not found');
    }

    return this.dataSource.manager.transaction(async (transactionalEntityManager) => {
      const previousStepDef = application.workflowState.currentStepDefinition;

      const transitionResultDto = await this.workflowTransitionService.transitionSingleStateToNextStep(
        WorkflowEntityType.APPLICATION,
        application.id,
        transactionalEntityManager,
      );

      await this.anchorTransitionEvent(
        application,
        reason,
        transitionResultDto.stepDefinitionCode,
        userId,
        previousStepDef.code,
      );
      await this.sendTransitionNotification(application, transitionResultDto.stepDefinitionCode);

      return transitionResultDto;
    });
  }

  async updateApplicationStatusForCoordinator(
    applicationId: number,
    newStatus: WorkflowStatus,
    reason: string | undefined,
    coordinatorUserId: number,
  ): Promise<UpdateStateStatusResponseDto> {
    const application = await this.fetchApplication(applicationId);

    if (newStatus === WorkflowStatus.WITHDRAWN) {
      throw new BadRequestException(
        `Coordinator cannot change status to '${WorkflowStatus.WITHDRAWN}'. Application owners should use the dedicated withdraw endpoint.`,
      );
    }

    return this.performStatusUpdateAndNotifications(application, newStatus, reason, coordinatorUserId);
  }

  async withdrawApplication(
    applicationId: number,
    reason: string | undefined,
    requestingUserId: number,
  ): Promise<UpdateStateStatusResponseDto> {
    const application = await this.fetchApplication(applicationId);

    if (application.createdById !== requestingUserId) {
      throw new ForbiddenException('You do not have permission to withdraw this application.');
    }

    return this.performStatusUpdateAndNotifications(application, WorkflowStatus.WITHDRAWN, reason, requestingUserId);
  }

  async findGrantApplications(
    query: GetGrantApplicationsQueryDto,
    requestingUserId: number | null,
  ): Promise<FindGrantApplicationResponseDto[]> {
    const { grantCallSlug, stageCodes, statusCodes, myApplications, sort = 'createdAt', direction = 'DESC' } = query;

    const queryBuilder = this.applicationRepo
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.createdBy', 'creatorUser')
      .leftJoinAndSelect('application.grantCall', 'grantCall')
      .leftJoinAndSelect('grantCall.grantProgram', 'grantProgram')
      .leftJoinAndSelect('application.workflowState', 'appWorkflowState')
      .leftJoinAndSelect('appWorkflowState.currentStepDefinition', 'appCurrentStepDef')
      .leftJoinAndSelect('appWorkflowState.workflowTemplate', 'appWorkflowTemplate');

    if (grantCallSlug) {
      queryBuilder.andWhere('grantCall.grantCallSlug = :grantCallSlug', { grantCallSlug });
    }

    if (myApplications && requestingUserId) {
      queryBuilder.andWhere('application.createdById = :requestingUserId', { requestingUserId });
    } else if (myApplications && !requestingUserId) {
      return [];
    }

    if (stageCodes && stageCodes.length > 0) {
      queryBuilder.andWhere('appCurrentStepDef.code IN (:...stageCodesToFilter)', { stageCodesToFilter: stageCodes });
    }

    if (statusCodes && statusCodes.length > 0) {
      queryBuilder.andWhere('appWorkflowState.status IN (:...statusCodesToFilter)', {
        statusCodesToFilter: statusCodes,
      });
    }

    queryBuilder.orderBy(`application.${sort}`, direction as 'ASC' | 'DESC');

    const applications = await queryBuilder.getMany();

    return await Promise.all(applications.map((app) => this.mapApplicationToDetailDto(app))).then((results) =>
      results.filter((dto) => dto !== null),
    );
  }

  async findGrantApplication(applicationId: number): Promise<FindGrantApplicationResponseDto> {
    const application = await this.applicationRepo.findOne({
      where: { id: applicationId },
      relations: {
        createdBy: true,
        workflowState: {
          currentStepDefinition: true,
        },
        grantCall: {
          createdBy: true,
          stageSettings: { workflowStepDefinition: true },
          distributionRules: true,
          grantProgram: { grantProgramCoordinator: true },
          workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } },
        },
      },
    });

    if (!application) {
      this.logger.warn(`Grant Application with ID ${applicationId} not found.`);

      throw new NotFoundException(`Grant Application with ID ${applicationId} not found.`);
    }

    return await this.mapApplicationToDetailDto(application);
  }

  async getApplicationActivityLog(applicationId: number): Promise<ApplicationActivityLogResponseDto> {
    const [applicationStageDefinitions, topicMessages] = await Promise.all([
      this.workflowService.getStageDefinitionsForEntityType(WorkflowEntityType.APPLICATION),
      this.getApplicationTopicMessages(applicationId),
    ]);

    if (!applicationStageDefinitions || applicationStageDefinitions.length === 0) {
      this.logger.warn(`No application stage definitions found for activity log generation.`);

      return { applicationId, activityLog: [] };
    }

    const activitiesGroupedByFromStage = new Map<string, ActivityLogEntryDto[]>();

    topicMessages.forEach((msg) => {
      const fromStage = !msg.fromStage || msg.fromStage === '' ? 'InitialCreation' : msg.fromStage;

      if (!activitiesGroupedByFromStage.has(fromStage)) {
        activitiesGroupedByFromStage.set(fromStage, []);
      }

      activitiesGroupedByFromStage.get(fromStage).push(msg);
    });

    const structuredActivityLog: StageActivityGroupDto[] = applicationStageDefinitions
      .map((stageDef) => {
        const activities = activitiesGroupedByFromStage.get(stageDef.name) || [];

        if (stageDef.position === applicationStageDefinitions[0].position) {
          const initialActivities = activitiesGroupedByFromStage.get('InitialCreation') || [];
          activities.unshift(...initialActivities);
        }

        return {
          stageName: stageDef.name,
          stageCode: stageDef.code as unknown as GrantApplicationStageCode,
          position: stageDef.position,
          activities,
        };
      })
      .filter((group) => group.activities.length > 0)
      .sort((a, b) => a.position - b.position);

    return {
      applicationId,
      activityLog: structuredActivityLog,
    };
  }

  async getApplicationStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return await this.workflowService.getStageDefinitionsForEntityType(WorkflowEntityType.APPLICATION);
  }

  async getApplicationStagesWithCounts(): Promise<StageWithCountResponseDto[]> {
    return await this.workflowService.getStageSummariesWithCounts(WorkflowEntityType.APPLICATION);
  }

  async ensureApplicationVotingTopicId(application: FindGrantApplicationResponseDto) {
    if (!application.votingTopicId) {
      this.logger.warn(`Application (ID: ${application.id}) has no votingTopicId.`);

      try {
        const newVoteTopicId = await this.hederaService.createTopicWithRetry();
        this.logger.log(`Created new voteTopicId: ${newVoteTopicId} for Application (ID: ${application.id})`);

        await this.applicationRepo.update(application.id, {
          votingTopicId: newVoteTopicId.toString(),
        });

        return newVoteTopicId.toString();
      } catch (error) {
        this.logger.error(`Failed to create or save voteTopicId for Application (ID: ${application.id})`, error.stack);

        throw new Error(`Failed to establish voting topic for application ${application.id}`);
      }
    }

    return application.votingTopicId;
  }

  private async fetchApplication(applicationId: number): Promise<GrantApplication> {
    const application = await this.applicationRepo.findOne({
      where: { id: applicationId },
      relations: {
        grantCall: {
          grantProgram: true,
        },
        workflowState: { currentStepDefinition: true },
      },
    });

    if (!application) {
      throw new NotFoundException('Grant application not found');
    }

    return application;
  }

  private async performStatusUpdateAndNotifications(
    application: GrantApplication,
    newStatus: WorkflowStatus,
    reason: string | undefined,
    userId: number,
  ): Promise<UpdateStateStatusResponseDto> {
    return this.dataSource.manager.transaction(async (transactionalEntityManager) => {
      await this.workflowTransitionService.updateStateStatus(application.id, newStatus, transactionalEntityManager);

      const maxReasonLength = await this.anchorTransitionEvent(
        application,
        reason,
        newStatus,
        userId,
        application.workflowState.currentStepDefinition.code,
      );

      await this.sendStatusUpdateNotification(application, newStatus, reason);

      this.logger.log(`Updated MAIN status for App ${application.id} to ${newStatus}`);

      return {
        maxReasonLength,
      };
    });
  }

  private async getApplicationTopicMessages(applicationId: number): Promise<ActivityLogEntryDto[]> {
    const application = await this.applicationRepo.findOne({
      where: { id: applicationId },
      select: ['id', 'actionTopicId'],
    });

    if (!application) {
      throw new NotFoundException(`Grant Application with ID ${applicationId} not found.`);
    }

    if (!application.actionTopicId) {
      this.logger.warn(`Application ${applicationId} does not have an actionTopicId.`);

      return [];
    }

    const rawHederaMessages = await this.hederaService.getMessagesFromTopic(application.actionTopicId);

    return rawHederaMessages
      .filter((msg) => typeof msg.message === 'string')
      .map((decodedMessage) => {
        const hederaTimestamp = new Date(decodedMessage.consensus_timestamp * 1000).toISOString();

        try {
          const parsedData = JSON.parse(decodedMessage.message);

          return {
            fromStage: parsedData.previous_status ?? null,
            toStage: parsedData.current_status,
            who: parsedData.who,
            reason: parsedData.reason,
            timestamp: hederaTimestamp,
          };
        } catch (jsonParseError) {
          this.logger.error(
            `Error parsing JSON for app ${applicationId}: ${jsonParseError.message}`,
            `Raw: ${decodedMessage}`,
          );

          return {
            toStage: 'Unparseable Log Entry',
            reason: `Malformed data: ${String(decodedMessage).substring(0, 100)}...`,
            who: '',
            timestamp: hederaTimestamp,
          };
        }
      })
      .sort(
        (a, b) =>
          (a.timestamp ? new Date(a.timestamp).getTime() : 0) - (b.timestamp ? new Date(b.timestamp).getTime() : 0),
      );
  }

  private async mapApplicationToDetailDto(
    application: GrantApplication,
  ): Promise<FindGrantApplicationResponseDto | null> {
    if (!application) {
      return null;
    }

    const {
      id,
      grantCall,
      title,
      companyName,
      description,
      companyCountry,
      categories,
      workflowState,
      actionTopicId,
      votingTopicId,
      createdAt,
      updatedAt,
      contactFullName,
      contactEmail,
      contactPhoneNumber,
      createdBy,
    } = application;

    const { id: createdById, displayName, email } = createdBy;

    return {
      id,
      title,
      description,
      categories,
      companyName,
      companyCountry,
      actionTopicId,
      votingTopicId,
      status: workflowState.status,
      createdAt: createdAt.toISOString(),
      updatedAt: updatedAt.toISOString(),
      contactFullName,
      contactEmail,
      contactPhoneNumber,
      grantCallSlug: grantCall.grantCallSlug,
      grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
      creator: { id: createdById, displayName, email },
      currentStage: this.workflowService.stageDefinitionToDto(workflowState.currentStepDefinition),
    };
  }

  private async sendTransitionNotification(application: GrantApplication, newStepCode: StageCode) {
    try {
      const applicationLink = `${this.configService.get('FRONTEND_BASE_URL')}/grant-programs/${application.grantCall?.grantProgram?.grantProgramSlug}/grant-call/${application.grantCall?.grantCallSlug}/applications/${application.id}`;

      this.mailService.sendApplicationMovedNotification(
        application.contactEmail,
        application.contactFullName,
        application.title,
        formatStageCode(newStepCode),
        applicationLink,
      );

      this.logger.log(`Sent transition notifications for App ${application.id} to stage ${newStepCode}`);
    } catch (error) {
      this.logger.error(
        `Failed to send transition notification for App ${application.id}: ${error.message}`,
        error.stack,
      );
    }
  }

  private async sendStatusUpdateNotification(application: GrantApplication, newStatus: WorkflowStatus, reason: string) {
    if (newStatus !== WorkflowStatus.APPROVED && newStatus !== WorkflowStatus.REJECTED) {
      return;
    }

    try {
      const applicationLink = `${this.configService.get('FRONTEND_BASE_URL')}/grant-programs/${application.grantCall?.grantProgram?.grantProgramSlug}/grant-call/${application.grantCall?.grantCallSlug}/applications/${application.id}`;
      const applicationMail = application.contactEmail;
      if (newStatus === WorkflowStatus.APPROVED) {
        this.mailService.sendApplicationApprovedNotification(
          applicationMail,
          application.contactFullName,
          application.title,
          applicationLink,
        );
      } else if (newStatus === WorkflowStatus.REJECTED) {
        this.mailService.sendApplicationRejectedNotification(
          applicationMail,
          application.contactFullName,
          application.title,
          applicationLink,
          reason,
        );
      }

      this.logger.log(`Sent status update (${newStatus}) notifications for App ${application.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to send status update notification for App ${application.id}: ${error.message}`,
        error.stack,
      );
    }
  }

  private async anchorTransitionEvent(
    application: GrantApplication,
    reason: string,
    newStepCode: StageCode | WorkflowStatus,
    userId: number,
    previousStepCode?: StageCode | null,
  ) {
    if (!application.actionTopicId) {
      this.logger.warn(`DLT Anchoring skipped for App ${application.id} - no actionTopicId defined.`);
      return;
    }

    try {
      await this.balanceAlertService.ensureSufficientSystemOperatorBalance();
      const creator = await this.authRepository.findOne({ where: { id: userId } });
      const baseMessagePayload = {
        previous_status: previousStepCode ? formatStageCode(previousStepCode) : null,
        current_status: newStepCode ? formatStageCode(newStepCode) : null,
        who: creator.displayName,
        reason: '',
      };

      const staticPayloadLength = JSON.stringify(baseMessagePayload).length;
      const maxReasonLength = this.maxHederaMessagePayloadLength - staticPayloadLength;

      this.validateReasonLength(reason, maxReasonLength);

      const messagePayload = { ...baseMessagePayload, reason };

      await this.hederaService.submitMessage(application.actionTopicId, JSON.stringify(messagePayload));

      this.logger.log(
        `Anchored App ${application.id} stage transition to topic ${application.actionTopicId}: ${previousStepCode} -> ${newStepCode}`,
      );

      return maxReasonLength;
    } catch (error) {
      this.logger.error(
        `Error anchoring App ${application.id} stage change (Topic ${application.actionTopicId}):`,
        error,
      );

      throw new UnprocessableEntityException(`Failed to anchor stage transition to DLT: ${error.message}`);
    }
  }

  private validateReasonLength(reason: string, maxReasonLength: number): void {
    if (reason && reason.length > maxReasonLength) {
      throw new BadRequestException(`reason is too long. Maximum allowed characters: ${maxReasonLength}.`);
    }
  }
}
