import { BadRequestException, LogLevel, ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { initializeTracing } from './utils/tracing.util';
import { MetricsModule } from './metrics/metrics.module';

async function bootstrap() {
  // Initialize OpenTelemetry
  await initializeTracing();
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  app.useLogger([configService.get<string>('LOG_LEVEL') as LogLevel]);

  app.enableCors({
    origin: configService.get<string>('CORS_ORIGINS').split(','),
    methods: 'GET,PATCH,POST,PUT,DELETE',
  });

  app.useGlobalPipes(
    new ValidationPipe({
      // Strip away any properties from request bodies/params/queries that don't have class-validator decorators
      whitelist: true,
      exceptionFactory: (errors) => {
        const result = errors.map((error) => {
          return {
            property: error.property,
            constraints: error.constraints,
          };
        });
        return new BadRequestException({
          success: false,
          message: 'Validation failed',
          errors: result,
        });
      },
    }),
  );

  const config = new DocumentBuilder()
    .setTitle('THG API Documentation')
    .setDescription("The HashGraph Association's Funding Platform Backend API Documentation")
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);

  await app.listen(configService.get<string>('BACKEND_PORT') ?? 3000);

  // Metrics app
  const metricsApp = await NestFactory.create(MetricsModule);
  await metricsApp.listen(Number(configService.get<string>('OTEL_METRICS_PORT') ?? 9090));
}
bootstrap();
