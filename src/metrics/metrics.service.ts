import { Injectable } from '@nestjs/common';
import * as client from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly register: client.Registry;

  constructor() {
    this.register = new client.Registry();
    this.register.setDefaultLabels({ app: process.env.METRICS_APP_NAME || 'test-otel' });
    client.collectDefaultMetrics({ register: this.register });
  }

  getMetrics() {
    return this.register.metrics();
  }
}
