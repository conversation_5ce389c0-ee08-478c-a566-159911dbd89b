import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { BalanceAlertModule } from './balance-alerting/balance-alert.module';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
import { GrantApplicationModule } from './grant-application/grant-application.module';
import { GrantCallModule } from './grant-call/grant-call.module';
import { GrantProgramModule } from './grant-program/grant-program.module';
import { Module } from '@nestjs/common';
import { NotificationsModule } from './notifications/notifications.module';
import { VotesModule } from './votes/votes.module';
import { WorkflowModule } from './workflow/workflow.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    DatabaseModule,
    AuthModule,
    GrantProgramModule,
    GrantCallModule,
    GrantApplicationModule,
    NotificationsModule,
    BalanceAlertModule,
    WorkflowModule,
    VotesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
