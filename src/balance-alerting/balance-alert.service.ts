import { <PERSON><PERSON>, <PERSON>barUnit } from '@hashgraph/sdk';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { catchError, firstValueFrom, map, throwError } from 'rxjs';

import { AccountDetailsResponse } from './types/account-details-response.type';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { MailService } from '../notifications/mail/mail.service';
import { User } from '../auth/entities/user.entity';
import { ArrayContains, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class BalanceAlertService {
  private readonly logger = new Logger(BalanceAlertService.name);
  private readonly hederaAccountIdToCheck: string;
  private readonly mirrorNodeUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly mailService: MailService,
    // TODO: Remove this once we have a proper user service
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {
    this.hederaAccountIdToCheck = this.configService.getOrThrow<string>('HEDERA_ACCOUNT_ID');
    this.mirrorNodeUrl = this.configService.getOrThrow<string>('MIRROR_NODE_URL');
  }

  async ensureSufficientSystemOperatorBalance(operationMinimumHbar?: number) {
    const walletToMonitor = this.hederaAccountIdToCheck;
    const balanceUrl = `${this.mirrorNodeUrl}/api/v1/accounts/${walletToMonitor}`;

    const tinybarsBalance = await firstValueFrom(
      this.httpService.get<AccountDetailsResponse>(balanceUrl).pipe(
        map((response) => response.data.balance.balance),
        catchError((err) => {
          this.logger.error(err.message);
          return throwError(() => new HttpException('Request to hedera mirror failed', HttpStatus.FAILED_DEPENDENCY));
        }),
      ),
    );

    const currentHbarBalance = Hbar.fromTinybars(tinybarsBalance).to(HbarUnit.Hbar);
    const currentHbarBalanceNumber = currentHbarBalance.toNumber();

    const thresholds = [
      { limit: 25, level: 'critical' },
      { limit: 50, level: 'alert' },
      { limit: 100, level: 'warning' },
    ] as const;

    const matchedInternalAlert = thresholds.find((t) => currentHbarBalanceNumber <= t.limit);

    if (!matchedInternalAlert) {
      return;
    }

    this.trySendBalanceAlertEmail(walletToMonitor, matchedInternalAlert.level, currentHbarBalanceNumber);

    if (operationMinimumHbar !== undefined && currentHbarBalanceNumber < operationMinimumHbar) {
      const errorMsg =
        `Insufficient funds in system operator wallet (${walletToMonitor}) for operation. ` +
        `Current: ${currentHbarBalanceNumber} Hbar, Required minimum: ${operationMinimumHbar} Hbar.`;

      this.logger.error(errorMsg + ' Operation BLOCKED.');

      throw new HttpException(errorMsg, HttpStatus.PAYMENT_REQUIRED);
    }
  }

  private async trySendBalanceAlertEmail(
    accountIdToAlertFor: string,
    alertLevel: 'critical' | 'alert' | 'warning',
    currentBalanceNumber: number,
  ): Promise<void> {
    let emailToSendTo: string | undefined;
    let displayNameForEmail: string = 'System Administrator';

    const user = await this.userRepository.findOne({
      where: { addresses: ArrayContains([accountIdToAlertFor]) },
    });

    if (user?.email) {
      emailToSendTo = user.email;
      displayNameForEmail = user.displayName || displayNameForEmail;
    } else {
      this.logger.warn(
        `User for alert not found in DB for system operator wallet ${accountIdToAlertFor}. Attempting fallback to EMERGENCY_ALERT_EMAIL.`,
      );

      emailToSendTo = this.configService.getOrThrow<string>('EMERGENCY_ALERT_EMAIL');
    }

    await this.mailService.sendBalanceAlertEmail(emailToSendTo, displayNameForEmail, alertLevel, currentBalanceNumber);

    this.logger.log(
      `Balance alert (Level: ${alertLevel}) sent to ${emailToSendTo} for system wallet ${accountIdToAlertFor}.`,
    );
  }
}
