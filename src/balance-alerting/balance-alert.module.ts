import { BalanceAlertService } from './balance-alert.service';
import { HttpModule } from '@nestjs/axios';
import { MailModule } from '../notifications/mail/mail.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
@Module({
  imports: [TypeOrmModule.forFeature([User]), HttpModule, MailModule],
  providers: [BalanceAlertService],
  exports: [BalanceAlertService],
})
export class BalanceAlertModule {}
