import { HttpException, HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { of, throwError } from 'rxjs';

import { BalanceAlertService } from './balance-alert.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { MailService } from '../notifications/mail/mail.service';
import { Repository } from 'typeorm';
import { User } from '../auth/entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

const DEFAULT_HEDERA_ACCOUNT_ID = '0.0.999';
const DEFAULT_MIRROR_NODE_URL = 'http://test-mirror.node';
const DEFAULT_EMERGENCY_EMAIL = '<EMAIL>';

describe('BalanceAlertService', () => {
  let service: BalanceAlertService;
  let httpService: HttpService;
  let mailService: MailService;
  let userRepository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BalanceAlertService,
        {
          provide: ConfigService,
          useValue: {
            getOrThrow: jest.fn((key: string) => {
              if (key === 'HEDERA_ACCOUNT_ID') return DEFAULT_HEDERA_ACCOUNT_ID;
              if (key === 'MIRROR_NODE_URL') return DEFAULT_MIRROR_NODE_URL;
              throw new Error(`Unmocked config key for getOrThrow: ${key}`);
            }),
            get: jest.fn((key: string, defaultValue?: any) => {
              if (key === 'EMERGENCY_ALERT_EMAIL') return DEFAULT_EMERGENCY_EMAIL;
              return defaultValue;
            }),
          },
        },
        {
          provide: MailService,
          useValue: {
            sendBalanceAlertEmail: jest.fn(),
          },
        },
        {
          provide: HttpService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(BalanceAlertService);
    httpService = module.get(HttpService);
    mailService = module.get(MailService);
    userRepository = module.get(getRepositoryToken(User));
  });

  describe('ensureSufficientSystemOperatorBalance', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      displayName: 'Test User',
      addresses: [DEFAULT_HEDERA_ACCOUNT_ID],
    } as unknown as User;

    it('should throw failed dependency error when the request to the mirror node fails', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      jest.spyOn(httpService, 'get').mockReturnValue(throwError(() => new Error('Failed to fetch balance')));

      await expect(service.ensureSufficientSystemOperatorBalance()).rejects.toThrow(
        new HttpException('Request to hedera mirror failed', HttpStatus.FAILED_DEPENDENCY),
      );
    });

    it('should send a critical email when the balance is below 25 Hbar', async () => {
      jest.spyOn(httpService, 'get').mockReturnValue(of({ data: { balance: { balance: ********** } } } as any));
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockUser);

      await service.ensureSufficientSystemOperatorBalance();

      expect(mailService.sendBalanceAlertEmail).toHaveBeenCalledWith(
        mockUser.email,
        mockUser.displayName,
        'critical',
        24,
      );
    });

    it('should send an alert email when the balance is below 50 Hbar', async () => {
      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: {
            balance: {
              balance: 4900000000,
            },
          },
        } as any),
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      await service.ensureSufficientSystemOperatorBalance();

      expect(mailService.sendBalanceAlertEmail).toHaveBeenCalledWith('<EMAIL>', 'Test User', 'alert', 49);
    });

    it('should send a warning email when the balance is below 100 Hbar', async () => {
      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: {
            balance: {
              balance: 9900000000,
            },
          },
        } as any),
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      await service.ensureSufficientSystemOperatorBalance();

      expect(mailService.sendBalanceAlertEmail).toHaveBeenCalledWith('<EMAIL>', 'Test User', 'warning', 99);
    });

    it('should not send an email when the balance is above 100 Hbar', async () => {
      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: {
            balance: {
              balance: 10100000000,
            },
          },
        } as any),
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({
        email: '<EMAIL>',
        displayName: 'Test User',
      } as User);

      await service.ensureSufficientSystemOperatorBalance();

      expect(mailService.sendBalanceAlertEmail).not.toHaveBeenCalled();
    });
  });
});
