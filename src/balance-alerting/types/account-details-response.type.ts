export type AccountDetailsResponse = {
  account: string | null;
  alias: string | null;
  auto_renew_period: number | null;
  balance: AccountBalance;
  created_timestamp: string | null;
  decline_reward: boolean;
  deleted: boolean | null;
  ethereum_nonce: number | null;
  evm_address: string | null;
  expiry_timestamp: string | null;
  key: Key | null;
  max_automatic_token_associations: number | null;
  memo: string | null;
  pending_reward: number;
  receiver_sig_required: boolean | null;
  staked_account_id: string | null;
  staked_node_id: number | null;
  stake_period_start: string | null;
  transactions: Array<object>; // We don't care about it for now
  links: {
    next: string | null;
  };
};

type AccountBalance = {
  timestamp: string;
  balance: null | number;
  tokens: AccountBalanceToken;
};

type AccountBalanceToken = {
  token_id: null | string;
  balance: number;
};

type Key = {
  _type: 'ECDSA_SECP256K1' | 'ED25519' | 'ProtobufEncoded';
  key: string;
};
