import { ApplicationStatus, GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { PostgreSqlContainer, StartedPostgreSqlContainer } from '@testcontainers/postgresql';
import { Test, TestingModule } from '@nestjs/testing';
import {
  TestData,
  createTestApplicationStage,
  createTestApplicationStageLog,
  createTestData,
  createTestGrantApplication,
  createTestGrantCall,
  createTestGrantProgram,
  createTestUsers,
  generateSessionToken,
} from './test.utils';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';

import { ApplicationStagesSummary } from '../src/grant-call/dto';
import { AuthModule } from '../src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { CreateGrantApplication } from '../src/grant-application/dto/create-grant-application.dto';
import { GrantApplicationDto } from '../src/grant-application/dto';
import { GrantApplicationMember } from '../src/grant-application/entities/grant-application-member.entity';
import { GrantApplicationModule } from '../src/grant-application/grant-application.module';
import { GrantApplicationStage } from '../src/grant-application/entities/application-stage.entity';
import { GrantApplicationStageLog } from '../src/grant-application/entities/application-stage-log.entity';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../src/grant-call/grant-call.module';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantProgramModule } from '../src/grant-program/grant-program.module';
import { HederaService } from '../src/hedera/hedera.service';
import { JwtService } from '@nestjs/jwt';
import { KeyManagementService } from '../src/key-management/key-management.service';
import { Long } from '@hashgraph/sdk';
import { MailerService } from '@nestjs-modules/mailer';
import { PhoneVerificationModule } from '../src/auth/phone-verification/phone-verification.module';
import { Repository } from 'typeorm';
import { User } from '../src/auth/entities/user.entity';
import { UserNotificationPreferences } from '../src/notifications/entities/user-notification-preferences.entity';
import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';
import request from 'supertest';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

describe('GrantApplication (e2e)', () => {
  let app: INestApplication;
  let container: StartedPostgreSqlContainer;
  let userRepository: Repository<User>;
  let grantProgramRepo: Repository<GrantProgram>;
  let grantCallRepo: Repository<GrantCall>;
  let stageRepo: Repository<GrantApplicationStage>;
  let applicationRepo: Repository<GrantApplication>;
  let stageLogRepo: Repository<GrantApplicationStageLog>;
  let applicationMemberRepo: Repository<GrantApplicationMember>;
  let jwtService: JwtService;

  const testUser1 = { email: '<EMAIL>', displayName: 'Test User 1', address: '0.0.1780959' };
  const testUser2 = { email: '<EMAIL>', displayName: 'Test User 2', address: '0.0.2780959' };
  const testUser3 = { email: '<EMAIL>', displayName: 'Test User 3', address: '0.0.3780959' };

  let testUsers: User[];
  let testGrantProgram: GrantProgram;
  let testGrantCall1: GrantCall;
  let testGrantCall2: GrantCall;
  let testStage0GrantCall1: GrantApplicationStage;
  let testStage1GrantCall1: GrantApplicationStage;
  let testStage0GrantCall2: GrantApplicationStage;

  beforeAll(async () => {
    // Set necessary env vars
    process.env.JWT_EXPIRATION_TIME = '1d';
    process.env.JWT_SECRET = 'test';
    process.env.FRONTEND_BASE_URL = 'https://localhost:3000';
    process.env.AWS_REGION = 'eu-central-2';
    container = await new PostgreSqlContainer().start();
    // Setup the testing module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AuthModule,
        GrantProgramModule,
        GrantCallModule,
        GrantApplicationModule,
        ConfigModule.forRoot({ isGlobal: true }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          url: container.getConnectionUri(),
          entities: [
            User,
            GrantProgram,
            GrantCall,
            GrantApplicationStage,
            GrantApplicationStageLog,
            GrantApplication,
            WorkflowState,
            WorkflowStepDefinition,
            WorkflowTemplate,
            GrantApplicationMember,
            UserNotificationPreferences,
            PhoneVerificationModule,
          ],
          synchronize: true,
        }),
      ],
    })
      .overrideProvider(MailerService)
      .useValue({ sendMail: jest.fn() })
      .overrideProvider(HederaService)
      .useValue({
        createTopic: jest.fn().mockResolvedValue({ toString: () => 'mock-topic-id' }),
        submitMessage: jest.fn(),
        getMessagesFromTopic: jest.fn(),
      })
      .overrideProvider(KeyManagementService)
      .useValue({
        signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')),
      })
      .compile();
    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    grantProgramRepo = moduleFixture.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    grantCallRepo = moduleFixture.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    stageRepo = moduleFixture.get<Repository<GrantApplicationStage>>(getRepositoryToken(GrantApplicationStage));
    applicationRepo = moduleFixture.get<Repository<GrantApplication>>(getRepositoryToken(GrantApplication));
    stageLogRepo = moduleFixture.get<Repository<GrantApplicationStageLog>>(
      getRepositoryToken(GrantApplicationStageLog),
    );
    applicationMemberRepo = moduleFixture.get<Repository<GrantApplicationMember>>(
      getRepositoryToken(GrantApplicationMember),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Enable validation pipe because the one in main.ts is not used in tests
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();
  }, 60000);

  beforeEach(async () => {
    await userRepository.manager.connection.dropDatabase();
    await userRepository.manager.connection.synchronize();

    testUsers = await createTestUsers([testUser1, testUser2, testUser3], userRepository);
    testGrantProgram = await createTestGrantProgram('grant-program-slug', testUsers[1], grantProgramRepo);
    testGrantCall1 = await createTestGrantCall('grant-call-slug-1', testGrantProgram, testUsers[0], grantCallRepo);
    testGrantCall2 = await createTestGrantCall('grant-call-slug-2', testGrantProgram, testUsers[0], grantCallRepo);
    testStage0GrantCall1 = await createTestApplicationStage(0, testGrantCall1, stageRepo);
    testStage1GrantCall1 = await createTestApplicationStage(1, testGrantCall1, stageRepo);
    testStage0GrantCall2 = await createTestApplicationStage(0, testGrantCall2, stageRepo);
  });

  afterAll(async () => {
    await app.close();
    await container.stop();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
  });

  describe('application stage summary of a grant call', () => {
    it('should return application count zero if no applications exist for stages', async () => {
      const res = await request(app.getHttpServer()).get(
        `/grant-call/${testGrantCall1.grantCallSlug}/application-stages`,
      );

      const data = res.body.data;
      expect(data).toBeDefined();
      expect(data.totalApplications).toEqual(0);
      expect(data.newApplications).toEqual(0);
      expect(data.stages).toHaveLength(2);
      expect(data.stages[0]).toEqual({
        applications: 0,
        position: testStage0GrantCall1.position,
        title: testStage0GrantCall1.title,
      });
      expect(data.stages[1]).toEqual({
        applications: 0,
        position: testStage1GrantCall1.position,
        title: testStage1GrantCall1.title,
      });
    });

    it('should return correct application count when applications exist for stages', async () => {
      let application = await createTestGrantApplication('Application 1', testGrantCall1, applicationRepo);
      let stageLog = stageLogRepo.create({
        stage: testStage0GrantCall1,
        grantApplication: application,
        startedOn: new Date(),
        finishedOn: null,
      });
      await stageLogRepo.save(stageLog);

      application = await createTestGrantApplication('Application 2', testGrantCall1, applicationRepo);
      // In reality this application should also have a stage log for stage 1, but it doesn't matter for this test
      stageLog = stageLogRepo.create({
        stage: testStage1GrantCall1,
        grantApplication: application,
        startedOn: new Date(),
        finishedOn: null,
      });
      await stageLogRepo.save(stageLog);

      application = await createTestGrantApplication('Application 3', testGrantCall1, applicationRepo);
      stageLog = stageLogRepo.create({
        stage: testStage0GrantCall1,
        grantApplication: application,
        startedOn: new Date(),
        finishedOn: null,
      });
      await stageLogRepo.save(stageLog);

      // Also add an application for the second grant call to make sure it doesn't affect the count
      application = await createTestGrantApplication('Application 4', testGrantCall2, applicationRepo);
      stageLog = stageLogRepo.create({
        stage: testStage0GrantCall2,
        grantApplication: application,
        startedOn: new Date(),
        finishedOn: null,
      });
      await stageLogRepo.save(stageLog);

      const res = await request(app.getHttpServer()).get(
        `/grant-call/${testGrantCall1.grantCallSlug}/application-stages`,
      );
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      const data: ApplicationStagesSummary = res.body.data;
      expect(data).toBeDefined();
      expect(data.totalApplications).toEqual(3);
      expect(data.newApplications).toEqual(0);
      expect(data.stages).toHaveLength(2);
      expect(data.stages[0]).toEqual({
        applications: 2,
        position: testStage0GrantCall1.position,
        title: testStage0GrantCall1.title,
      });
      expect(data.stages[1]).toEqual({
        applications: 1,
        position: testStage1GrantCall1.position,
        title: testStage1GrantCall1.title,
      });
    });
  });

  describe('create grant application', () => {
    it('should create grant application', async () => {
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      const creationDto: CreateGrantApplication = {
        grantCallSlug: testGrantCall1.grantCallSlug,
        title: 'My Grant Application',
        description: 'In this project we will build a rocket to the moon.',
        companyName: 'Example Company',
        companyWebpage: 'https://www.example.com/',
        companyCountry: 'United States',
        industry: 'Technology',
        contactFullName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '+14 123 456 78 90',
      };
      jest.spyOn(app.get(HederaService), 'createTopic').mockResolvedValueOnce({
        shard: Long.fromNumber(0),
        realm: Long.fromNumber(0),
        num: Long.fromNumber(123),
        toString: () => 'mock-topic-id',
        toSolidityAddress: () => '0.0.123',
        toBytes: () => new Uint8Array(),
        _checksum: null,
        checksum: '',
        validateChecksum: () => true,
        validate: () => true,
        compare: () => 0,
        _toProtobuf: () => null,
        toStringWithChecksum: () => null,
        clone: () => null,
      });
      const res = await request(app.getHttpServer())
        .post('/grant-application')
        .send(creationDto)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.CREATED);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toBeDefined();
      expect(res.body.data.grantApplicationId).toBeDefined();

      const application: GrantApplication = await applicationRepo.findOne({
        where: { id: res.body.data.grantApplicationId },
        relations: {
          grantCall: true,
          grantApplicationMembers: { user: true },
          stageLogs: { stage: true },
        },
      });
      expect(application).toBeDefined();
      expect(application.title).toEqual(creationDto.title);
      expect(application.description).toEqual(creationDto.description);
      expect(application.companyName).toEqual(creationDto.companyName);
      expect(application.companyWebpage).toEqual(creationDto.companyWebpage);
      expect(application.companyCountry).toEqual(creationDto.companyCountry);
      expect(application.industry).toEqual(creationDto.industry);
      expect(application.contactFullName).toEqual(creationDto.contactFullName);
      expect(application.contactEmail).toEqual(creationDto.contactEmail);
      expect(application.contactPhoneNumber).toEqual(creationDto.contactPhoneNumber);
      expect(application.status).toEqual('OPEN');
      expect(application.statusChangedOn).toBeDefined();
      expect(application.createdAt).toBeDefined();
      expect(application.grantCall.id).toEqual(testGrantCall1.id);
      expect(application.grantApplicationMembers).toHaveLength(1);
      expect(application.grantApplicationMembers[0].isCreator).toBeTruthy();
      expect(application.grantApplicationMembers[0].user.id).toEqual(testUsers[0].id);
      // Check if the application has a stage log for the first stage
      expect(application.stageLogs).toHaveLength(1);
      expect(application.stageLogs[0].stage.position).toEqual(0);
      expect(application.stageLogs[0].startedOn).toBeDefined();
      expect(application.stageLogs[0].finishedOn).toBeNull();
    });

    it('should fail creating grant application with non-existent grant call', async () => {
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      const creationDto: CreateGrantApplication = {
        grantCallSlug: 'non-existent-grant-call',
        title: 'My Grant Application',
        description: 'In this project we will build a rocket to the moon.',
        companyName: 'Example Company',
        companyWebpage: 'https://www.example.com/',
        companyCountry: 'United States',
        industry: 'Technology',
        contactFullName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '+14 123 456 78 90',
      };

      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);

      const res = await request(app.getHttpServer())
        .post('/grant-application')
        .send(creationDto)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });

      expect(res.status).toEqual(HttpStatus.NOT_FOUND);
      expect(res.body.message).toEqual('Grant call not found');
    });

    it('should fail creating grant application if grant call is closed', async () => {
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      testGrantCall1.isClosed = true;
      await grantCallRepo.save(testGrantCall1);

      const creationDto: CreateGrantApplication = {
        grantCallSlug: testGrantCall1.grantCallSlug,
        title: 'My Grant Application',
        description: 'In this project we will build a rocket to the moon.',
        companyName: 'Example Company',
        companyWebpage: 'https://www.example.com/',
        companyCountry: 'United States',
        industry: 'Technology',
        contactFullName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '+14 123 456 78 90',
      };

      const res = await request(app.getHttpServer())
        .post('/grant-application')
        .send(creationDto)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant call is not open');
    });
  });

  describe('fetch grant applications', () => {
    it('should return correct grant application information', async () => {
      const application = await createTestGrantApplication(
        'Test Application',
        testGrantCall1,
        applicationRepo,
        applicationMemberRepo,
        userRepository,
        testUsers[0].id,
        testUsers[1].id,
      );
      await createTestApplicationStageLog(testStage0GrantCall1, application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      const res = await request(app.getHttpServer())
        .get(`/grant-application/${application.id}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();

      const applicationDto: GrantApplicationDto = res.body.data;
      expect(applicationDto.title).toEqual(application.title);
      expect(applicationDto.companyName).toEqual(application.companyName);
      expect(applicationDto.companyWebpage).toEqual(application.companyWebpage);
      expect(applicationDto.companyCountry).toEqual(application.companyCountry);
      expect(applicationDto.industry).toEqual(application.industry);
      expect(applicationDto.grantCallSlug).toEqual(application.grantCall.grantCallSlug);
      expect(applicationDto.status).toEqual(application.status);

      // Retrieve grantCall of the application
      const grantCall = await grantCallRepo.findOne({
        where: { grantCallSlug: applicationDto.grantCallSlug },
      });
      // Check application stage DTOs
      expect(applicationDto.applicationStages).toHaveLength(grantCall.applicationStages.length);

      //Check grant call DTO
      expect(applicationDto.grantCall.grantCallSlug).toEqual(grantCall.grantCallSlug);
      expect(applicationDto.grantCall.grantCallCoordinator.id).toEqual(
        grantCall.grantCallMembers.find((m) => m.isCoordinator).user.id,
      );
      // Check contact DTO
      expect(applicationDto.contact.contactEmail).toEqual(application.contactEmail);
      expect(applicationDto.contact.contactFullName).toEqual(application.contactFullName);
      expect(applicationDto.contact.contactPhoneNumber).toEqual(application.contactPhoneNumber);
      // Check grant program DTO
      expect(applicationDto.grantCall.grantProgram.grantProgramSlug).toEqual(grantCall.grantProgram.grantProgramSlug);
      expect(applicationDto.grantCall.grantProgram.name).toEqual(grantCall.grantProgram.name);

      // Expect grant program creator to be testUsers[0]
      expect(applicationDto.creator).toEqual({
        id: testUsers[0].id,
        displayName: testUsers[0].displayName,
      });
    });

    it('should fail fetching grant application if it does not exist', async () => {
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      const res = await request(app.getHttpServer())
        .get(`/grant-application/123456`) // Non-existent application ID
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(HttpStatus.NOT_FOUND);
      expect(res.body.message).toEqual('The grant application does not exist.');
    });

    it('should fail fetching grant application if user is not grant call member nor application member', async () => {
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      // Make user 1 a member of the application
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[1], grantApplication: application, isCreator: true }),
      );
      // Make sure user 2 is not a member of the grant call
      expect(testGrantCall1.grantCallMembers.map((m) => m.user.id)).not.toContain(testUsers[2].id);

      const sessionToken = generateSessionToken(jwtService, testUsers[2]);

      const res = await request(app.getHttpServer())
        .get(`/grant-application/${application.id}`)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(HttpStatus.FORBIDDEN);
      expect(res.body.message).toEqual('User is not a member of the grant call or the application nor coordinator');
    });

    it('should return all grant applications', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );
      // Add a member to the first application to later check if creator and members are returned correctly
      await applicationMemberRepo.save(
        applicationMemberRepo.create({
          user: testUsers[1],
          grantApplication: testData.call1Application1,
          isCreator: false,
        }),
      );
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      const res = await request(app.getHttpServer())
        .get(`/grant-application`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();

      const applications: GrantApplicationDto[] = res.body.data;
      // Check if all applications are returned
      expect(applications).toHaveLength(4);
      const applicationIds = applications.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);
      expect(applicationIds).toContain(testData.call1Application2.id);
      expect(applicationIds).toContain(testData.call2Application1.id);
      expect(applicationIds).toContain(testData.call2Application2.id);
      // For the first application check some of the DTO values
      const app1 = applications.find((a) => a.id === testData.call1Application1.id);
      expect(app1.title).toEqual(testData.call1Application1.title);
      expect(app1.status).toEqual(testData.call1Application1.status);
      expect(app1.contact.contactEmail).toEqual(testData.call1Application1.contactEmail);
      expect(app1.grantCallSlug).toEqual(testData.call1.grantCallSlug);
      expect(app1.assignee).toEqual({
        displayName: testData.grantCallCoordinator1.displayName,
        id: testData.grantCallCoordinator1.id,
        email: testData.grantCallCoordinator1.email,
      });
      // Check application stage DTOs
      const call1 = await grantCallRepo.findOne({
        where: { grantCallSlug: app1.grantCallSlug },
        relations: { applicationStages: true, grantCallMembers: { user: true }, grantProgram: true },
      });
      expect(app1.applicationStages).toHaveLength(call1.applicationStages.length);

      //Check grant call DTO
      expect(app1.grantCall.grantCallSlug).toEqual(call1.grantCallSlug);
      expect(app1.grantCall.grantCallCoordinator.id).toEqual(
        call1.grantCallMembers.find((m) => m.isCoordinator).user.id,
      );
      // Check grant program DTO
      expect(app1.grantCall.grantProgram.grantProgramSlug).toEqual(call1.grantProgram.grantProgramSlug);
      expect(app1.grantCall.grantProgram.name).toEqual(call1.grantProgram.name);
      // Check the creator
      expect(app1.creator).toEqual({
        id: testData.call1Application1Creator.id,
        displayName: testData.call1Application1Creator.displayName,
      });
    });

    it('should return grant applications filtered by status', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      // Change the status of one application to rejected
      testData.call1Application1.status = ApplicationStatus.REJECTED;
      await applicationRepo.save(testData.call1Application1);
      // Change the status of one application to withdrawn
      testData.call2Application1.status = ApplicationStatus.WITHDRAWN;
      await applicationRepo.save(testData.call2Application1);

      // Fetch the rejected and withdrawn applications
      let res = await request(app.getHttpServer())
        .get(`/grant-application?status=${ApplicationStatus.REJECTED},${ApplicationStatus.WITHDRAWN}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      let applications: GrantApplicationDto[] = res.body.data;
      // Check if all applications are returned
      expect(applications).toHaveLength(2);
      let applicationIds = applications.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);
      expect(applicationIds).toContain(testData.call2Application1.id);

      // Fetch the open applications
      res = await request(app.getHttpServer())
        .get(`/grant-application?status=${ApplicationStatus.OPEN}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      applications = res.body.data;
      // Check if all applications are returned
      expect(applications).toHaveLength(2);
      applicationIds = applications.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application2.id);
      expect(applicationIds).toContain(testData.call2Application2.id);
    });

    it('should return grant applications filtered by assignee', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );

      // Fetch applications with assignee=me for user that is not a member of any application
      let sessionToken = generateSessionToken(jwtService, testUsers[0]);
      let res = await request(app.getHttpServer())
        .get(`/grant-application?assignee=me`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(0);

      // Fetch applications assignee=me for user that is member of 2 applications
      sessionToken = generateSessionToken(jwtService, testData.grantCallCoordinator1);
      res = await request(app.getHttpServer())
        .get(`/grant-application?assignee=me`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(2);
      let applicationIds = res.body.data.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);
      expect(applicationIds).toContain(testData.call1Application2.id);

      // Fetch applications with user ID for user that is member of 2 application
      sessionToken = generateSessionToken(jwtService, testData.grantCallCoordinator1);
      res = await request(app.getHttpServer())
        .get(`/grant-application?assignee=${testData.grantCallCoordinator1.id}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(2);
      applicationIds = res.body.data.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);
      expect(applicationIds).toContain(testData.call1Application2.id);
    });

    it('should return grant applications filtered by member', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );

      // Fetch applications with member=me for user that is creator of one application
      let sessionToken = generateSessionToken(jwtService, testData.call1Application1Creator);
      let res = await request(app.getHttpServer())
        .get(`/grant-application?member=me`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(1);
      let applicationIds = res.body.data.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);

      // Add another member to the first application. Check if filtering with the non-creator works too.
      await applicationMemberRepo.save(
        applicationMemberRepo.create({
          user: testData.grantCallCoordinator1,
          grantApplication: testData.call1Application1,
          isCreator: false,
        }),
      );
      sessionToken = generateSessionToken(jwtService, testData.grantCallCoordinator1);
      res = await request(app.getHttpServer())
        .get(`/grant-application?member=me`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(1);
      applicationIds = res.body.data.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);
    });

    it('should throw an error when filtering grant applications by member and by assignee', async () => {
      await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );

      // Fetch applications with member=me and assignee=me
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      const res = await request(app.getHttpServer())
        .get(`/grant-application?member=me&assignee=me`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Cannot filter by assignee and member at the same time');
    });

    it('should return grant applications filtered by grant call', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );

      // Fetch applications for grant call 2
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      const res = await request(app.getHttpServer())
        .get(`/grant-application?grantCall=${testData.call2.grantCallSlug}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(2);
      const applicationIds = res.body.data.map((app) => app.id);
      expect(applicationIds).toContain(testData.call2Application1.id);
      expect(applicationIds).toContain(testData.call2Application2.id);
    });

    it('should return grant applications sorted by creation date', async () => {
      // jest.setTimeout(10000);
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );

      // Fetch all applications in ascending order
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      let res = await request(app.getHttpServer())
        .get(`/grant-application?sort=created&direction=asc`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(4);
      let applications = res.body.data;
      let sorted = applications
        .slice()
        .sort(
          (a: GrantApplicationDto, b: GrantApplicationDto) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);

      // Fetch all applications in descending order
      res = await request(app.getHttpServer())
        .get(`/grant-application?sort=created&direction=desc`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(4);
      applications = res.body.data;
      sorted = applications
        .slice()
        .sort(
          (a: GrantApplicationDto, b: GrantApplicationDto) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);

      // Fetch applications with status=OPEN in ascending order
      // Change the status of one application to rejected
      testData.call1Application2.status = ApplicationStatus.REJECTED;
      await applicationRepo.save(testData.call1Application2);
      res = await request(app.getHttpServer())
        .get(`/grant-application?sort=created&direction=asc&status=OPEN`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(3);
      applications = res.body.data;
      sorted = applications
        .slice()
        .sort(
          (a: GrantApplicationDto, b: GrantApplicationDto) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);
    });
  });

  describe('move application to next stage', () => {
    it('should move application from first to second stage', async () => {
      // Setup
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testGrantCall1.id },
        relations: { applicationStages: true, grantCallMembers: { user: true } },
      });
      expect(grantCall.grantCallMembers.map((m) => m.user.id)).toContain(testUsers[0].id);
      expect(grantCall.applicationStages).toHaveLength(2);
      let application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[0], grantApplication: application, isCreator: true }),
      );
      await createTestApplicationStageLog(grantCall.applicationStages[0], application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined); // Mock submitMessage

      // Test
      const res = await request(app.getHttpServer())
        .post(`/grant-application/${application.id}/change-stage`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });

      expect(res.status).toEqual(HttpStatus.CREATED);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toBeDefined();
      expect(res.body.data.currentStage).toEqual(1);
      application = await applicationRepo.findOneOrFail({
        where: { id: application.id },
        relations: { stageLogs: { stage: true } },
      });
      expect(application.stageLogs).toHaveLength(2);
      expect(application.stageLogs[0].finishedOn).toBeDefined();
      expect(application.stageLogs[1].finishedOn).toBeNull();
      expect(application.stageLogs[1].stage.position).toEqual(1);
      expect(application.status).toEqual(ApplicationStatus.OPEN);
    });

    it('should change status to APPROVED if last stage is passed', async () => {
      // Setup
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testGrantCall1.id },
        relations: { applicationStages: true, grantCallMembers: { user: true } },
      });
      expect(grantCall.grantCallMembers.map((m) => m.user.id)).toContain(testUsers[0].id);
      expect(grantCall.applicationStages).toHaveLength(2);
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[0], grantApplication: application, isCreator: true }),
      );
      await createTestApplicationStageLog(grantCall.applicationStages[0], application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined); // Mock submitMessage

      // Test
      // Move to the second stage
      let res = await request(app.getHttpServer())
        .post(`/grant-application/${application.id}/change-stage`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });
      expect(res.status).toEqual(HttpStatus.CREATED);

      // Move passed the second stage
      res = await request(app.getHttpServer())
        .post(`/grant-application/${application.id}/change-stage`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });
      expect(res.status).toEqual(HttpStatus.CREATED);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toBeDefined();
      expect(res.body.data.currentStage).toEqual(-1); // -1 means the application is approved

      const updateApplication = await applicationRepo.findOneOrFail({
        where: { id: application.id },
        relations: { stageLogs: { stage: true } },
      });

      expect(updateApplication.stageLogs).toHaveLength(2);
      expect(updateApplication.stageLogs[1].stage.position).toEqual(1);
      expect(updateApplication.stageLogs[1].finishedOn).toBeDefined();
      expect(updateApplication.status).toEqual(ApplicationStatus.APPROVED);
      expect(updateApplication.statusChangedOn.getTime()).toBeGreaterThan(application.statusChangedOn.getTime());
    });

    it('should fail moving the application if user is not grant call member', async () => {
      // Setup
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testGrantCall1.id },
        relations: { applicationStages: true, grantCallMembers: { user: true } },
      });
      // Make sure user[1] is not a member of the grant call
      expect(grantCall.grantCallMembers.map((m) => m.user.id)).not.toContain(testUsers[1].id);
      expect(grantCall.applicationStages).toHaveLength(2);
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      await createTestApplicationStageLog(grantCall.applicationStages[0], application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[1]);
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      // Test
      const res = await request(app.getHttpServer())
        .post(`/grant-application/${application.id}/change-stage`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });
      expect(res.status).toEqual(HttpStatus.FORBIDDEN);
      expect(res.body.message).toEqual('User is not a member of the corresponding grant call');
    });

    it('should fail moving the application if it is not open', async () => {
      // Setup
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testGrantCall1.id },
        relations: { applicationStages: true, grantCallMembers: { user: true } },
      });
      expect(grantCall.grantCallMembers.map((m) => m.user.id)).toContain(testUsers[0].id);
      expect(grantCall.applicationStages).toHaveLength(2);
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      await createTestApplicationStageLog(grantCall.applicationStages[0], application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      application.status = ApplicationStatus.REJECTED;
      await applicationRepo.save(application);

      // Test REJECTED
      application.status = ApplicationStatus.REJECTED;
      await applicationRepo.save(application);
      let res = await request(app.getHttpServer())
        .post(`/grant-application/${application.id}/change-stage`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');

      // Test APPROVED
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      application.status = ApplicationStatus.APPROVED;
      await applicationRepo.save(application);
      res = await request(app.getHttpServer())
        .post(`/grant-application/${application.id}/change-stage`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');

      // Test CLOSED
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      application.status = ApplicationStatus.WITHDRAWN;
      await applicationRepo.save(application);
      res = await request(app.getHttpServer())
        .post(`/grant-application/${application.id}/change-stage`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');
    });
  });

  describe('withdraw application', () => {
    it('should withdraw application when invoked by creator', async () => {
      // Setup
      let application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      expect(application.status).toEqual(ApplicationStatus.OPEN);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[0], grantApplication: application, isCreator: true }),
      );
      await createTestApplicationStageLog(testStage0GrantCall1, application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      const dateBefore = application.statusChangedOn;
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      // Test
      const res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/withdraw`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Withdraw application' });

      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();

      application = await applicationRepo.findOneOrFail({ where: { id: application.id } });
      expect(application.status).toEqual(ApplicationStatus.WITHDRAWN);
      expect(application.statusChangedOn.getTime()).toBeGreaterThan(dateBefore.getTime());
    });

    it('should withdraw application when invoked by application member', async () => {
      // Setup
      let application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      expect(application.status).toEqual(ApplicationStatus.OPEN);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[0], grantApplication: application, isCreator: false }),
      );
      await createTestApplicationStageLog(testStage0GrantCall1, application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      const dateBefore = application.statusChangedOn;
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      // Test
      const res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/withdraw`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Withdraw application' });

      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();

      application = await applicationRepo.findOneOrFail({ where: { id: application.id } });
      expect(application.status).toEqual(ApplicationStatus.WITHDRAWN);
      expect(application.statusChangedOn.getTime()).toBeGreaterThan(dateBefore.getTime());
    });

    it('should fail withdraw when application is not open', async () => {
      // Setup
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[0], grantApplication: application, isCreator: true }),
      );
      await createTestApplicationStageLog(testStage0GrantCall1, application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      // Test REJECTED
      application.status = ApplicationStatus.REJECTED;
      await applicationRepo.save(application);
      let res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/withdraw`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Withdraw application' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');

      // Test APPROVED
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      application.status = ApplicationStatus.APPROVED;
      await applicationRepo.save(application);
      res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/withdraw`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Withdraw application' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');

      // Test CLOSED
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      application.status = ApplicationStatus.WITHDRAWN;
      await applicationRepo.save(application);
      res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/withdraw`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Withdraw application' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');
    });

    it('should fail withdraw when user is not application creator', async () => {
      // Setup
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      expect(application.status).toEqual(ApplicationStatus.OPEN);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[1], grantApplication: application, isCreator: true }),
      );
      await createTestApplicationStageLog(testStage0GrantCall1, application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      // Test
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      const res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/withdraw`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Withdraw application' });
      expect(res.status).toEqual(HttpStatus.FORBIDDEN);
      expect(res.body.message).toEqual('User is not a member of the application');
    });
  });

  describe('reject application', () => {
    it('should reject application', async () => {
      // Setup
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testGrantCall1.id },
        relations: { applicationStages: true, grantCallMembers: { user: true } },
      });
      expect(grantCall.grantCallMembers.map((m) => m.user.id)).toContain(testUsers[0].id);
      let application = await createTestGrantApplication('Test Application', grantCall, applicationRepo);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[0], grantApplication: application, isCreator: true }),
      );
      await createTestApplicationStageLog(testStage0GrantCall1, application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);
      const dateBefore = application.statusChangedOn;

      // Test
      const res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/reject`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Reject application' });

      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();

      application = await applicationRepo.findOneOrFail({ where: { id: application.id } });
      expect(application.status).toEqual(ApplicationStatus.REJECTED);
      expect(application.statusChangedOn.getTime()).toBeGreaterThan(dateBefore.getTime());
    });

    it('should fail reject when application is not open', async () => {
      // Setup
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testGrantCall1.id },
        relations: { applicationStages: true, grantCallMembers: { user: true } },
      });
      expect(grantCall.grantCallMembers.map((m) => m.user.id)).toContain(testUsers[0].id);
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      await applicationMemberRepo.save(
        applicationMemberRepo.create({ user: testUsers[0], grantApplication: application, isCreator: true }),
      );
      await createTestApplicationStageLog(testStage0GrantCall1, application, stageLogRepo);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      // Test REJECTED
      application.status = ApplicationStatus.REJECTED;
      await applicationRepo.save(application);
      let res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/reject`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Reject application' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');

      // Test APPROVED
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      application.status = ApplicationStatus.APPROVED;
      await applicationRepo.save(application);
      res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/reject`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Reject application' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');

      // Test CLOSED
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      application.status = ApplicationStatus.WITHDRAWN;
      await applicationRepo.save(application);
      res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/reject`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Reject application' });
      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Grant application is not open');
    });

    it('should fail reject when user is not grant call member', async () => {
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testGrantCall1.id },
        relations: { applicationStages: true, grantCallMembers: { user: true } },
      });
      // Make sure user[1] is not a member of the grant call
      expect(grantCall.grantCallMembers.map((m) => m.user.id)).not.toContain(testUsers[1].id);
      expect(grantCall.applicationStages).toHaveLength(2);
      const application = await createTestGrantApplication('Test Application', testGrantCall1, applicationRepo);
      await createTestApplicationStageLog(grantCall.applicationStages[0], application, stageLogRepo);
      await applicationMemberRepo.save(
        // add testUser[1] as member of the application. should not influence the test
        applicationMemberRepo.create({ user: testUsers[1], grantApplication: application, isCreator: true }),
      );
      const sessionToken = generateSessionToken(jwtService, testUsers[1]);

      // Test
      jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
      const res = await request(app.getHttpServer())
        .patch(`/grant-application/${application.id}/reject`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Reject application' });
      expect(res.status).toEqual(HttpStatus.FORBIDDEN);
      expect(res.body.message).toEqual('User is not a member of the corresponding grant call');
    });
  });

  it('grant program coordinator should be able to view grant application details', async () => {
    const testGrantProgram = await createTestGrantProgram('test-grant-program-abc', testUsers[0], grantProgramRepo);
    const testGrantCall = await createTestGrantCall(
      'test-grant-call-abc',
      testGrantProgram,
      testUsers[1],
      grantCallRepo,
    );
    const testApplication = await createTestGrantApplication(
      'test-application-abc',
      testGrantCall,
      applicationRepo,
      applicationMemberRepo,
      userRepository,
      testUsers[1].id,
      testUsers[2].id,
    );

    const sessionToken = generateSessionToken(jwtService, testUsers[0]);
    const grantApplication = await request(app.getHttpServer())
      .get(`/grant-application/${testApplication.id}`)
      .set('Authorization', `Bearer ${sessionToken}`);

    expect(grantApplication.status).toEqual(HttpStatus.OK);
    expect(grantApplication.body.success).toBeTruthy();
  });
});
