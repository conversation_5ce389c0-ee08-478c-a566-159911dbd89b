import * as path from 'node:path';
import * as request from 'supertest';

import { ApplicationStatus, GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { CreateGrantCallDto, GrantCallDto } from '../src/grant-call/dto';
import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { PostgreSqlContainer, StartedPostgreSqlContainer } from '@testcontainers/postgresql';
import { Test, TestingModule } from '@nestjs/testing';
import { TestData, createTestData, createTestGrantProgram, createTestUsers, generateSessionToken } from './test.utils';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';

import { AuthModule } from '../src/auth/auth.module';
import { BusinessCategory } from '../src/grant-call/enums/business-category.enum';
import { ConfigModule } from '@nestjs/config';
import { GrantApplicationDto } from '../src/grant-application/dto';
import { GrantApplicationMember } from '../src/grant-application/entities/grant-application-member.entity';
import { GrantApplicationStage } from '../src/grant-application/entities/application-stage.entity';
import { GrantApplicationStageLog } from '../src/grant-application/entities/application-stage-log.entity';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../src/grant-call/grant-call.module';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantProgramModule } from '../src/grant-program/grant-program.module';
import { HederaService } from '../src/hedera/hedera.service';
import { JwtScope } from '../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { KeyManagementService } from '../src/key-management/key-management.service';
import { MailerService } from '@nestjs-modules/mailer';
import { PhoneVerificationModule } from '../src/auth/phone-verification/phone-verification.module';
import { Repository } from 'typeorm';
import { User } from '../src/auth/entities/user.entity';
import { UserNotificationPreferences } from '../src/notifications/entities/user-notification-preferences.entity';
import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';
import { readFileSync } from 'node:fs';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

describe('GrantCall (e2e)', () => {
  let app: INestApplication;
  let container: StartedPostgreSqlContainer;
  let userRepository: Repository<User>;
  let grantProgramRepo: Repository<GrantProgram>;
  let grantCallRepo: Repository<GrantCall>;
  let stageRepo: Repository<GrantApplicationStage>;
  let applicationRepo: Repository<GrantApplication>;
  let stageLogRepo: Repository<GrantApplicationStageLog>;
  let applicationMemberRepo: Repository<GrantApplicationMember>;
  let jwtService: JwtService;

  let sessionToken: string;
  let grantCallSlug: string; // slug of a grant call created in the test
  let testUsers: User[];
  const grantProgramSlug = 'test-grant-program';
  const testUser1 = { email: '<EMAIL>', displayName: 'Test User 1', address: '0.0.1780959' };
  const testUser2 = { email: '<EMAIL>', displayName: 'Test User 2', address: '0.0.2780959' };
  const testUser3 = { email: '<EMAIL>', displayName: 'Test User 3', address: '0.0.3780959' };

  beforeAll(async () => {
    // Set necessary env vars
    process.env.JWT_EXPIRATION_TIME = '1d';
    process.env.CHALLENGE_TOKEN_EXPIRATION_TIME = '5m';
    process.env.RECOVERY_TOKEN_EXPIRATION_TIME = '5m';
    process.env.JWT_SECRET = 'test';
    process.env.FRONTEND_BASE_URL = 'https://localhost:3000';

    process.env.AWS_REGION = 'eu-central-2';

    container = await new PostgreSqlContainer().start();
    // Setup the testing module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AuthModule,
        GrantProgramModule,
        GrantCallModule,
        ConfigModule.forRoot({ isGlobal: true }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          url: container.getConnectionUri(),
          entities: [
            User,
            GrantProgram,
            GrantCall,
            GrantApplicationStage,
            GrantApplicationStageLog,
            GrantApplication,
            WorkflowState,
            WorkflowStepDefinition,
            WorkflowTemplate,
            GrantApplicationMember,
            UserNotificationPreferences,
            PhoneVerificationModule,
          ],
          synchronize: true,
        }),
      ],
    })
      .overrideProvider(MailerService)
      .useValue({ sendMail: jest.fn() })
      .overrideProvider(HederaService)
      .useValue({
        createTopic: jest.fn().mockResolvedValue({ toString: () => 'mock-topic-id' }),
        submitMessage: jest.fn(),
        getMessagesFromTopic: jest.fn(),
      })
      .overrideProvider(KeyManagementService)
      .useValue({
        signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')),
      })
      .compile();
    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    grantProgramRepo = moduleFixture.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    grantCallRepo = moduleFixture.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    stageRepo = moduleFixture.get<Repository<GrantApplicationStage>>(getRepositoryToken(GrantApplicationStage));
    applicationRepo = moduleFixture.get<Repository<GrantApplication>>(getRepositoryToken(GrantApplication));
    stageLogRepo = moduleFixture.get<Repository<GrantApplicationStageLog>>(
      getRepositoryToken(GrantApplicationStageLog),
    );
    applicationMemberRepo = moduleFixture.get<Repository<GrantApplicationMember>>(
      getRepositoryToken(GrantApplicationMember),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);
    // Enable validation pipe because the one in main.ts is not used in tests
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();

    testUsers = await createTestUsers([testUser1, testUser2, testUser3], userRepository);
    await createTestGrantProgram(grantProgramSlug, testUsers[1], grantProgramRepo);
    // Create session token for the testUser[0]
    sessionToken = jwtService.sign({
      payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
    });
  }, 60000);

  describe('create grant call', () => {
    it('should create a grant call', async () => {
      // get grant program from DB
      const grantProgram = await grantProgramRepo.findOneOrFail({ where: { grantProgramSlug: grantProgramSlug } });

      const createGrantCall: CreateGrantCallDto = {
        grantProgramSlug: grantProgram.grantProgramSlug,
        name: 'Test Grant Call',
        description: 'This is a test grant call',
        businessCategory: BusinessCategory.STARTUP,
      };

      const res = await request(app.getHttpServer())
        .post('/grant-call')
        .send(createGrantCall)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(201);
      expect(res.body.success).toBeTruthy();
      expect(res.body.grantCallSlug).toContain('test-grant-call');

      const calls = await grantCallRepo.find({
        relations: {
          grantProgram: true,
          grantCallMembers: {
            user: true,
          },
          applicationStages: true,
        },
      });
      expect(calls.length).toEqual(1);
      const call = calls[0];
      expect(call.name).toEqual(createGrantCall.name);
      expect(call.description).toEqual(createGrantCall.description);
      expect(call.businessCategory).toEqual(createGrantCall.businessCategory);
      expect(call.targetedIndustries).toEqual(createGrantCall.targetedIndustries);
      expect(call.startDate).toEqual(createGrantCall.startDate);
      expect(call.endDate).toEqual(createGrantCall.endDate);
      expect(call.minGrantSize).toEqual(createGrantCall.minGrantSize);
      expect(call.maxGrantSize).toEqual(createGrantCall.maxGrantSize);
      expect(call.grantCallMembers).toHaveLength(1);
      const coordinatorMember = call.grantCallMembers.find((member) => member.isCoordinator);
      expect(coordinatorMember.user.email).toEqual(testUser1.email);
      expect(call.grantProgram.id).toEqual(grantProgram.id);
      grantCallSlug = res.body.grantCallSlug;

      // Check if the application stages have been created
      const filePath = path.join(__dirname, '..', 'src', 'assets', 'default-application-stages.json');
      const stageData = JSON.parse(readFileSync(filePath, 'utf-8'));
      expect(call.applicationStages).toHaveLength(stageData.length);
      for (const stage of stageData) {
        const dbStage = call.applicationStages.find((s) => s.position === stage.position);
        expect(dbStage.title).toEqual(stage.title);
      }
    });

    it('should successfully create a grant call', async () => {
      const expectedSlug = 'new-grant-call-slug';
      const result = await service.create(createDto, creatorId);

      expect(result).toEqual({ grantCallSlug: expectedSlug });
      expect(grantCallRepository.manager.transaction).toHaveBeenCalledTimes(1);
      expect((service as any).getProgramAndStageDefinitions).toHaveBeenCalledWith(
        createDto.grantProgramSlug,
        ALL_POSSIBLE_SETTING_STAGES,
        mockTransactionalEntityManager,
      );
      expect(mockedGenerateUniqueSlug).toHaveBeenCalledWith(createDto.name, expect.any(Function), 'grant-call');
      expect((service as any).buildStageSettings).toHaveBeenCalledWith(createDto, stepDefMap);
      expect((service as any).buildDistributionRules).toHaveBeenCalledWith(createDto);

      expect(mockGrantCallRepoTransactional.save).toHaveBeenCalledTimes(1);
      expect(mockGrantCallRepoTransactional.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: createDto.name,
          grantCallSlug: expectedSlug,
          description: createDto.description,
          businessCategory: createDto.businessCategory,
          categories: createDto.categories,
          totalGrantAmount: createDto.totalGrantAmount,
          createdById: creatorId,
          grantProgram: mockProg,
          stageSettings: mockSettings,
          distributionRules: mockRules,
        }),
      );
    });

    it('should create a grant call without end date', async () => {
      // get grant program from DB
      const grantProgram = await grantProgramRepo.findOneOrFail({ where: { grantProgramSlug: grantProgramSlug } });

      // CreateGrantCall DTO without an end date
      const createGrantCall = {
        grantProgramSlug: grantProgram.grantProgramSlug,
        name: 'Grant Call without end date',
        description: 'This is a test grant call',
        businessCategory: BusinessCategory.STARTUP,
        targetedIndustries: ['Technology'],
        startDate: new Date(),
        minGrantSize: 1000,
        maxGrantSize: 5000,
      };

      const res = await request(app.getHttpServer())
        .post('/grant-call')
        .send(createGrantCall)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(201);

      // Get the grant call from the database and check that the end date is null
      const call = await grantCallRepo.findOneByOrFail({ grantCallSlug: res.body.grantCallSlug });
      expect(call.endDate).toBeNull();
    });
  });

  describe('get grant calls', () => {
    it('should get previously created grant call', async () => {
      const res = await request(app.getHttpServer()).get('/grant-call/' + grantCallSlug);

      expect(res.status).toEqual(200);
      expect(res.body.success).toBeTruthy();
      const grantCallResp: GrantCallDto = res.body.data;
      expect(grantCallResp.name).toEqual('Test Grant Call');
      expect(grantCallResp.description).toEqual('This is a test grant call');
      expect(grantCallResp.businessCategory).toEqual(BusinessCategory.STARTUP);
      expect(grantCallResp.targetIndustries).toEqual(['Technology']);
      expect(grantCallResp.startDate).toBeDefined();
      expect(grantCallResp.endDate).toBeDefined();
      expect(grantCallResp.status).toEqual('OPEN');
      expect(grantCallResp.minGrantSize).toEqual(1000);
      expect(grantCallResp.maxGrantSize).toEqual(5000);
      expect(grantCallResp.grantCallCoordinator.displayName).toEqual(testUser1.displayName);
      expect(grantCallResp.grantProgram.name).toEqual('Test Grant Program');
      expect(grantCallResp.grantProgram.grantProgramSlug).toEqual(grantProgramSlug);
      expect(grantCallResp.grantProgram.grantProgramCoordinator.displayName).toEqual(testUser2.displayName);
    });

    it('should check if the grant call is expired', async () => {
      const calls = await grantCallRepo.find({
        relations: {
          grantProgram: true,
          grantCallMembers: {
            user: true,
          },
        },
      });
      expect(calls.length).toEqual(2);
      const grantCall = calls[0];
      grantCall.startDate = new Date(new Date().setFullYear(new Date().getFullYear() - 2));
      grantCall.endDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1));
      await grantCallRepo.save(grantCall);

      const res = await request(app.getHttpServer()).get('/grant-call/' + grantCallSlug);

      expect(res.status).toEqual(200);
      expect(res.body.success).toBeTruthy();

      const grantCallResp: GrantCallDto = res.body.data;
      // Check if the status is `EXPIRED`
      expect(grantCallResp.status).toEqual('EXPIRED');
    });

    it('should check if the grant call is closed', async () => {
      const calls = await grantCallRepo.find({
        relations: {
          grantProgram: true,
          grantCallMembers: {
            user: true,
          },
        },
      });
      expect(calls.length).toEqual(2);
      const grantCall = calls[0];
      grantCall.startDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1));
      grantCall.endDate = new Date(new Date().setFullYear(new Date().getFullYear() + 1));
      grantCall.isClosed = true;
      await grantCallRepo.save(grantCall);

      const res = await request(app.getHttpServer()).get('/grant-call/' + grantCallSlug);

      expect(res.status).toEqual(200);
      expect(res.body.success).toBeTruthy();

      const grantCallResp: GrantCallDto = res.body.data;
      // Check if the status is `CLOSED`
      expect(grantCallResp.status).toEqual('CLOSED');
    });
  });

  describe('should update the grant call', () => {
    it('should update name, slug and description of grant call', async () => {
      const updateGrantCall = {
        name: 'Updated Grant Call',
        description: 'This is an updated grant call',
      };

      const res = await request(app.getHttpServer())
        .patch('/grant-call/' + grantCallSlug)
        .send(updateGrantCall)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.body.success).toBeTruthy();
      const newGrantCallSlug = res.body.grantCallSlug;

      const updatedCall = await grantCallRepo.findOneOrFail({
        where: { grantCallSlug: newGrantCallSlug },
        relations: {
          grantCallMembers: { user: true },
        },
      });
      expect(updatedCall.grantCallSlug).toContain('updated-grant-call');
      expect(updatedCall.name).toEqual(updateGrantCall.name);
      expect(updatedCall.description).toEqual(updateGrantCall.description);

      // Update the grant call slug for the next test
      grantCallSlug = newGrantCallSlug;
    });

    it('should not change slug if name is not updated', async () => {
      const updateGrantCall = {
        description: 'Yet another update to the description',
      };

      const res = await request(app.getHttpServer())
        .patch('/grant-call/' + grantCallSlug)
        .send(updateGrantCall)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.body.success).toBeTruthy();
      expect(res.body.grantCallSlug).toEqual(grantCallSlug);
    });

    it('should fail if empty name is passed', async () => {
      const updateGrantCall = {
        name: '',
        description: 'More updates to the description',
      };

      const res = await request(app.getHttpServer())
        .patch('/grant-call/' + grantCallSlug)
        .send(updateGrantCall)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toContain('name should not be empty');
    });
  });

  describe('fetch grant applications', () => {
    it('should return grant applications for grant call filtered by status', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );
      // Change the status of one application to rejected
      testData.call1Application1.status = ApplicationStatus.REJECTED;
      await applicationRepo.save(testData.call1Application1);
      const sessionToken = generateSessionToken(jwtService, testUsers[0]);

      // Fetch the rejected application
      let res = await request(app.getHttpServer())
        .get(`/grant-call/${testData.call1.grantCallSlug}/grant-applications?status=${ApplicationStatus.REJECTED}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      let applications: GrantApplicationDto[] = res.body.data;
      // Check if all applications are returned
      expect(applications).toHaveLength(1);
      expect(applications[0].id).toEqual(testData.call1Application1.id);

      // Fetch the open applications
      res = await request(app.getHttpServer())
        .get(`/grant-call/${testData.call1.grantCallSlug}/grant-applications?status=${ApplicationStatus.OPEN}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      applications = res.body.data;
      // Check if all applications are returned
      expect(applications).toHaveLength(1);
      const applicationIds = applications.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application2.id);
    });

    it('should return grant applications filtered by assignee', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );

      // Fetch applications with assignee=me for user that is not a member of any application
      let sessionToken = generateSessionToken(jwtService, testUsers[0]);
      let res = await request(app.getHttpServer())
        .get(`/grant-call/${testData.call1.grantCallSlug}/grant-applications?assignee=me`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(0);

      // Set the assignee on the first application to another grant call member than the coordinator
      const grantCall = await grantCallRepo.findOneOrFail({
        where: { id: testData.call1.id },
        relations: { grantCallMembers: { user: true } },
      });
      testData.call1Application1.assignee = grantCall.grantCallMembers.find((m) => !m.isCoordinator);
      await applicationRepo.save(testData.call1Application1);
      // Fetch applications with assignee=me for user that is member of 1 application
      sessionToken = generateSessionToken(jwtService, testData.grantCallCoordinator1);
      res = await request(app.getHttpServer())
        .get(`/grant-call/${testData.call1.grantCallSlug}/grant-applications?assignee=me`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(1);
      const applicationIds = res.body.data.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application2.id);
    });

    it('should return grant applications sorted by creation date', async () => {
      const testData: TestData = await createTestData(
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        stageRepo,
        applicationRepo,
        stageLogRepo,
        applicationMemberRepo,
      );

      // Fetch all applications in ascending order
      let sessionToken = generateSessionToken(jwtService, testUsers[0]);
      let res = await request(app.getHttpServer())
        .get(`/grant-call/${testData.call1.grantCallSlug}/grant-applications?sort=created&direction=asc`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(2);
      let applications = res.body.data;
      let sorted = applications
        .slice()
        .sort(
          (a: GrantApplicationDto, b: GrantApplicationDto) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);

      // Fetch all applications in descending order
      res = await request(app.getHttpServer())
        .get(`/grant-call/${testData.call1.grantCallSlug}/grant-applications?sort=created&direction=desc`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(2);
      applications = res.body.data;
      sorted = applications
        .slice()
        .sort(
          (a: GrantApplicationDto, b: GrantApplicationDto) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);

      // Fetch applications with assignee=me for assignee with 2 applications in ascending order
      sessionToken = generateSessionToken(jwtService, testData.grantCallCoordinator1);
      res = await request(app.getHttpServer())
        .get(`/grant-call/${testData.call1.grantCallSlug}/grant-applications?assignee=me&sort=created&direction=asc`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body.success).toBeTruthy();
      expect(res.body.data).toHaveLength(2);
      applications = res.body.data;
      sorted = applications
        .slice()
        .sort(
          (a: GrantApplicationDto, b: GrantApplicationDto) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);
    });
  });

  afterAll(async () => {
    await app.close();
    await container.stop();
  });
});
