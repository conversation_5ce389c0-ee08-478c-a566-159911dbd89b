import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer';
import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import { PostgreSqlContainer, StartedPostgreSqlContainer } from '@testcontainers/postgresql';
import { SentMessageInfo } from 'nodemailer';
import { CreateGrantApplication } from '../src/grant-application/dto';
import request from 'supertest';
import { Repository } from 'typeorm';
import { AuthModule } from '../src/auth/auth.module';
import { User } from '../src/auth/entities/user.entity';
import { GrantApplicationStageLog } from '../src/grant-application/entities/application-stage-log.entity';
import { GrantApplicationStage } from '../src/grant-application/entities/application-stage.entity';
import { GrantApplicationMember } from '../src/grant-application/entities/grant-application-member.entity';
import { GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { GrantApplicationModule } from '../src/grant-application/grant-application.module';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../src/grant-call/grant-call.module';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantProgramModule } from '../src/grant-program/grant-program.module';
import { createTestData, generateSessionToken, generateSessionTokenEmailNotVerified, TestData } from './test.utils';
import { UserNotificationPreferences } from '../src/notifications/entities/user-notification-preferences.entity';
import { PhoneVerificationModule } from '../src/auth/phone-verification/phone-verification.module';
import { HederaService } from '../src/hedera/hedera.service';
import { KeyManagementService } from '../src/key-management/key-management.service';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';

// Mock the MailerService such that we can retrieve the content of sent emails.
class MockMailerService extends MailerService {
  newGrantApplicationEmail: { name: string; applicationTitle: string; applicationLink: string; grantCallName: string };
  grantCallInvitationEmails: { name: string; role: string; grantCallName: string; grantCallLink: string }[] = [];
  recoveryEmail: { name: string; url: string };
  otpEmail: { name: string; otp: string };
  applicationApprovedEmails: { name: string; applicationTitle: string; applicationLink: string }[] = [];
  applicationMovedEmails: { name: string; applicationTitle: string; applicationLink: string; stageTitle: string }[] =
    [];
  applicationRejectedEmails: { name: string; applicationTitle: string; applicationLink: string }[] = [];
  assigneeChangedEmails: { name: string; applicationTitle: string; applicationLink: string }[] = [];

  override async sendMail(options: ISendMailOptions): Promise<SentMessageInfo> {
    if (options.template === './new-grant-application.template.hbs') {
      this.newGrantApplicationEmail = options.context as any;
    } else if (options.template === './account-recovery.template.hbs') {
      this.recoveryEmail = options.context as any;
    } else if (options.template === './user-verification.template.hbs') {
      this.otpEmail = options.context as any;
    } else if (options.template === './grant-call-invitation.template.hbs') {
      this.grantCallInvitationEmails.push(options.context as any);
    } else if (options.template === './application-approved.template.hbs') {
      this.applicationApprovedEmails.push(options.context as any);
    } else if (options.template === './application-moved.template.hbs') {
      this.applicationMovedEmails.push(options.context as any);
    } else if (options.template === './application-rejected.template.hbs') {
      this.applicationRejectedEmails.push(options.context as any);
    } else if (options.template === './assignee-changed.template.hbs') {
      this.assigneeChangedEmails.push(options.context as any);
    } else {
      throw new Error(`Unknown template: ${options.template}`);
    }
    return null;
  }

  clearEmails() {
    this.newGrantApplicationEmail = undefined;
    this.recoveryEmail = undefined;
    this.otpEmail = undefined;
    this.grantCallInvitationEmails = [];
    this.applicationApprovedEmails = [];
    this.applicationMovedEmails = [];
    this.applicationRejectedEmails = [];
  }
}

describe('GrantApplication (e2e)', () => {
  let app: INestApplication;
  let container: StartedPostgreSqlContainer;
  let userRepository: Repository<User>;
  let grantProgramRepo: Repository<GrantProgram>;
  let grantCallRepo: Repository<GrantCall>;
  let stageRepo: Repository<GrantApplicationStage>;
  let applicationRepo: Repository<GrantApplication>;
  let stageLogRepo: Repository<GrantApplicationStageLog>;
  let applicationMemberRepo: Repository<GrantApplicationMember>;
  let jwtService: JwtService;
  let mockMailerService: MockMailerService;
  let testData: TestData;

  beforeAll(async () => {
    // Set necessary env vars
    process.env.JWT_EXPIRATION_TIME = '1d';
    process.env.JWT_SECRET = 'test';
    process.env.RECOVERY_TOKEN_EXPIRATION_TIME = '1d';
    process.env.FRONTEND_BASE_URL = 'https://localhost:3000';

    process.env.AWS_REGION = 'eu-central-2';

    container = await new PostgreSqlContainer().start();

    // Setup the testing module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AuthModule,
        GrantProgramModule,
        GrantCallModule,
        GrantApplicationModule,
        ConfigModule.forRoot({ isGlobal: true }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          url: container.getConnectionUri(),
          entities: [
            User,
            GrantProgram,
            GrantCall,
            GrantApplicationStage,
            GrantApplicationStageLog,
            GrantApplication,
            WorkflowState,
            WorkflowStepDefinition,
            WorkflowTemplate,
            GrantApplicationMember,
            UserNotificationPreferences,
            PhoneVerificationModule,
          ],
          synchronize: true,
        }),
      ],
    })
      .overrideProvider(MailerService)
      .useClass(MockMailerService)
      .overrideProvider(HederaService)
      .useValue({
        createTopic: jest.fn().mockResolvedValue({ toString: () => 'mock-topic-id' }),
        submitMessage: jest.fn(),
        getMessagesFromTopic: jest.fn(),
      })
      .overrideProvider(KeyManagementService)
      .useValue({
        signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')),
      })
      .compile();
    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    grantProgramRepo = moduleFixture.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    grantCallRepo = moduleFixture.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    stageRepo = moduleFixture.get<Repository<GrantApplicationStage>>(getRepositoryToken(GrantApplicationStage));
    applicationRepo = moduleFixture.get<Repository<GrantApplication>>(getRepositoryToken(GrantApplication));
    stageLogRepo = moduleFixture.get<Repository<GrantApplicationStageLog>>(
      getRepositoryToken(GrantApplicationStageLog),
    );
    applicationMemberRepo = moduleFixture.get<Repository<GrantApplicationMember>>(
      getRepositoryToken(GrantApplicationMember),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);
    mockMailerService = moduleFixture.get<MockMailerService>(MailerService);

    // Enable validation pipe because the one in main.ts is not used in tests
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();
  }, 100000);

  beforeEach(async () => {
    jest.clearAllMocks();
    await userRepository.manager.connection.dropDatabase();
    await userRepository.manager.connection.synchronize();
    testData = await createTestData(
      userRepository,
      grantProgramRepo,
      grantCallRepo,
      stageRepo,
      applicationRepo,
      stageLogRepo,
      applicationMemberRepo,
    );
    mockMailerService.clearEmails();
  });

  afterAll(async () => {
    await app.close();
    await container.stop();
  });

  it('should send OTP email correctly', async () => {
    // Authenticate the user and get the JWT token
    // Change the emailVerified flag to false on grantCallCoordinator1
    await userRepository.update(testData.grantCallCoordinator1.id, { emailVerified: false });
    const token = generateSessionTokenEmailNotVerified(jwtService, testData.grantCallCoordinator1);

    // Send OTP request
    const response = await request(app.getHttpServer())
      .post('/auth/send-otp')
      .set('Authorization', `Bearer ${token}`)
      .send();

    expect(response.status).toBe(201);
    expect(response.body).toEqual({
      success: true,
      message: 'OTP sent to the user.',
    });

    // Verify the email was sent with the correct context
    expect(mockMailerService.otpEmail.name).toBe(testData.grantCallCoordinator1.displayName);
    expect(mockMailerService.otpEmail.otp).toMatch(/^\d{6}$/);
  });

  it('should send recovery email correctly', async () => {
    // Authenticate the user and get the JWT token
    const token = generateSessionToken(jwtService, testData.grantCallCoordinator1);

    // Send recovery request
    const response = await request(app.getHttpServer())
      .post('/auth/recover-account')
      .set('Authorization', `Bearer ${token}`)
      .send({ email: testData.grantCallCoordinator1.email });

    expect(response.status).toBe(201);
    expect(response.body.success).toBeTruthy();

    // Verify the email was sent with the correct context
    expect(mockMailerService.recoveryEmail.name).toBe(testData.grantCallCoordinator1.displayName);
    expect(mockMailerService.recoveryEmail.url).toContain(
      `${process.env.FRONTEND_BASE_URL}/connect-wallet/recover-account?token=`,
    );
  });

  it('should send new application notification email correctly', async () => {
    // Authenticate the user and get the JWT token
    const token = generateSessionToken(jwtService, testData.call1Application1Creator);

    const title = 'My Grant Application';
    const creationDto: CreateGrantApplication = {
      grantCallSlug: testData.call1.grantCallSlug,
      title,
      description: 'In this project we will build a rocket to the moon.',
      companyName: 'Example Company',
      companyWebpage: 'https://www.example.com/',
      companyCountry: 'United States',
      industry: 'Technology',
      contactFullName: 'John Doe',
      contactEmail: '<EMAIL>',
      contactPhoneNumber: '+14 123 456 78 90',
    };

    const res = await request(app.getHttpServer())
      .post('/grant-application')
      .send(creationDto)
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toEqual(HttpStatus.CREATED);
    expect(res.body.success).toBeTruthy();
    expect(res.body.data).toBeDefined();
    expect(res.body.data.grantApplicationId).toBeDefined();

    expect(mockMailerService.newGrantApplicationEmail.name).toBe(testData.call1Application1Creator.displayName);
    expect(mockMailerService.newGrantApplicationEmail.applicationTitle).toBe(title);
    expect(mockMailerService.newGrantApplicationEmail.grantCallName).toBe(testData.call1.name);
    // Verify the link is correct
    const expectedLink = `${process.env.FRONTEND_BASE_URL}/grant-programs/${testData.grantProgram.grantProgramSlug}/grant-call/${testData.call1.grantCallSlug}/applications/${res.body.data.grantApplicationId.toString()}/manage`;
    expect(mockMailerService.newGrantApplicationEmail.applicationLink).toBe(expectedLink);
  });

  it('should send email when application is moved', async () => {
    // Authenticate the user and get the JWT token
    const token = generateSessionToken(jwtService, testData.grantCallCoordinator1);
    // Add another application member to application 1
    const member = new GrantApplicationMember();
    member.grantApplication = testData.call1Application1;
    member.user = testData.grantCallMember2;
    member.isCreator = false;
    await applicationMemberRepo.save(member);
    jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
    const updateRes = await request(app.getHttpServer())
      .post(`/grant-application/${testData.call1Application1.id}/change-stage`)
      .set('Authorization', `Bearer ${token}`)
      .send({ reason: 'Moving to next stage' });

    expect(updateRes.status).toBe(201);
    expect(updateRes.body.success).toBeTruthy();

    // Two emails should have been sent, one to the creator and one to the member
    expect(mockMailerService.applicationMovedEmails).toHaveLength(2);
    // Get the email that was sent to the creator
    const creatorEmail = mockMailerService.applicationMovedEmails.find(
      (email) => email.name === testData.call1Application1Creator.displayName,
    );
    expect(creatorEmail.stageTitle).toBe(testData.call1Stage1.title); // stage0 -> stage1
    expect(creatorEmail.applicationTitle).toBe(testData.call1Application1.title);
    expect(creatorEmail.applicationLink).toContain(process.env.FRONTEND_BASE_URL);
    // Get the email that was sent to the member
    const memberEmail = mockMailerService.applicationMovedEmails.filter(
      (email) => email.name === testData.grantCallMember2.displayName,
    );
    expect(memberEmail).toBeDefined();
    const expectedLink = `${process.env.FRONTEND_BASE_URL}/grant-programs/${testData.grantProgram.grantProgramSlug}/grant-call/${testData.call1.grantCallSlug}/applications/${testData.call1Application1.id}`;
    expect(memberEmail[0].applicationLink).toBe(expectedLink);
  });

  it('should send email when application is approved', async () => {
    // Authenticate the user and get the JWT token
    const token = generateSessionToken(jwtService, testData.grantCallCoordinator1);
    // Add another application member to application 2
    const member = new GrantApplicationMember();
    member.grantApplication = testData.call1Application2;
    member.user = testData.grantCallMember2;
    member.isCreator = false;
    await applicationMemberRepo.save(member);
    jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);

    let updateRes = await request(app.getHttpServer())
      .post(`/grant-application/${testData.call1Application2.id}/change-stage`)
      .set('Authorization', `Bearer ${token}`)
      .send({ reason: 'Approved' });
    expect(updateRes.status).toBe(201); // stage1 -> stage2
    updateRes = await request(app.getHttpServer())
      .post(`/grant-application/${testData.call1Application2.id}/change-stage`)
      .set('Authorization', `Bearer ${token}`)
      .send({ reason: 'Approved' });
    expect(updateRes.status).toBe(201); //stage2 -> approved

    // Two emails should have been sent, one to the creator and one to the member
    expect(mockMailerService.applicationApprovedEmails).toHaveLength(2);
    // Get the email that was sent to the creator
    const creatorEmail = mockMailerService.applicationApprovedEmails.find(
      (email) => email.name === testData.call1Application2Creator.displayName,
    );
    expect(creatorEmail.applicationTitle).toBe(testData.call1Application2.title);
    expect(creatorEmail.applicationLink).toContain(process.env.FRONTEND_BASE_URL);
    // Get the email that was sent to the member
    const memberEmail = mockMailerService.applicationApprovedEmails.filter(
      (email) => email.name === testData.grantCallMember2.displayName,
    );
    expect(memberEmail).toBeDefined();
    const expectedLink = `${process.env.FRONTEND_BASE_URL}/grant-programs/${testData.grantProgram.grantProgramSlug}/grant-call/${testData.call1.grantCallSlug}/applications/${testData.call1Application2.id}`;
    expect(memberEmail[0].applicationLink).toBe(expectedLink);
  });

  it('should send email when application is rejected', async () => {
    // Authenticate the user and get the JWT token
    const token = generateSessionToken(jwtService, testData.grantCallCoordinator1);
    // Add another application member to application 2
    const member = new GrantApplicationMember();
    member.grantApplication = testData.call1Application2;
    member.user = testData.grantCallMember2;
    member.isCreator = false;
    await applicationMemberRepo.save(member);
    jest.spyOn(app.get(HederaService), 'submitMessage').mockResolvedValueOnce(undefined);
    const updateRes = await request(app.getHttpServer())
      .patch(`/grant-application/${testData.call1Application2.id}/reject`)
      .set('Authorization', `Bearer ${token}`)
      .send({ reason: 'Rejected' });
    expect(updateRes.status).toBe(200); // stage1 -> stage2

    // Two emails should have been sent, one to the creator and one to the member
    expect(mockMailerService.applicationRejectedEmails).toHaveLength(2);
    // Get the email that was sent to the creator
    const creatorEmail = mockMailerService.applicationRejectedEmails.find(
      (email) => email.name === testData.call1Application2Creator.displayName,
    );
    expect(creatorEmail.applicationTitle).toBe(testData.call1Application2.title);
    expect(creatorEmail.applicationLink).toContain(process.env.FRONTEND_BASE_URL);
    // Get the email that was sent to the member
    const memberEmail = mockMailerService.applicationRejectedEmails.filter(
      (email) => email.name === testData.grantCallMember2.displayName,
    );
    expect(memberEmail).toBeDefined();
    const expectedLink = `${process.env.FRONTEND_BASE_URL}/grant-programs/${testData.grantProgram.grantProgramSlug}/grant-call/${testData.call1.grantCallSlug}/applications/${testData.call1Application2.id}`;
    expect(memberEmail[0].applicationLink).toBe(expectedLink);
  });
});
