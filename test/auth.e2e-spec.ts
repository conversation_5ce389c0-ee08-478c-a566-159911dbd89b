import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

import { PrivateKey } from '@hashgraph/sdk';
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import { PostgreSqlContainer, StartedPostgreSqlContainer } from '@testcontainers/postgresql';
import request from 'supertest';
import { User, UserRole } from '../src/auth/entities/user.entity';
import { AuthModule } from '../src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { SentMessageInfo } from 'nodemailer';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { prefixMessageToSign } from '../src/utils/hedera.util';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantApplicationStage } from '../src/grant-application/entities/application-stage.entity';
import { GrantApplicationStageLog } from '../src/grant-application/entities/application-stage-log.entity';
import { GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { GrantApplicationMember } from '../src/grant-application/entities/grant-application-member.entity';
import { MailService } from '../src/notifications/mail/mail.service';

import { generateSessionToken, generateSessionTokenEmailNotVerified } from './test.utils';
import { UserNotificationPreferences } from '../src/notifications/entities/user-notification-preferences.entity';
import { PhoneVerificationModule } from '../src/auth/phone-verification/phone-verification.module';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';

// Mock the MailService such that we can retrieve the content of sent emails.
class MockMailService extends MailService {
  private recoveryLink: string;
  private otp: string;

  getRecoveryLink() {
    return this.recoveryLink;
  }

  getOtp() {
    return this.otp;
  }

  override async sendAccountRecoveryEmail(user: User, recoveryLink: string): Promise<SentMessageInfo> {
    this.recoveryLink = recoveryLink;
    return true;
  }

  override async sendEmailVerification(user: User, otp: string): Promise<SentMessageInfo> {
    this.otp = otp;
    return true;
  }
}

// Note, that most of the tests are dependent on the order of execution, i.e., they depend on changes made by the
// previous tests.
describe('AuthController (e2e)', () => {
  let app: INestApplication;
  let container: StartedPostgreSqlContainer;
  let mockMailService: MockMailService;
  let userRepository: Repository<User>;
  let jwtService: JwtService;

  const testUserEmail = '<EMAIL>';

  // Generate a private key and account ID for testing (This is considered as primary wallet)
  const privateKey = PrivateKey.generateED25519();
  const accountId = '0.0.3780959';

  beforeAll(async () => {
    // Set necessary env vars
    process.env.JWT_EXPIRATION_TIME = '1d';
    process.env.CHALLENGE_TOKEN_EXPIRATION_TIME = '5m';
    process.env.RECOVERY_TOKEN_EXPIRATION_TIME = '5m';
    process.env.JWT_SECRET = 'test';
    process.env.FRONTEND_BASE_URL = 'https://localhost:3000';

    container = await new PostgreSqlContainer().start();
    // Setup the testing module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AuthModule,
        ConfigModule.forRoot({ isGlobal: true }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          url: container.getConnectionUri(),
          entities: [
            User,
            GrantProgram,
            GrantCall,
            GrantApplicationStage,
            GrantApplicationStageLog,
            GrantApplication,
            GrantApplicationMember,
            WorkflowState,
            WorkflowStepDefinition,
            WorkflowTemplate,
            UserNotificationPreferences,
            PhoneVerificationModule,
          ],
          synchronize: true,
        }),
      ],
    })
      .overrideProvider(MailService)
      .useClass(MockMailService)
      .compile();
    app = moduleFixture.createNestApplication();
    mockMailService = moduleFixture.get<MockMailService>(MailService);
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    jwtService = moduleFixture.get<JwtService>(JwtService);
    await app.init();
  }, 60000);

  it('should request a challenge and create account for the user', async () => {
    // Request a challenge
    let res = await request(app.getHttpServer())
      .get('/auth/request-challenge')
      .query({ address: accountId })
      .set('Referer', 'http://localhost:3000/connect-wallet');

    expect(res.status).toEqual(200);
    expect(res.body).toEqual({
      success: true,
      challenge: expect.any(String),
      challengeToken: expect.any(String),
      accountExists: false,
      accountDeleted: false,
    });

    // Mock the mirror node response
    const mirrorNodeAccountResponse = {
      key: { _type: 'ED25519', key: privateKey.publicKey.toStringRaw() },
    };
    jest.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mirrorNodeAccountResponse)));

    // Create a signature and call the signature verification endpoint
    const prefixedChallengeBytes = Buffer.from(prefixMessageToSign(res.body.challenge));
    const sigHex = '0x' + Buffer.from(privateKey.sign(prefixedChallengeBytes)).toString('hex');

    // Create an account for the user
    res = await request(app.getHttpServer()).post('/auth/sign-up').send({
      address: accountId,
      email: testUserEmail,
      displayName: 'Test User',
      challengeToken: res.body.challengeToken,
      signature: sigHex,
    });
    expect(res.status).toEqual(201);
    expect(res.body).toEqual({
      success: true,
      message: 'User created successfully.',
    });

    // Check if the user exists in the database
    const exists = await userRepository.exists({ where: { email: testUserEmail } });
    expect(exists).toBe(true);
  });

  it('should request a challenge and sign in the user', async () => {
    // Request a challenge
    let res = await request(app.getHttpServer())
      .get('/auth/request-challenge')
      .query({ address: accountId })
      .set('Referer', 'http://localhost:3000/connect-wallet');

    expect(res.status).toEqual(200);
    expect(res.body).toEqual({
      success: true,
      challenge: expect.any(String),
      challengeToken: expect.any(String),
      accountExists: true,
      accountDeleted: false,
    });

    // Mock the mirror node response
    const mirrorNodeAccountResponse = {
      key: { _type: 'ED25519', key: privateKey.publicKey.toStringRaw() },
    };
    jest.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mirrorNodeAccountResponse)));

    // Create a signature and call the signature verification endpoint
    const prefixedChallengeBytes = Buffer.from(prefixMessageToSign(res.body.challenge));
    const sigHex = '0x' + Buffer.from(privateKey.sign(prefixedChallengeBytes)).toString('hex');

    // Sign in the user
    res = await request(app.getHttpServer()).post('/auth/sign-in').send({
      address: accountId,
      challengeToken: res.body.challengeToken,
      signature: sigHex,
    });

    const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });

    expect(res.status).toEqual(201);
    expect(res.body).toEqual({
      success: true,
      sessionToken: expect.any(String),
      user: {
        id: user.id,
        addresses: user.addresses,
        email: user.email,
        phoneNumber: user.phoneNumber,
        emailVerified: user.emailVerified,
        isPhoneVerified: user.isPhoneVerified,
        displayName: user.displayName,
        role: user.role,
        createdAt: user.createdAt.toISOString(),
      },
    });
    expect(jwtService.decode(res.body.sessionToken).payload.emailVerified).toBeFalsy();
  });

  it('should set the right role on the session token', async () => {
    // Update the user's role to GRANT_PROGRAM_COORDINATOR to check if the role is correct in the session token
    await userRepository.update({ email: testUserEmail }, { role: UserRole.GRANT_PROGRAM_COORDINATOR });

    // Request a challenge
    let res = await request(app.getHttpServer())
      .get('/auth/request-challenge')
      .query({ address: accountId })
      .set('Referer', 'http://localhost:3000/connect-wallet');

    expect(res.status).toEqual(200);
    expect(res.body).toEqual({
      success: true,
      challenge: expect.any(String),
      challengeToken: expect.any(String),
      accountExists: true,
      accountDeleted: false,
    });

    // Mock the mirror node response
    const mirrorNodeAccountResponse = {
      key: { _type: 'ED25519', key: privateKey.publicKey.toStringRaw() },
    };
    jest.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mirrorNodeAccountResponse)));

    // Create a signature and call the signature verification endpoint
    const prefixedChallengeBytes = Buffer.from(prefixMessageToSign(res.body.challenge));
    const sigHex = '0x' + Buffer.from(privateKey.sign(prefixedChallengeBytes)).toString('hex');

    // Sign in the user
    res = await request(app.getHttpServer()).post('/auth/sign-in').send({
      address: accountId,
      challengeToken: res.body.challengeToken,
      signature: sigHex,
    });

    // Fetch the session token and check if the role is correct
    const jwtPayload = jwtService.decode(res.body.sessionToken);
    expect(jwtPayload.payload.role).toEqual(UserRole.GRANT_PROGRAM_COORDINATOR);

    // Reset the user's role to USER
    await userRepository.update({ email: testUserEmail }, { role: UserRole.USER });
  });

  it('should fail getting the user details without verification', async () => {
    const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    expect(user.emailVerified).toBeFalsy();
    const authToken = generateSessionTokenEmailNotVerified(jwtService, user);

    const res = await request(app.getHttpServer()).get('/auth/user').set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toEqual(401);
    expect(res.body.message).toEqual('Email not verified');
  });

  it('should request for a OTP and verify the user', async () => {
    let user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    expect(user.emailVerified).toBeFalsy();
    const authToken = generateSessionTokenEmailNotVerified(jwtService, user);

    let res = await request(app.getHttpServer()).post('/auth/send-otp').set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toEqual(201);
    expect(res.body).toEqual({
      success: true,
      message: 'OTP sent to the user.',
    });

    res = await request(app.getHttpServer())
      .post('/auth/verify-otp')
      .send({ otp: mockMailService.getOtp().toString() })
      .set('Authorization', `Bearer ${authToken}`);

    expect(res.status).toEqual(201);
    expect(res.body).toEqual({
      success: true,
      sessionToken: expect.any(String),
    });
    expect(jwtService.decode(res.body.sessionToken).payload.emailVerified).toBeTruthy();

    // Check if the user's email address is verified
    user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    expect(user.emailVerified).toBe(true);
  });

  it('should fail sending OTP email to user that already has verified email', async () => {
    const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    expect(user.emailVerified).toBeTruthy();

    const authToken = generateSessionToken(jwtService, user);

    const res = await request(app.getHttpServer()).post('/auth/send-otp').set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toEqual(400);
    expect(res.body.message).toEqual('User email already verified.');
  });

  it('should add another wallet address to the user', async () => {
    const secondaryPrivateKey = PrivateKey.generateED25519();
    const secondaryAccountId = '0.0.4293821';
    // Request a challenge
    let res = await request(app.getHttpServer())
      .get('/auth/request-challenge')
      .query({ address: secondaryAccountId.toString() })
      .set('Referer', 'http://localhost:3000/connect-wallet');

    expect(res.status).toEqual(200);
    expect(res.body).toEqual({
      success: true,
      challenge: expect.any(String),
      challengeToken: expect.any(String),
      accountExists: false,
      accountDeleted: false,
    });

    // Mock the mirror node response
    const mirrorNodeAccountResponse = {
      key: { _type: 'ED25519', key: secondaryPrivateKey.publicKey.toStringRaw() },
    };
    jest.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mirrorNodeAccountResponse)));

    // Create a signature and call the signature verification endpoint
    const prefixedChallengeBytes = Buffer.from(prefixMessageToSign(res.body.challenge));
    const sigHex = '0x' + Buffer.from(secondaryPrivateKey.sign(prefixedChallengeBytes)).toString('hex');

    let user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    const authToken = generateSessionToken(jwtService, user);

    // Add a new wallet to the user's account
    res = await request(app.getHttpServer()).post('/auth/add-wallet').set('Authorization', `Bearer ${authToken}`).send({
      address: secondaryAccountId.toString(),
      challengeToken: res.body.challengeToken,
      signature: sigHex,
    });

    expect(res.status).toEqual(201);
    expect(res.body).toEqual({
      success: true,
      message: 'Wallet successfully added to the account.',
    });

    // Check if the secondary wallet address is added to the user's account
    user = await userRepository.findOne({ where: { email: testUserEmail } });
    expect(user.addresses).toEqual(expect.arrayContaining([secondaryAccountId.toString()]));
  });

  it('should check that the user has multiple wallets', async () => {
    const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    const authToken = generateSessionToken(jwtService, user);

    const res = await request(app.getHttpServer()).get('/auth/user').set('Authorization', `Bearer ${authToken}`);

    expect(res.status).toEqual(200);
    expect(res.body).toHaveProperty('success', true);
    expect(res.body.user.addresses).toHaveLength(2);
  });

  it("should send a recovery email to the user and update the user's account with new wallet", async () => {
    let res = await request(app.getHttpServer()).post('/auth/recover-account').send({ email: testUserEmail });

    expect(res.status).toEqual(201);
    expect(res.body).toEqual({
      success: true,
      message: 'Recovery email sent to the user.',
    });

    const tertiaryPrivateKey = PrivateKey.generateED25519();
    const tertiaryAccountId = '0.0.8139012';

    // Request a challenge
    res = await request(app.getHttpServer())
      .get('/auth/request-challenge')
      .query({ address: tertiaryAccountId.toString() })
      .set('Referer', 'http://localhost:3000/connect-wallet');

    expect(res.status).toEqual(200);
    expect(res.body).toEqual({
      success: true,
      challenge: expect.any(String),
      challengeToken: expect.any(String),
      accountExists: false,
      accountDeleted: false,
    });

    // Mock the mirror node response
    const mirrorNodeAccountResponse = {
      key: { _type: 'ED25519', key: tertiaryPrivateKey.publicKey.toStringRaw() },
    };
    jest.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mirrorNodeAccountResponse)));

    // Create a signature and call the signature verification endpoint
    const prefixedChallengeBytes = Buffer.from(prefixMessageToSign(res.body.challenge));
    const sigHex = '0x' + Buffer.from(tertiaryPrivateKey.sign(prefixedChallengeBytes)).toString('hex');

    // Get recovery token from email
    const validRecoveryToken = mockMailService.getRecoveryLink().split('?token=')[1];

    // Update the user's account with the new wallet
    res = await request(app.getHttpServer()).post('/auth/update-wallet').send({
      recoveryToken: validRecoveryToken,
      address: tertiaryAccountId.toString(),
      challengeToken: res.body.challengeToken,
      signature: sigHex,
    });

    expect(res.status).toEqual(201);
    expect(res.body).toEqual({
      success: true,
      message: 'Wallet address updated successfully.',
    });

    const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    expect(user.addresses).toHaveLength(1); // The previous addresses should be removed
    expect(user.addresses).toEqual(expect.arrayContaining([tertiaryAccountId.toString()]));
  });

  it('should check that the user has new wallet', async () => {
    const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
    const authToken = generateSessionToken(jwtService, user);

    const res = await request(app.getHttpServer()).get('/auth/user').set('Authorization', `Bearer ${authToken}`);

    expect(res.status).toEqual(200);
    expect(res.body).toHaveProperty('success', true);
    expect(res.body.user.addresses).toHaveLength(1);
  });

  describe('User exists', () => {
    it('should check if the given user exists', async () => {
      const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
      const authToken = generateSessionToken(jwtService, user);

      // Encode the email address to base64
      const base64Email = Buffer.from('<EMAIL>').toString('base64');
      const res = await request(app.getHttpServer())
        .get(`/auth/user/exists?email=${base64Email}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toEqual(200);
      expect(res.body.success).toBeTruthy();
      expect(res.body.exists).toBeTruthy();
    });

    it('should check if the given user does not exists', async () => {
      const user = await userRepository.findOneOrFail({ where: { email: testUserEmail } });
      const authToken = generateSessionToken(jwtService, user);

      const base64Email = Buffer.from('<EMAIL>').toString('base64');
      const res = await request(app.getHttpServer())
        .get(`/auth/user/exists?email=${base64Email}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toEqual(200);
      expect(res.body.success).toBeTruthy();
      expect(res.body.exists).toBeFalsy();
    });
  });

  afterAll(async () => {
    await app.close();
    await container.stop();
  });
});
