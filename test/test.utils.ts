import { ApplicationStatus, GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { User, UserRole } from '../src/auth/entities/user.entity';

import { BusinessCategory } from '../src/grant-call/enums/business-category.enum';
import { GrantApplicationMember } from '../src/grant-application/entities/grant-application-member.entity';
import { GrantApplicationStage } from '../src/grant-application/entities/application-stage.entity';
import { GrantApplicationStageLog } from '../src/grant-application/entities/application-stage-log.entity';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { JwtScope } from '../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm/repository/Repository';
import { StageCode } from '../src/workflow/enums/stage-code.enum';
import { WorkflowEntityType } from '../src/workflow/enums/workflow-entity-type.enum';
import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { WorkflowStatus } from '../src/workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';

export type TestUser = {
  email: string;
  displayName: string;
  address: string;
};

export function createTestUsers(testUsers: TestUser[], userRepository: Repository<User>): Promise<User[]> {
  return Promise.all(
    testUsers.map(async (testUser) => {
      const user = userRepository.create({
        email: testUser.email,
        displayName: testUser.displayName,
        addresses: [testUser.address],
        emailVerified: true,
        role: UserRole.GRANT_PROGRAM_COORDINATOR,
      });
      return userRepository.save(user);
    }),
  );
}

export function createTestUser(user: TestUser, userRepository: Repository<User>): Promise<User> {
  const newUser = userRepository.create({
    email: user.email,
    displayName: user.displayName,
    addresses: [user.address],
    emailVerified: true,
    role: UserRole.GRANT_PROGRAM_COORDINATOR,
  });
  return userRepository.save(newUser);
}

export async function createTestGrantProgram(
  slug: string,
  grantProgramCoordinator: User,
  grantProgramRepo: Repository<GrantProgram>,
): Promise<GrantProgram> {
  // Create workflow state first
  const entityManager = grantProgramRepo.manager;
  const wfStateRepo = entityManager.getRepository(WorkflowState);
  const stepDefRepo = entityManager.getRepository(WorkflowStepDefinition);
  const wfTemplateRepo = entityManager.getRepository(WorkflowTemplate);

  // Find default step definition for GP_OPEN
  const defaultOpenStepDef = await stepDefRepo.findOne({ where: { code: StageCode.GP_OPEN } });

  // Find workflow template for PROGRAM entity type
  const workflowTemplate = await wfTemplateRepo.findOneBy({
    entityType: WorkflowEntityType.PROGRAM,
  });

  // Create initial workflow state
  const initialWorkflowState = wfStateRepo.create({
    workflowTemplate,
    currentStepDefinitionId: defaultOpenStepDef.id,
    currentStepDefinition: defaultOpenStepDef,
    currentStepTransitionedAt: new Date(),
    currentStepEndsAt: null,
    status: WorkflowStatus.IN_PROGRESS,
  });

  await wfStateRepo.save(initialWorkflowState);

  // Create the grant program with the workflow state
  const program = grantProgramRepo.create({
    grantProgramSlug: slug,
    name: 'Test Grant Program',
    description: 'This is a test grant program',
    scope: 'Test Scope',
    budget: 10000,
    grantorPublicProfileName: 'Test Grantor',
    grantorLogoURL: 'https://example.com/logo.jpg',
    grantProgramCoordinator: grantProgramCoordinator,
    grantorDescription: 'Test Grantor Description',
    grantorWebsite: 'example.com',
    workflowState: initialWorkflowState,
  });

  return grantProgramRepo.save(program);
}

/**
 * Creates and stores a grant call instance.
 *
 * @param slug the slug of the grant call
 * @param grantProgram the grant program to which the grant call belongs
 * @param creatorUser the user who created the grant call
 * @param grantCallRepo the repository to save the grant call
 * @returns the created grant call
 */
export async function createTestGrantCall(
  slug: string,
  grantProgram: GrantProgram,
  creatorUser: User,
  grantCallRepo: Repository<GrantCall>,
): Promise<GrantCall> {
  const entityManager = grantCallRepo.manager;
  const wfStateRepo = entityManager.getRepository(WorkflowState);
  const stepDefRepo = entityManager.getRepository(WorkflowStepDefinition);
  const wfTemplateRepo = entityManager.getRepository(WorkflowTemplate);

  // Find default step definition for GP_OPEN
  const defaultOpenStepDef = await stepDefRepo.findOne({ where: { code: StageCode.GC_CLOSED } });

  // Find workflow template for PROGRAM entity type
  const workflowTemplate = await wfTemplateRepo.findOneBy({
    entityType: WorkflowEntityType.CALL,
  });

  // Create initial workflow state
  const initialWorkflowState = wfStateRepo.create({
    workflowTemplate,
    currentStepDefinitionId: defaultOpenStepDef.id,
    currentStepDefinition: defaultOpenStepDef,
    currentStepTransitionedAt: new Date(),
    currentStepEndsAt: null,
    status: WorkflowStatus.IN_PROGRESS,
  });

  await wfStateRepo.save(initialWorkflowState);

  const call = grantCallRepo.create({
    grantCallSlug: slug,
    grantProgram: grantProgram,
    name: 'Test Grant Call',
    description: 'This is a test grant call',
    businessCategory: BusinessCategory.STARTUP,
    createdById: creatorUser.id,
    createdBy: creatorUser,
    categories: ['Test Category'],
    totalGrantAmount: 10000,
    workflowState: initialWorkflowState,
  });

  return grantCallRepo.save(call);
}

export function createTestApplicationStage(
  position: number,
  grantCall: GrantCall,
  stageRepo: Repository<GrantApplicationStage>,
): Promise<GrantApplicationStage> {
  // Creating a new stage using any() to bypass type checking for fields that exist in the database but not in TypeORM entity
  return stageRepo.save({
    title: `Stage ${position}`,
    subtitle: `Stage ${position} Subtitle`,
    description: `Stage ${position} Description`,
    expectedDuration: 3,
    position: position,
    grantCall: grantCall,
  } as any);
}

export async function createTestGrantApplication(
  title: string,
  grantCall: GrantCall,
  applicationRepo: Repository<GrantApplication>,
  applicationMembersRepo?: Repository<GrantApplicationMember>,
  userRepo?: Repository<User>,
  creatorId?: number,
  memberId?: number,
): Promise<GrantApplication> {
  let application = applicationRepo.create({
    title: title,
    description: 'This is a test application: ' + title,
    companyName: 'Test Company',
    companyWebpage: 'https://example.com',
    companyCountry: 'Test Country',
    industry: 'Test Industry',
    contactFullName: 'Test Contact',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '1234567890',
    status: ApplicationStatus.OPEN,
    statusChangedOn: new Date(),
    grantCall: grantCall,
  });

  if (creatorId) {
    const creator = await userRepo.findOne({ where: { id: creatorId } });
    const applicationCreator = new GrantApplicationMember();
    applicationCreator.user = creator;
    applicationCreator.isCreator = true;
    await applicationMembersRepo.save(applicationCreator);
    application = {
      ...application,
      grantApplicationMembers: [applicationCreator],
    };
  }

  if (memberId) {
    const member = await userRepo.findOne({ where: { id: memberId } });
    const applicationMember = new GrantApplicationMember();
    applicationMember.user = member;
    applicationMember.isCreator = false;
    await applicationMembersRepo.save(applicationMember);
    application.grantApplicationMembers.push(applicationMember);
  }

  return applicationRepo.save(application);
}

export function createTestApplicationStageLog(
  stage: GrantApplicationStage,
  application: GrantApplication,
  logRepo: Repository<GrantApplicationStageLog>,
): Promise<GrantApplicationStageLog> {
  const progress = logRepo.create({
    startedOn: new Date(),
    finishedOn: null,
    grantApplication: application,
    stage: stage,
  });
  return logRepo.save(progress);
}

export function generateSessionToken(jwtService: JwtService, user: User) {
  return jwtService.sign({
    payload: { id: user.id, role: user.role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
  });
}

export function generateSessionTokenEmailNotVerified(jwtService: JwtService, user: User) {
  return jwtService.sign({
    payload: { id: user.id, role: user.role, scope: JwtScope.SESSION_TOKEN, emailVerified: false },
  });
}

export function createVerifiedUserRandomly(
  userRepository: Repository<User>,
  role: UserRole = UserRole.GRANT_PROGRAM_COORDINATOR,
): Promise<User> {
  const randomNumber = Math.floor(100000 + Math.random() * 900000);
  return userRepository.save(
    userRepository.create({
      email: `user${randomNumber}@example.com`,
      displayName: 'User ' + randomNumber,
      addresses: [`0.0.${randomNumber}`],
      emailVerified: true,
      role: role,
    }),
  );
}

export function createGrantApplicationRandomly(
  grantCall: GrantCall,
  status: ApplicationStatus = ApplicationStatus.OPEN,
  applicationRepo: Repository<GrantApplication>,
): Promise<GrantApplication> {
  const randomNumber = Math.floor(100000 + Math.random() * 900000);
  return applicationRepo.save(
    applicationRepo.create({
      title: 'Test Grant Application ' + randomNumber,
      description: 'This is a test application ' + randomNumber,
      companyName: 'Test Company ' + randomNumber,
      companyWebpage: `https://example-${randomNumber}.com`,
      companyCountry: 'Test Country ' + randomNumber,
      industry: 'Test Industry',
      contactFullName: 'Test Contact ' + randomNumber,
      contactEmail: randomNumber + '@example.com',
      contactPhoneNumber: randomNumber.toString(),
      status: status,
      statusChangedOn: new Date(),
      grantCall: grantCall,
    }),
  );
}

export type TestData = {
  programCoordinator: User;
  grantProgram: GrantProgram;
  call1: GrantCall;
  grantCallCoordinator1: User;
  grantCallMember1: User;
  call1Stage0: GrantApplicationStage;
  call1Stage1: GrantApplicationStage;
  call1Stage2: GrantApplicationStage;
  call1Application1: GrantApplication;
  call1Application1Creator: User;
  call1Application2: GrantApplication;
  call1Application2Creator: User;
  call2: GrantCall;
  grantCallCoordinator2: User;
  grantCallMember2: User;
  call2Stage0: GrantApplicationStage;
  call2Stage1: GrantApplicationStage;
  call2Stage2: GrantApplicationStage;
  call2Application1: GrantApplication;
  call2Application1Creator: User;
  call2Application2: GrantApplication;
  call2Application2Creator: User;
};

// Creates:
// - Grant program, which is open and has a coordinator
// - Two Grant calls that are open. Start date 1 month in the past. End date in a year from now.
// - Grant call member and a coordinator per grant call
// - Three stages per grant call
// - Two grant applications per grant call. First application is in first stage (0) and second application is in second
//   (1) stage. -> 4 applications in total
// - Grant call coordinators are the assignees in both applications respectively.
// - Each application has a different creator user, but, no other members.
export async function createTestData(
  userRepository: Repository<User>,
  grantProgramRepo: Repository<GrantProgram>,
  grantCallRepo: Repository<GrantCall>,
  stageRepo: Repository<GrantApplicationStage>,
  applicationRepo: Repository<GrantApplication>,
  logRepo: Repository<GrantApplicationStageLog>,
  applicationMemberRepo: Repository<GrantApplicationMember>,
): Promise<TestData> {
  // Create grant program
  const programCoordinator: User = await createVerifiedUserRandomly(userRepository);
  let randomNumber = Math.floor(100000 + Math.random() * 900000);
  const grantProgram = await grantProgramRepo.save(
    grantProgramRepo.create({
      grantProgramSlug: 'test-grant-program-' + randomNumber,
      name: 'Test Grant Program ' + randomNumber,
      description: 'This is a test grant program ' + randomNumber,
      scope: 'Test Scope',
      budget: randomNumber,
      grantorPublicProfileName: 'Test Grantor ' + randomNumber,
      grantorLogoURL: `https://example.com/logo-${randomNumber}.jpg`,
      grantProgramCoordinator: programCoordinator,
      grantorDescription: 'Test Grantor Description ' + randomNumber,
      grantorWebsite: 'https://example.com' + randomNumber,
    }),
  );

  // Create grant call
  const grantCallCoordinator1: User = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  const grantCallMember1: User = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  randomNumber = Math.floor(100000 + Math.random() * 900000);

  // Create the grant call with the coordinator as creator
  let call1 = await grantCallRepo.save(
    grantCallRepo.create({
      grantCallSlug: 'test-grant-call-' + randomNumber,
      grantProgram: grantProgram,
      name: 'Test Grant Call ' + randomNumber,
      description: 'This is a test grant call ' + randomNumber,
      businessCategory: BusinessCategory.STARTUP,
      categories: ['Test Industry ' + randomNumber],
      totalGrantAmount: randomNumber + 10000,
      createdById: grantCallCoordinator1.id,
      createdBy: grantCallCoordinator1,
    }),
  );

  // Create a single instance and make sure it's a GrantCall, not an array
  call1 = await grantCallRepo.findOneOrFail({
    where: { id: call1.id },
  });

  // Create grant application stages
  const call1Stage0: GrantApplicationStage = await stageRepo.save({
    title: `Stage 0`,
    subtitle: `Stage 0 Subtitle`,
    description: `Stage 0 Description`,
    expectedDuration: 3,
    position: 0,
    grantCall: call1,
  } as any);

  const call1Stage1: GrantApplicationStage = await stageRepo.save({
    title: `Stage 1`,
    subtitle: `Stage 1 Subtitle`,
    description: `Stage 1 Description`,
    expectedDuration: 3,
    position: 1,
    grantCall: call1,
  } as any);

  const call1Stage2: GrantApplicationStage = await stageRepo.save({
    title: `Stage 2`,
    subtitle: `Stage 2 Subtitle`,
    description: `Stage 2 Description`,
    expectedDuration: 3,
    position: 2,
    grantCall: call1,
  } as any);

  // Create grant application in first stage
  const call1Application1: GrantApplication = await createGrantApplicationRandomly(
    call1,
    ApplicationStatus.OPEN,
    applicationRepo,
  );
  await logRepo.save(
    logRepo.create({
      startedOn: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      finishedOn: null,
      grantApplication: call1Application1,
      stage: call1Stage0,
    }),
  );
  const call1Application1Creator = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  await applicationMemberRepo.save(
    applicationMemberRepo.create({
      user: call1Application1Creator,
      grantApplication: call1Application1,
      isCreator: true,
    }),
  );

  // Create grant application in second stage
  const call1Application2: GrantApplication = await createGrantApplicationRandomly(
    call1,
    ApplicationStatus.OPEN,
    applicationRepo,
  );
  await logRepo.save(
    logRepo.create({
      startedOn: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      finishedOn: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      grantApplication: call1Application2,
      stage: call1Stage0,
    }),
  );
  await logRepo.save(
    logRepo.create({
      startedOn: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      finishedOn: null,
      grantApplication: call1Application2,
      stage: call1Stage1,
    }),
  );
  const call1Application2Creator = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  await applicationMemberRepo.save(
    applicationMemberRepo.create({
      user: call1Application2Creator,
      grantApplication: call1Application2,
      isCreator: true,
    }),
  );

  // Create another grant call
  const grantCallCoordinator2: User = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  const grantCallMember2: User = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  randomNumber = Math.floor(100000 + Math.random() * 900000);
  let call2 = await grantCallRepo.save(
    grantCallRepo.create({
      grantCallSlug: 'test-grant-call-' + randomNumber,
      grantProgram: grantProgram,
      name: 'Test Grant Call ' + randomNumber,
      description: 'This is a test grant call ' + randomNumber,
      businessCategory: BusinessCategory.STARTUP,
      categories: ['Test Industry ' + randomNumber],
      totalGrantAmount: randomNumber + 10000,
      createdById: grantCallCoordinator2.id,
      createdBy: grantCallCoordinator2,
    }),
  );

  // Create a single instance and make sure it's a GrantCall, not an array
  call2 = await grantCallRepo.findOneOrFail({
    where: { id: call2.id },
  });

  // Create grant application stages for the new grant call
  const call2Stage0: GrantApplicationStage = await stageRepo.save({
    title: `Stage 0`,
    subtitle: `Stage 0 Subtitle`,
    description: `Stage 0 Description`,
    expectedDuration: 3,
    position: 0,
    grantCall: call2,
  } as any);

  const call2Stage1: GrantApplicationStage = await stageRepo.save({
    title: `Stage 1`,
    subtitle: `Stage 1 Subtitle`,
    description: `Stage 1 Description`,
    expectedDuration: 3,
    position: 1,
    grantCall: call2,
  } as any);

  const call2Stage2: GrantApplicationStage = await stageRepo.save({
    title: `Stage 2`,
    subtitle: `Stage 2 Subtitle`,
    description: `Stage 2 Description`,
    expectedDuration: 3,
    position: 2,
    grantCall: call2,
  } as any);

  // Create first grant application in first stage
  const call2Application1: GrantApplication = await createGrantApplicationRandomly(
    call2,
    ApplicationStatus.OPEN,
    applicationRepo,
  );

  await logRepo.save(
    logRepo.create({
      startedOn: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      finishedOn: null,
      grantApplication: call2Application1,
      stage: call2Stage0,
    }),
  );

  const call2Application1Creator = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  await applicationMemberRepo.save(
    applicationMemberRepo.create({
      user: call2Application1Creator,
      grantApplication: call2Application1,
      isCreator: true,
    }),
  );

  // Create second grant application in second stage
  const call2Application2: GrantApplication = await createGrantApplicationRandomly(
    call2,
    ApplicationStatus.OPEN,
    applicationRepo,
  );

  await logRepo.save(
    logRepo.create({
      startedOn: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      finishedOn: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      grantApplication: call2Application2,
      stage: call2Stage0,
    }),
  );

  await logRepo.save(
    logRepo.create({
      startedOn: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      finishedOn: null,
      grantApplication: call2Application2,
      stage: call2Stage1,
    }),
  );

  const call2Application2Creator = await createVerifiedUserRandomly(userRepository, UserRole.USER);
  await applicationMemberRepo.save(
    applicationMemberRepo.create({
      user: call2Application2Creator,
      grantApplication: call2Application2,
      isCreator: true,
    }),
  );

  return {
    programCoordinator,
    grantProgram,
    call1,
    grantCallCoordinator1,
    grantCallMember1,
    call1Stage0,
    call1Stage1,
    call1Stage2,
    call1Application1,
    call1Application1Creator,
    call1Application2,
    call1Application2Creator,
    call2,
    grantCallCoordinator2,
    grantCallMember2,
    call2Stage0,
    call2Stage1,
    call2Stage2,
    call2Application1,
    call2Application1Creator,
    call2Application2,
    call2Application2Creator,
  };
}
