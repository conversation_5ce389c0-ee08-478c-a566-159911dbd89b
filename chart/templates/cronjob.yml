{{- if .Values.cronJobs -}}
{{- $general := $.Values.cronJobsGeneral -}}
{{- $cronJobs := list -}}
{{- if kindIs "string" .Values.cronJobs -}}
{{- $cronJobs = fromYaml .Values.cronJobs -}}
{{- else if kindIs "map" .Values.cronJobs -}}
{{- $cronJobs = .Values.cronJobs -}}
{{- end -}}
{{- range $name, $job := $cronJobs }}
{{- $cjName := include "helpers.app.fullname" (dict "name" $name "context" $) }}
---
apiVersion: {{ include "helpers.capabilities.cronJob.apiVersion" $ }}
kind: CronJob
metadata:
  name: {{ $cjName }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with $general.labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
    {{- with .labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
  annotations:
    {{- include "helpers.app.genericAnnotations" $ | indent 4 }}
    {{- with $general.annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
    {{- with .annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
spec:
  schedule: {{ include "helpers.tplvalues.render" (dict "value" .schedule "context" $) | quote }}
  {{- if .singleOnly }}
  concurrencyPolicy: Forbid
  {{- end }}
  {{- if .startingDeadlineSeconds }}
  startingDeadlineSeconds: {{ .startingDeadlineSeconds }}
  {{- else if $general.startingDeadlineSeconds }}
  startingDeadlineSeconds: {{ $general.startingDeadlineSeconds }}
  {{- end }}
  {{- if .successfulJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ .successfulJobsHistoryLimit }}
  {{- else if $general.successfulJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ $general.successfulJobsHistoryLimit }}
  {{- end }}
  {{- if .failedJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ .failedJobsHistoryLimit }}
  {{- else if $general.failedJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ $general.failedJobsHistoryLimit }}
  {{- end }}
  jobTemplate:
    spec:
      {{- if .parallelism }}
      parallelism: {{ .parallelism }}
      {{- else if $general.parallelism }}
      parallelism: {{ $general.parallelism }}
      {{- end }}
      {{- if .completions }}
      completions: {{ .completions }}
      {{- else if $general.completions }}
      completions: {{ $general.completions }}
      {{- end }}
      {{- if .activeDeadlineSeconds }}
      activeDeadlineSeconds: {{ .activeDeadlineSeconds }}
      {{- else if $general.activeDeadlineSeconds }}
      activeDeadlineSeconds: {{ $general.activeDeadlineSeconds }}
      {{- end }}
      {{- if .backoffLimit }}
      backoffLimit: {{ .backoffLimit }}
      {{- else if $general.backoffLimit }}
      backoffLimit: {{ $general.backoffLimit }}
      {{- end }}
      {{- if .ttlSecondsAfterFinished }}
      ttlSecondsAfterFinished: {{ .ttlSecondsAfterFinished }}
      {{- else if $general.ttlSecondsAfterFinished }}
      ttlSecondsAfterFinished: {{ $general.ttlSecondsAfterFinished }}
      {{- end }}
      template:
        metadata:
          labels:
            {{- with $.Values.generic.podLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 12 }}{{- end }}
            {{- with .podLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 12 }}{{- end }}
          annotations:
            {{- with $.Values.generic.podAnnotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 12 }}{{- end }}
            {{- with .podAnnotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 12 }}{{- end }}
        spec:
          {{- include "helpers.pod" (dict "value" . "general" $general "name" $name "context" $) | indent 10 }}
          restartPolicy: {{ .restartPolicy | default "Never" }}
{{- if .commandDurationAlert }}
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cronjob-{{ $cjName }}
  labels:
    prometheus: k8s
    role: alert-rules
  namespace: nxs-monitoring
spec:
  groups:
  - name: "cronJobs_rules"
    interval: 1m # period check for alerts
    rules:
    - alert: "cronjob-{{ $cjName }}-too-long-execution"
      expr: '((time() - kube_job_status_start_time{namespace="{{ $.Release.Namespace }}", job_name=~"{{ $cjName }}-.*"}) and kube_job_status_active{namespace="{{ $.Release.Namespace }}", job_name=~"{{ $cjName }}-.*"} == 1) > {{ .commandDurationAlert }}'
      for: 3m
      labels:
        severity: warning
      annotations:
        message: "CronJob {{ $.Release.Namespace }}/{{ $cjName }} is taking more than {{ .commandDurationAlert }} to complete"
{{- end }}
{{- end }}
{{- end }}
