{{- range $name, $s := $.Values.services }}
---
kind: Service
apiVersion: v1
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" $name "context" $) }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with .labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
  annotations:
    {{- with $.Values.generic.annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
    {{- with .annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
spec:
  {{- if not (empty .clusterIP ) }}
  clusterIP: {{ .clusterIP }}
  {{- end }}
  {{- if not (empty .type) }}
  type: {{ .type }}
  {{- if eq .type "LoadBalancer" }}
  {{- if not (empty .loadBalancerIP) }}
  loadBalancerIP: {{ .loadBalancerIP }}
  {{- end }}
  {{- if .loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
    {{- toYaml .loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  {{- else if (eq .type "NodePort") }}
  {{- if empty .externalTrafficPolicy }}
  externalTrafficPolicy: "Cluster"
  {{- else }}
  externalTrafficPolicy: {{ .externalTrafficPolicy }}
  {{- end }}
  {{- if not (empty .healthCheckNodePort) }}
  healthCheckNodePort: {{ .healthCheckNodePort }}
  {{- end }}
  {{- end }}
  {{- if .externalIPs }}
  externalIPs: {{- toYaml .externalIPs | nindent 4 }}
  {{- end }}
  {{- end }}
  ports:
  {{- range .ports }}
  - name: {{ .name | default $name}}
    protocol: {{ .protocol | default "TCP"}}
    port: {{ .port }}
    {{- if not (empty .targetPort) }}
    targetPort: {{ .targetPort }}
    {{- end }}
    {{- if not (empty .nodePort) }}
    nodePort: {{ .nodePort }}
    {{- end }}
  {{- end }}
  selector:
    {{- include "helpers.app.selectorLabels" $ | nindent 4 }}
    {{- with .extraSelectorLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
{{- end }}
