{{- range $name, $sm := .Values.serviceMonitors }}
---
kind: ServiceMonitor
apiVersion: monitoring.coreos.com/v1
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" $name "context" $) }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with .labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
spec:
  endpoints:
    {{- include "helpers.tplvalues.render" (dict "value" .endpoints "context" $) | nindent 4 }}
  selector:
    matchLabels:
      {{- include "helpers.app.selectorLabels" $ | nindent 6 }}
      {{- with $.Values.generic.extraSelectorLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 6 }}{{- end -}}
      {{- with .extraSelectorLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 6 }}{{- end -}}
{{- end }}

{{- range $name, $sm := .Values.servicemonitors }}
---
kind: ServiceMonitor
apiVersion: monitoring.coreos.com/v1
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" $name "context" $) }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with .labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
spec:
  endpoints:
    {{- include "helpers.tplvalues.render" (dict "value" .endpoints "context" $) | nindent 4 }}
  selector:
    matchLabels:
      {{- include "helpers.app.selectorLabels" $ | nindent 6 }}
      {{- with $.Values.generic.extraSelectorLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 6 }}{{- end -}}
      {{- with .extraSelectorLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 6 }}{{- end -}}
{{- end }}
