{{- if .Values.hooks -}}
{{- $general := $.Values.hooksGeneral -}}
{{- $hooks := list -}}
{{- if kindIs "string" .Values.hooks -}}
{{- $hooks = fromYaml .Values.hooks -}}
{{- else if kindIs "map" .Values.hooks -}}
{{- $hooks = .Values.hooks -}}
{{- end -}}
{{- range $name, $hook := $hooks }}
{{- $hookName := include "helpers.app.fullname" (dict "name" $name "context" $) }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $hookName }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with $general.labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
    {{- with .labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
  annotations:
    "helm.sh/hook": {{ .kind | default "pre-install,pre-upgrade" | quote }}
    "helm.sh/hook-weight": {{ .weight | default "5" | quote }}
    "helm.sh/hook-delete-policy": {{ .deletePolicy | default "before-hook-creation" | quote }}
    {{- include "helpers.app.genericAnnotations" $ | indent 4 }}
    {{- with $general.annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
    {{- with .annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
spec:
  {{- if .parallelism }}
  parallelism: {{ .parallelism }}
  {{- else if $general.parallelism }}
  parallelism: {{ $general.parallelism }}
  {{- end }}
  {{- if .completions }}
  completions: {{ .completions }}
  {{- else if $general.completions }}
  completions: {{ $general.completions }}
  {{- end }}
  {{- if .activeDeadlineSeconds }}
  activeDeadlineSeconds: {{ .activeDeadlineSeconds }}
  {{- else if $general.activeDeadlineSeconds }}
  activeDeadlineSeconds: {{ $general.activeDeadlineSeconds }}
  {{- end }}
  {{- if .backoffLimit }}
  backoffLimit: {{ .backoffLimit }}
  {{- else if $general.backoffLimit }}
  backoffLimit: {{ $general.backoffLimit }}
  {{- end }}
  {{- if .ttlSecondsAfterFinished }}
  ttlSecondsAfterFinished: {{ .ttlSecondsAfterFinished }}
  {{- else if $general.ttlSecondsAfterFinished }}
  ttlSecondsAfterFinished: {{ $general.ttlSecondsAfterFinished }}
  {{- end }}
  template:
    metadata:
      name: {{ $hookName }}
      labels:
        {{- with $.Values.generic.podLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
        {{- with .podLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
      annotations:
        {{- with $.Values.generic.podAnnotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
        {{- with .podAnnotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
    spec:
      {{- include "helpers.pod" (dict "value" . "general" $general "name" $name "context" $) | indent 6 }}
      restartPolicy: {{ .restartPolicy | default "Never" }}
{{- end -}}
{{- end -}}