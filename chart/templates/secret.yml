{{- if or (not (empty .Values.secretEnvs)) (not (empty .Values.secretEnvsString)) }}
---
kind: Secret
apiVersion: v1
type: Opaque
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" "secret-envs" "context" $) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "helpers.app.labels" $ | nindent 4 }}
  annotations: {{- include "helpers.app.hooksAnnotations" $ | nindent 4 }}
data:
  {{- include "helpers.secrets.render" (dict "value" .Values.secretEnvsString) | indent 2 }}
  {{- include "helpers.secrets.render" (dict "value" .Values.secretEnvs) | indent 2 }}
{{- end }}

{{- range $sName, $val := .Values.secrets -}}
{{- if not (eq $sName "secret-envs") }}
---
apiVersion: v1
kind: Secret
type: {{ $val.type | default  "Opaque" }}
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" $sName "context" $) }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with $val.labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{ end }}
  annotations:
    {{- include "helpers.app.hooksAnnotations" $ | nindent 4 }}
    {{- with $val.annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{ end }}
data:
  {{- include "helpers.secrets.render" (dict "value" $val.data) | indent 2 }}
{{- end }}
{{- end }}

{{- range $name, $value := .Values.imagePullSecrets }}
---
apiVersion: v1
kind: Secret
type: kubernetes.io/dockerconfigjson
metadata:
  name: {{ $name }}
  namespace: {{ $.Release.Namespace | quote }}
  labels: {{- include "helpers.app.labels" $ | nindent 4 }}
  annotations: {{- include "helpers.app.hooksAnnotations" $ | nindent 4 }}
data:
  {{- include "helpers.secrets.render" (dict "value" (printf ".dockerconfigjson: %v" $value)) | indent 2 }}
{{- end }}
