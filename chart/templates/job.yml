{{- if .Values.jobs -}}
{{- $general := $.Values.jobsGeneral -}}
{{- $jobs := list -}}
{{- if kindIs "string" .Values.jobs -}}
{{- $jobs = fromYaml .Values.jobs -}}
{{- else if kindIs "map" .Values.jobs -}}
{{- $jobs = .Values.jobs -}}
{{- end -}}
{{- range $name, $job := $jobs }}
{{- $jobName := include "helpers.app.fullname" (dict "name" $name "context" $) }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $jobName }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with .labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
  annotations:
    {{- include "helpers.app.genericAnnotations" $ | indent 4 }}
    {{- with .annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
spec:
  {{- if .parallelism }}
  parallelism: {{ .parallelism }}
  {{- else if $general.parallelism }}
  parallelism: {{ $general.parallelism }}
  {{- end }}
  {{- if .completions }}
  completions: {{ .completions }}
  {{- else if $general.completions }}
  completions: {{ $general.completions }}
  {{- end }}
  {{- if .activeDeadlineSeconds }}
  activeDeadlineSeconds: {{ .activeDeadlineSeconds }}
  {{- else if $general.activeDeadlineSeconds }}
  activeDeadlineSeconds: {{ $general.activeDeadlineSeconds }}
  {{- end }}
  {{- if .backoffLimit }}
  backoffLimit: {{ .backoffLimit }}
  {{- else if $general.backoffLimit }}
  backoffLimit: {{ $general.backoffLimit }}
  {{- end }}
  {{- if .ttlSecondsAfterFinished }}
  ttlSecondsAfterFinished: {{ .ttlSecondsAfterFinished }}
  {{- else if $general.ttlSecondsAfterFinished }}
  ttlSecondsAfterFinished: {{ $general.ttlSecondsAfterFinished }}
  {{- end }}
  template:
    metadata:
      labels:
        {{- with $.Values.generic.podLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
        {{- with .podLabels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
      annotations:
        {{- with $.Values.generic.podAnnotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
        {{- with .podAnnotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 8 }}{{- end }}
    spec:
      {{- include "helpers.pod" (dict "value" . "general" $general "name" $name "context" $) | indent 6 }}
      restartPolicy: {{ .restartPolicy | default "Never" }}
{{- if .commandDurationAlert }}
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: job-{{ $jobName }}
  labels:
    prometheus: k8s
    role: alert-rules
  namespace: nxs-monitoring
spec:
  groups:
  - name: "jobs_rules"
    interval: 1m # period check for alerts
    rules:
    - alert: "job-{{ $jobName }}-too-long-execution"
      expr: '((time() - kube_job_status_start_time{namespace="{{ $.Release.Namespace }}", job_name=~"{{ $jobName }}-.*"}) and kube_job_status_active{namespace="{{ $.Release.Namespace }}", job_name=~"{{ $jobName }}-.*"} == 1) > {{ .commandDurationAlert }}'
      for: 3m
      labels:
        severity: warning
      annotations:
        message: "Job {{ $.Release.Namespace }}/{{ $jobName }} is taking more than {{ .commandDurationAlert }} to complete"
{{- end }}
{{- end }}
{{- end }}
