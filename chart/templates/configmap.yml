{{- if or (not (empty .Values.envs)) (not (empty .Values.envsString)) }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" "envs" "context" $) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "helpers.app.labels" $ | nindent 4 }}
  annotations: {{- include "helpers.app.hooksAnnotations" $ | nindent 4 }}
data:
  {{- include "helpers.configmaps.renderConfigMap" (dict "value" .Values.envs) | indent 2 }}
  {{- include "helpers.configmaps.renderConfigMap" (dict "value" .Values.envsString) | indent 2 }}
{{- end }}

{{- range $cName, $val := .Values.configMaps -}}
{{- if not (eq $cName "envs") }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" $cName "context" $) }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with $val.labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{ end }}
  annotations:
    {{- include "helpers.app.hooksAnnotations" $ | nindent 4 }}
    {{- with $val.annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{ end }}
data:
  {{- include "helpers.tplvalues.render" ( dict "value" $val.data "context" $ ) | nindent 2 }}
{{- end -}}
{{- end -}}
