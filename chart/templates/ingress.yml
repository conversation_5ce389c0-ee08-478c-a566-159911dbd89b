{{- range $host, $ing := .Values.ingresses }}
---
apiVersion: {{ include "helpers.capabilities.ingress.apiVersion" $ }}
kind: Ingress
metadata:
  name: {{ include "helpers.app.fullname" (dict "name" ($ing.name | default $host) "context" $) }}
  namespace: {{ $.Release.Namespace | quote }}
  labels:
    {{- include "helpers.app.labels" $ | nindent 4 }}
    {{- with $ing.labels }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
  annotations:
    {{- include "helpers.app.genericAnnotations" $ | indent 4 }}
    {{- with $ing.certManager }}
    kubernetes.io/tls-acme: "true"
    {{- if .issuerName }}
    cert-manager.io/{{ .issuerType | default "cluster-issuer" }}: {{ .issuerName }}
    {{- end }}
    {{- end }}
    {{- with $ing.annotations }}{{- include "helpers.tplvalues.render" (dict "value" . "context" $) | nindent 4 }}{{- end }}
spec:
  {{- if and (eq "true" (include "helpers.ingress.supportsIngressClass" $)) ($ing.ingressClassName) }}
  ingressClassName: {{ $ing.ingressClassName }}
  {{- end }}
  rules:
  {{- range $ing.hosts }}
  - {{ if .hostname -}}
    host: {{ include "helpers.tplvalues.render" (dict "value" .hostname "context" $) }}
    {{- else -}}
    host: {{ $host }}
    {{- end }}
    http:
      paths:
      {{- range .paths }}
      - path: {{ default "/" .path }}
        {{- if eq "true" (include "helpers.ingress.supportsPathType" $) }}
        pathType: {{ default "Prefix" .pathType }}
        {{- end }}
        backend:
          {{- include "helpers.ingress.backend" (dict "serviceName" (include "helpers.app.fullname" (dict "name" .serviceName "context" $)) "servicePort" .servicePort "context" $) | nindent 10 }}
      {{- end }}
  {{- end }}
  {{- if or $ing.certManager $ing.extraTls }}
  tls:
  {{- if $ing.certManager }}
  - hosts:
    {{- range $ing.hosts }}
    {{- if .hostname }}
    - {{ include "helpers.tplvalues.render" (dict "value" .hostname "context" $) }}
    {{- else }}
    - {{ $host }}
    {{- end }}
    {{- end }}
    secretName: {{ .tlsName | default (include "helpers.app.fullname" (dict "name" ($ing.name | default $host) "context" $)) }}-tls
  {{- end }}
  {{- if .extraTls }}
  {{- include "helpers.tplvalues.render" ( dict "value" .extraTls "context" $ ) | nindent 2 }}
  {{- end }}
  {{- end }}
{{- end -}}
