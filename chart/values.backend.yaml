generic:
  extraVolumes: []
  usePredefinedAffinity: true

podAffinityPreset: soft

podAntiAffinityPreset: soft

nodeAffinityPreset:
  type: ""
  key: ""
  values: []

releasePrefix: "-"

diagnosticMode:
  enabled: false
  command: ["sleep"]
  args: ["infinity"]

defaultImage: ""
defaultImageTag: "latest"
defaultImagePullPolicy: "IfNotPresent"
ingressHostname: ""

ingresses:
  thg-fp-backend:
    ingressClassName: alb
    annotations:
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/healthcheck-path: /v1/health
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
      external-dns.alpha.kubernetes.io/hostname: '{{ $.Values.ingressHostname }}'
    hosts:
      - hostname: '{{ $.Values.ingressHostname }}'
        paths:
          - serviceName: thg-fp-backend
            servicePort: http
            path: /


services:
  thg-fp-backend:
    type: NodePort
    ports:
      - name: http
        protocol: TCP
        port: 80
        targetPort: 3000
        nodePort:
    extraSelectorLabels:
      app/name: thg-fp-backend

deployments:
  thg-fp-backend:
    podLabels:
      app/name: thg-fp-backend
    podAnnotations:
      checksum/upgrade-date: "Updated at {{ now | toString }}"
    replicas: 1
    containers:
      - name: thg-fp-backend
        imagePullPolicy: IfNotPresent
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        resources:
          limits:
            cpu: 500m
            memory: 1024Mi
          requests:
            cpu: 200m
            memory: 512Mi
#        livenessProbe:
#          httpGet:
#            path: /v1/health
#            port: http
#          initialDelaySeconds: 30
#          periodSeconds: 15
#          timeoutSeconds: 10
#          successThreshold: 1
#          failureThreshold: 5
#        readinessProbe:
#          httpGet:
#            path: /v1/health
#            port: http
#          initialDelaySeconds: 30
#          periodSeconds: 5
#          timeoutSeconds: 3
#          successThreshold: 1
#          failureThreshold: 3
        envSecrets:
          - backend-secret

secrets:
  backend-secret:
    data: