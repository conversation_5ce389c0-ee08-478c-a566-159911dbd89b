name: TechDocs 📚

on:
  push:
    branches:
      - main
      - 'docs/**'
    paths:
      - "docs/**"
      - "mkdocs.yaml"

jobs:
  env:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/env.yaml@release/add-default-pipelines

  docs:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/techdocs.yaml@release/add-default-pipelines
    secrets: inherit
    with:
      repository: ${{ needs.env.outputs.app }}
    needs:
      - env
