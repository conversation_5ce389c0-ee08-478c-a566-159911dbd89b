name: HotFix 🔥

on:
  push:
    branches:
      - 'hotfix/**'
      - 'bugfix/**'

jobs:
  env:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/env.yaml@release/add-default-pipelines

  tests:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/tests.yaml@release/add-default-pipelines
    needs:
      - env
    secrets: inherit

  sonar:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/sonar.yaml@release/add-default-pipelines
    with:
      app: ${{ needs.env.outputs.app }}
    secrets:
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    needs:
      - env

  build-and-push:
    strategy:
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/build-and-push.yaml@release/add-default-pipelines
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.env.outputs.tag }}
      environment: 'qa'
      dockerfile: ./docker/${{ matrix.svc }}/Dockerfile
    secrets:
      aws_access_key_id: ${{ secrets.GA_ACCESS_KEY }}
      aws_secret_access_key: ${{ secrets.GA_SECRET_KEY }}
      aws_region: ${{ secrets.AWS_REGION }}
      ecr_registry: ${{ secrets.ECR_REGISTRY }}
      ecr_registry_id: ${{ secrets.ECR_REGISTRY_ID }}
    needs:
      - env
      - tests
      - sonar

  deploy:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/deploy.yaml@release/add-default-pipelines
    strategy:
      max-parallel: 0
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.env.outputs.tag }}
      environment: 'qa'
    secrets:
      GITOPS_GITHUB_APPLICATION_PRIVATE_KEY: ${{ secrets.GITOPS_GITHUB_APPLICATION_PRIVATE_KEY }}
    needs:
      - env
      - build-and-push

  trivy-image-scan:
    strategy:
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/trivy-image.yaml@release/add-default-pipelines
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.env.outputs.tag }}
      environment: 'qa'
    secrets:
      aws_access_key_id: ${{ secrets.GA_ACCESS_KEY }}
      aws_secret_access_key: ${{ secrets.GA_SECRET_KEY }}
      aws_region: ${{ secrets.AWS_REGION }}
      ecr_registry: ${{ secrets.ECR_REGISTRY }}
    needs:
      - env
      - build-and-push
