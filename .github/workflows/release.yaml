name: Release 📦

on:
  pull_request:
    branches:
      - 'release/**'
    types:
      - closed
    paths-ignore:
      - '.github/**'
      - 'catalog-info.yaml'
      - 'Dockerfile'
      - 'docker-compose.yaml'
      - 'docs/**'
      - 'mkdocs.yaml'
      - 'README.md'

jobs:
  env:
    if: github.event.pull_request.merged == true
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/env.yaml@release/add-default-pipelines

  show-last-commit:
    if: github.event.pull_request.merged == true
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/show-last-commit.yaml@release/add-default-pipelines

  sonar:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/sonar.yaml@release/add-default-pipelines
    with:
      app: ${{ needs.env.outputs.app }}
    secrets:
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    needs:
      - env
      - show-last-commit

  build-and-push:
    strategy:
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/build-and-push.yaml@release/add-default-pipelines
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.show-last-commit.outputs.last-commit }}
      environment: 'qa'
      dockerfile: ./docker/${{ matrix.svc }}/Dockerfile
    secrets:
      aws_access_key_id: ${{ secrets.GA_ACCESS_KEY }}
      aws_secret_access_key: ${{ secrets.GA_SECRET_KEY }}
      aws_region: ${{ secrets.AWS_REGION }}
      ecr_registry: ${{ secrets.ECR_REGISTRY }}
      ecr_registry_id: ${{ secrets.ECR_REGISTRY_ID }}
    needs:
      - env
      - show-last-commit
      - sonar

  deploy:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/deploy.yaml@release/add-default-pipelines
    strategy:
      max-parallel: 0
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.show-last-commit.outputs.last-commit }}
      environment: 'qa'
    secrets:
      GITOPS_GITHUB_APPLICATION_PRIVATE_KEY: ${{ secrets.GITOPS_GITHUB_APPLICATION_PRIVATE_KEY }}
    needs:
      - env
      - show-last-commit
      - build-and-push

  trivy-image-scan:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/trivy-image.yaml@release/add-default-pipelines
    strategy:
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.show-last-commit.outputs.last-commit }}
      environment: 'qa'
    secrets:
      aws_access_key_id: ${{ secrets.GA_ACCESS_KEY }}
      aws_secret_access_key: ${{ secrets.GA_SECRET_KEY }}
      aws_region: ${{ secrets.AWS_REGION }}
      ecr_registry: ${{ secrets.ECR_REGISTRY }}
    needs:
      - env
      - show-last-commit
      - build-and-push
