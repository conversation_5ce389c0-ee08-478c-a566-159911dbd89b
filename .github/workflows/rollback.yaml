name: Rollback 🔙

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Set environment'
        required: true
        type: choice
        options:
          - development
          - qa
          - production
      tag:
        description: "Set Docker Image Tag to RollBack"
        required: true


jobs:
  env:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/env.yaml@release/add-default-pipelines

  rollback:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/rollback.yaml@release/add-default-pipelines
    strategy:
      max-parallel: 0
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    secrets: inherit
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ inputs.tag }}
      environment: ${{ inputs.environment }}
    needs:
      - env
