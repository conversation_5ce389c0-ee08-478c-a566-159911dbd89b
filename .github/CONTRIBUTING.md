# Contributing to {{ repo_name }}

To ensure smooth collaboration and alignment with our processes, please follow the guidelines below.

## Issue Management (Jira)

We use **Jira** for project and issue management. Please ensure that each branch is associated with a Jira issue. You can reference the Jira issue number in your branch name and commit messages.

- **Jira Issue Tracker**: Make sure to track your work under the appropriate Jira ticket.
- Link Jira issues in your pull requests by adding `closes JIRA-ISSUE-NUMBER` in the description.

## Branching Strategy

We follow a simplified **Gitflow** model for branch management. Please use the following branch naming convention:

`{type}/{JIRA-issue-number}/{short-description}`

- **Types**:

  - `feature`: New features or enhancements.
  - `bugfix`: Fixes for bugs.
  - `hotfix`: Urgent fixes for critical issues.
  - `chore`: Maintenance tasks (e.g., dependency updates).

- **Examples**:
  - `feature/JIRA-123/add-schema-validation`
  - `bugfix/JIRA-456/fix-schema-parsing`

## Commit Messages

We follow the **Conventional Commits** standard for commit messages, with Jira references included.

`{type}: {short description} (JIRA-issue-number)`

**Types**:

- `feat`: A new feature.
- `fix`: A bug fix.
- `docs`: Documentation changes.
- `style`: Code formatting (no functional changes).
- `refactor`: Code restructuring without changing functionality.
- `test`: Adding or updating tests.
- `chore`: Routine maintenance tasks.

**Example**:

- `feat: implement schema validation for VC issuance (JIRA-123)`
- `fix: resolve null pointer exception in issuance flow (JIRA-456)`

## Pull Requests (PRs)

Pull requests are essential for collaborative development. Please follow these guidelines when submitting a PR:

### PR Guidelines

1. **One Issue per PR**: Each pull request should address a single Jira issue.
2. **Descriptive PR Titles and Descriptions**: Clearly describe the changes and reference the Jira issue (`closes JIRA-123`).
3. **Testing**: Ensure your changes pass all tests and add new tests for any new functionality.
4. **Reviews**: All PRs require **two approvals** before merging.

### PR Workflow

1. Create your branch from `develop` following the branch naming convention.
2. Work on your changes, commit, and push to your branch.
3. Open a PR against `develop`, ensuring all checks pass.
4. Request reviews and ensure the code follows the Jira issue's requirements.

## Merge Strategy

We use **Squash and Merge** for all pull requests. This condenses all commits into a single commit upon merging, keeping the repository’s history clean.

- **Who Can Merge?**: Only team members with write access can merge PRs once approved.

## Deleting Branches

Once your PR has been merged, delete the branch to keep the repository clean. You can enable automatic branch deletion in GitHub settings.

## Code Reviews

Code reviews are crucial for maintaining code quality. Every PR must be reviewed and approved by at least two team members before merging.

### Review Focus

- Code correctness and readability.
- Test coverage.
- Adherence to project architecture and standards.

## Continuous Integration (CI)

We use **GitHub Actions** for continuous integration. CI will automatically:

- Run unit tests.
- Run integration tests.
- Lint your code.

### Before submitting a PR

1. Run all tests locally.
2. Verify that your code conforms to the project’s linting standards.
3. Add or update tests if you're introducing new functionality.

## Repository Structure

Our repository is structured for maintainability and scalability. Please follow the structure when contributing:

- **Source Code**: Add all new features to `src/`.
- **Documentation**: Update `docs/` for any changes to functionality or architecture.
- **Tests**: All tests should go under the `test/` directory. Ensure that unit tests and integration tests cover new code.

## Versioning

We follow **Semantic Versioning** (`Major.Minor.Patch`). Contributions should respect the versioning system:

- **Major**: Breaking changes.
- **Minor**: Backward-compatible new features.
- **Patch**: Backward-compatible bug fixes.

## Security Considerations

Ensure that no sensitive information (e.g., keys or credentials) is included in the repository. If you're introducing third-party libraries, ensure they are well-vetted and maintained.

---

Thank you for contributing to the **{{ repo_name }}** project! Following these guidelines helps us maintain a high-quality, secure, and well-organized codebase.
