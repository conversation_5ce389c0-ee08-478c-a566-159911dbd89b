// @ts-check
import eslint from '@eslint/js';
import prettierConfig from 'eslint-config-prettier';
import tseslint from 'typescript-eslint';

export default tseslint.config(eslint.configs.recommended, tseslint.configs.recommended, prettierConfig, {
  languageOptions: {
    parserOptions: {
      projectService: true,
      // eslint-disable-next-line no-undef
      tsconfigRootDir: process.cwd(),
    },
  },
  ignores: ['/node_modules/**', '/dist/**', '/build/**', '/test/**'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
  },
});
