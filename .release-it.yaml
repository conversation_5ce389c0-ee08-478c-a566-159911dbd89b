---
github:
  release: true
  releaseName: "v${version}"
  tokenRef: GITHUB_TOKEN

git:
  addUntrackedFiles: false
  changelog: npx auto-changelog --stdout --commit-limit false #git log --pretty=format:"* %s (%h)" ${from}...${to}
  commit: true
  commitArgs: []
  commitMessage: "release: Release version v${version} 📦"
  push: true
  requireBranch: main
  requireCleanWorkingDir: true
  requireCommits: true
  requireUpstream: true
  tag: true
  tagAnnotation: "Release version v${version}"
  tagArgs: []
  tagName: "v${version}"
  pushArgs:
    - "--follow-tags"
  pushRepo: ""

npm:
  publish: false

plugins:
  "@release-it/conventional-changelog":
    infile: CHANGELOG.md
    preset:
      name: conventionalcommits
      types:
        - type: feat
          section: Features
        - type: fix
          section: Bug fixes
        - type: refactor
          section: Refactoring
        - type: test
          section: Tests
        - type: docs
          section: Documentation
        - type: chore
          section: Chores
