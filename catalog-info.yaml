apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: funding-platform-api
  description: Grant Management Platform App
  annotations:
    argocd/app-selector: "app.kubernetes.io/name=funding-platform-api"
    backstage.io/kubernetes-id: funding-platform-api
    backstage.io/techdocs-ref: dir:.    
    github.com/project-slug: Swiss-Digital-Assets-Institute/funding-platform-api
    grafana/alert-label-selector: "namespace=funding-platform-api"
    grafana/tag-selector: "nodejs"    
    prometheus.io/rule: memUsage|component,node_memory_active_bytes|instance,sum by (instance) (node_cpu_seconds_total)
    prometheus.io/alert: all
    prometheus.io/service-name: prometheusOPS
    sonarqube.org/project-key: default/funding-platform-api
    vault.io/secrets-path: "tha-dev-che-eks/dev/funding-platform"
  tags:
    - web
    - frontend
  links:
    - url: https://funding-platform-api.pub.dev2.hashgraph-group.com/
      title: Development
      icon: cloud
    - url: https://funding-platform-api.pub.qa.hashgraph-group.com/
      title: QA
      icon: cloud      
spec:
  type: service
  lifecycle: development
  owner: group:axlabs
  system: funding-platform
  dependsOn: ['resource:funding-platform-api-db']
  apiConsumedBy: ['component:funding-platform-app']  
  providesApis:
    - funding-platform
