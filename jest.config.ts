import { createDefaultPreset, type JestConfigWithTsJest } from 'ts-jest';

const presetConfig = createDefaultPreset({});

const jestConfig: JestConfigWithTsJest = {
  ...presetConfig,
  rootDir: './src',
  collectCoverageFrom: ['**/*.{!(module|config),}.(t|j)s', '!**/migrations/*'],
  coverageDirectory: '../coverage',
  coverageThreshold: {
    global: {
      lines: 60, //lowered from 70 to 60 till get more tests for votes
    },
  },
};

export default jestConfig;
